const ip = 'http://*************'
const gurl = `${ip}:9090` // 调试端口

const videoUrl = `http://***************:8889/`

window.g = {
  api: gurl,
  wsApi: gurl.replace('http:','ws:'),
  baseUrl: gurl,
  videoUrl:videoUrl,
  systemMonitor_url: `${ip}/portainer/`, // 系统监控地址
  SRSMonitor_url: `${ip}/srs-server/console/ng_index.html#/streams?port=1985`, // SRS监控地址
  nodered_url: `${ip}/nodered/`, // Node-RED地址
  plat_type: 0, // 是否集成底座,0为不集成,1为集成
  // sso_api_url: 'http://***************:6200', // hiaplant和xmigital都有效 校验accessToken的有效性，防止跳转到子系统时异常退出
};