import { createApp } from "vue";
import Antd from "ant-design-vue";
import "ant-design-vue/dist/reset.css";
import ElementPlus from "element-plus";
import CIconFont from "@/components/c-iconfont";
import "element-plus/dist/index.css";
import mavonEditor from "mavon-editor";
import "mavon-editor/dist/css/index.css";
import App from "./App.vue";
import router from "./router";
import { store } from "./store";
import "@/common/theme/theme-settings";
import { message } from "ant-design-vue";

// 定义特性标志
(window as any).__VUE_PROD_DEVTOOLS__ = false;
(window as any).__VUE_PROD_HYDRATION_MISMATCH_DETAILS__ = false;

const initapp = async () => {
  // 向后台请求是否为单点登录模式 并把结果存入localStorage
  await fetch(`${(window as any).g.api}/common-config/loginMode`).then(async (res) => {
    const resJson = await res.json();
    if (resJson.code === 0) {
      window.localStorage.setItem("plantMode", JSON.stringify(resJson.data));
    }
  });
  if (window.localStorage.getItem("plantMode") === "true") {
    await fetch(`${(window as any).g.api}/api/hsm/auth/sso-config`).then(async (res) => {
      const resJson = await res.json();
      if (resJson.code === 0) {
        (window as any).clientId = resJson.data.clientId;
        (window as any).g.sso_api_url = resJson.data.platformUrl;
        (window as any).g.plat_type = resJson.data.enable ? 1 : 0;
      } else {
        // 接口获取失败则提示信息
        message.error(resJson.msg);
      }
    });
  }

  const app = createApp(App as any);

  app.use(store).use(router).use(Antd).use(mavonEditor).use(ElementPlus);
  app.component("CIconFont", CIconFont);

  app.mount("#app");
};

initapp();
