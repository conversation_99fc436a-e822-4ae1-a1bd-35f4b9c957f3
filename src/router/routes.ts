import useTitle from "@/common/use-title";
import SysLayout from "@/layout/SysLayout.vue";
import SysLayoutProject from "@/layout/SysLayoutProject.vue";
import ProjectDataLayout from "@/layout/ProjectDataLayout.vue";
import ProjectMonitor from "@/layout/ProjectMonitor.vue";
import ProjectModel from "@/layout/ProjectModel.vue";
import ProjectLog from "@/layout/projectLog.vue";
import CError404 from "@/components/c-error404";

const appTitle = "智能视觉分析与监控系统";
useTitle(appTitle);

const routes = [
  {
    path: "/",
    name: "/",
    redirect: "/design",
  },
  {
    path: "/clientLogin",
    name: "clientLogin",
    component: () => import("@/views/clientLogin/login.vue"),
  },
  {
    path: "/authorization",
    name: "authorization",
    component: () => import("@/views/authorization/index.vue"),
  },
  {
    path: "/sys",
    name: "sys",
    component: SysLayout,
    children: [
      {
        path: "/homePage",
        name: "homePage",
        component: () => import("@/views/homePage/index.vue"),
      },
      {
        path: "/splitScreen",
        name: "splitScreen",
        component: () => import("@/views/splitScreen/index.vue"),
      },
      {
        path: "/configPage",
        name: "configPage",
        component: () => import("@/views/configPage/index.vue"),
      },
      {
        path: "/historyAlarm",
        name: "historyAlarm",
        component: () => import("@/views/historyAlarm/index.vue"),
      },
      {
        path: "/map",
        name: "map",
        component: () => import("@/views/map/index.vue"),
      },
      {
        path: "/inspectionRecords",
        name: "inspectionRecords",
        component: () => import("@/views/inspectionRecords/index.vue"),
      },
    ],
  },
  {
    path: "/project",
    name: "project",
    component: SysLayoutProject,
    children: [
      {
        path: "/design",
        name: "project_design",
        component: () => import("@/views/projectPage/design/designPage.vue"),
      },
      {
        path: "/conf",
        name: "conf",
        component: ProjectDataLayout,
        children: [
          {
            path: "/conf_pointItemConf",
            name: "conf_pointItemConf",
            component: () => import("@/views/projectPage/configure/pointConf/index.vue"),
          },
          {
            path: "/conf_schedulingConf",
            name: "conf_schedulingConf",
            component: () => import("@/views/projectPage/configure/scheduling/index.vue"),
          },
          {
            path: "/conf_alarmConf",
            name: "conf_alarmConf",
            component: () => import("@/views/projectPage/configure/alarmConf/index.vue"),
          },
          {
            path: "/conf_controlFlowConf",
            name: "conf_controlFlowConf",
            component: () => import("@/views/projectPage/configure/controlFlow/index.vue"),
          },
          {
            path: "/conf_interfaceConf",
            name: "conf_interfaceConf",
            component: () => import("@/views/projectPage/configure/interFace/index.vue"),
          },
          {
            path: "/conf_faultLibrary",
            name: "conf_faultLibrary",
            component: () => import("@/views/projectPage/configure/faultLibrary/index.vue"),
          },
          {
            path: "/conf_system",
            name: "conf_system",
            component: () => import("@/views/projectPage/configure/system/index.vue"),
          },
          {
            path: "/conf_JSON",
            name: "conf_JSON",
            component: () => import("@/views/projectPage/configure/channelJson/index.vue"),
          },
        ],
      },
      {
        path: "/model",
        name: "project_model",
        component: ProjectModel,
        children: [
          {
            path: "/algorithm_manage",
            name: "algorithm_manage",
            component: () => import("@/views/projectPage/model/algorithmManage/index.vue"),
          },
          {
            path: "/model_manage",
            name: "model_manage",
            component: () => import("@/views/projectPage/model/modelManage/index.vue"),
          },
        ],
      },
      {
        path: "/monitor",
        name: "monitor",
        component: ProjectMonitor,
        children: [
          {
            path: "/monitor_system",
            name: "monitor_system",
            component: () => import("@/views/projectPage/monitor/components/systemMonitor.vue"),
          },
          {
            path: "/monitor_service",
            name: "monitor_service",
            component: () => import("@/views/Empty.vue"),
          },
          {
            path: "/monitor_scheduling",
            name: "monitor_scheduling",
            component: () => import("@/views/Empty.vue"),
          },
          {
            path: "/monitor_point",
            name: "monitor_point",
            component: () => import("@/views/projectPage/monitor/components/pointMonitor.vue"),
          },
          {
            path: "/monitor_alarm",
            name: "monitor_alarm",
            component: () => import("@/views/Empty.vue"),
          },
          {
            path: "/monitor_SRS",
            name: "monitor_SRS",
            component: () => import("@/views/projectPage/monitor/components/SRSMonitor.vue"),
          },
        ],
      },
      {
        path: "/log",
        name: "log",
        component: ProjectLog,
        children: [
          {
            path: "/service_log",
            name: "service_log",
            component: () => import("@/views/Empty.vue"),
          },
          {
            path: "/operation_log",
            name: "operation_log",
            component: () => import("@/views/projectPage/log/operetionLog.vue"),
          },
        ],
      },
      {
        path: "/project_help",
        name: "project_help",
        component: () => import("@/views/Empty.vue"),
      },
    ],
  },

  {
    path: "/404",
    name: "404",
    component: CError404,
  },
  {
    path: "/:catchAll(.*)",
    redirect: "/404",
  },
  {
    path: "/designEditor",
    name: "designEditor",
    component: () => import("@/views/designEditor/index.vue"),
  },
  {
    path: "/test",
    name: "test",
    component: () => import("@/views/test.vue"),
  },
  // {
  //   path: "/previewVideo",
  //   name: "previewVideo",
  //   component: () => import("@/views/previewVideo/index.vue"),
  // },
  {
    path: "/DCSPopup",
    name: "DCSPopup",
    component: () => import("@/views/dcsSplitScreen/index.vue"),
  },
  {
    path: "/DCS",
    name: "DCS",
    component: () => import("@/views/dcs/index.vue"),
  },
  {
    path: "/DCSChannelView",
    name: "DCSChannelView",
    component: () => import("@/views/dcs/alarmDcs.vue"),
  },
  {
    path: "/login",
    name: "login",
    component: () => import("@/views/clientLogin/login.vue"),
  },
  {
    path: "/videoAlarm",
    name: "videoAlarm",
    component: () => import("@/views/dcs/alarmListForThis.vue"),
  },
  {
    path: "/DCSvideoAlarm",
    name: "DCSvideoAlarm",
    component: () => import("@/views/dcs/alarmListForThis.vue"),
  },
  {
    path: "/DCSDeployView",
    name: "DCSDeployView",
    component: () => import("@/views/map/dcsIndex.vue"),
  },
  {
    path: "/mesInsp",
    name: "mesInsp",
    component: () => import("@/views/mesInspection/mesPage.vue"),
  },

  {
    path: "/logo",
    name: "logo",
    component: () => import("@/views/download/index.vue"),
  },

  {
    path: "/systemPreset",
    name: "systemPreset",
    component: () => import("@/views/systemPresetPage/index.vue"),
  },

  {
    path: "/databasePreset",
    name: "databasePreset",
    component: () => import("@/views/databasePreset/index.vue"),
  },
];

export default routes;
