import { createRouter, createWebHashHistory, createWeb<PERSON>istory, useRouter } from "vue-router";
import { loginStatus, getUserName } from "@/api/project";
import routes from "./routes";

import OAuth2 from "@/common/oauth/OAuth2";

const router = createRouter({
  history: createWebHistory(),
  routes: routes as any,
});

let isCheckLogin = null;

const homePage = [
  "/homePage",
  "/splitScreen",
  "/configPage",
  "/historyAlarm",
  "/map",
  "/inspectionRecords",
  "/mesInsp",
];

const checkLogin = async (to, next) => {
  const toName = to.name;
  if (toName === "login") {
    if (isCheckLogin) {
      next();
      return;
    }
    if (!window.localStorage.getItem("hollysysIntelligent")) {
      next();
      return;
    }
    const res = await loginStatus();
    if (res.data === "true") {
      next("/design");
    }
    if (res.data === "false") {
      isCheckLogin = true;
      next();
    }
    return;
  }
  const homePage = [
    "/homePage",
    "/splitScreen",
    "/configPage",
    "/historyAlarm",
    "/map",
    "/inspectionRecords",
  ];
  if (!(to.fullPath.includes("/DCS") || to.fullPath.includes("/videoAlarm"))) {
    if (homePage.findIndex((item) => to.fullPath.includes(item)) > -1) {
      next();
      return;
    }
    loginStatus().then((res) => {
      if (res.data === "true") {
        next();
      }
      if (res.data === "false" && window.localStorage.getItem("plantMode") === "false" && !to.fullPath.includes("/authorization")) {
        // 只在非单点登录模式下重定向
        window.location.href = "/login";
      }
    });
  } else {
    next();
  }
};
router.beforeEach(async (to, from, next) => {
  if (to.fullPath.includes("/DCSvideoAlarm") || to.fullPath.includes("/DCSChannelView")) {
    (window as any).isDcs = true;
    next();
    return;
  }
  // 如果是往授权页面跳 则不需要检查登录状态 防止跳到登录页
  if (to.fullPath.includes("/authorization")) {
    next();
    return;
  }
  if (homePage.findIndex((item) => to.fullPath.includes(item)) > -1) {
    next();
    return;
  }
  if (
    window.localStorage.getItem("plantMode") === "true" &&
    !window.localStorage.getItem("ssoUser") &&
    to.fullPath.includes("/design")
  ) {
    // 调接口判断是否应该跳到单点登录配置页面
    await fetch(`${(window as any).g.api}/common-config/dbase`).then(async (res) => {
      const resJson = await res.json();
      if (resJson.code !== 0) {
        next("databasePreset");
        return;
      } else {
        next();
      }
    });
  } else {
    next();
  }
  if ((window as any).g.plat_type === 0) {
    checkLogin(to, next);
  } else {
    (window as any).isDcs = false;
    if ((window as any).g.plat_type === 1) {
      let webTokenUrl = "/design";
      await fetch(`${(window as any).g.api}/common-config/first-config`).then(async (res) => {
        const resJson = await res.json();
        if (resJson.data) {
          webTokenUrl = "/systemPreset"
        }
      });
      OAuth2.initialize({
        clientId: (window as any).clientId,
        authHost: (window as any).g.sso_api_url,
        apiHost: (window as any).g.api,
        webTokenUrl,
      });
    }
    const r = OAuth2.getInstance().getAccessToken();
    getUserName().then((res) => {
      if (res.code === 0) {
        window.localStorage.setItem("ssoUser", JSON.stringify(res.data));      }
    });

    if (!r) {
      next();
    }
  }

  // next();
});

/**
 * 路由守卫
 */

export default router;
