import lodash from "lodash";
// import cv from '@techstark/opencv-js';

export const PAGESIZE = 10;

export const colorMap = {
  default: "#d9d9d9",
  info: "#e6f7ff",
  success: "#f6ffed",
  warning: "#fffbe6",
  error: "#fff2f0",
};

export enum OPR_TYPE {
  ADD,
  PUB,
  UNPUB,
  START,
  STOP,
  PAUSE,
  RESUME,
  COPY,
}

export const runStatus = ["未启动", "已运行", "已暂停", "已停止"];
//     运行状态对应的操作：['启动','停止/挂起','停止/恢复','启动']（同时满足‘已发布’）
export const pubStatus = ["未发布", "已发布", "已撤销"];
//     发布状态对应的操作：['发布','撤销'(同时满足‘未启动/已停止’),'发布']
export const runStatusClass = ["default", "success", "warning", "error"];
export const pubStatusClass = ["default", "success", "error"];

export const arrTurnOptions = (arr: string[]) => {
  const options = arr.map((item, index) => ({
    label: item,
    value: index,
  }));
  return options;
};

export const listTurnOptions = (list: { id: string; name: string; [key: string]: any }[]) =>
  list.map((item) => ({
    label: item.name,
    value: item.id,
  }));

// 带函数的json序列化
export function JsonStrFun(obj: any, format = "") {
  const objstr = JSON.stringify(
    obj,
    (k, v) => {
      if (v instanceof Function) {
        return String(v);
      }
      return v;
    },
    format
  );
  return objstr;
}

// 带函数的json反序列化
export function JsonParseFun(objstr: string) {
  const objp = JSON.parse(objstr, (k, v) => {
    if (v && v.indexOf && v.indexOf("function") === 0) {
      return eval(`(function(){return ${v}  })`)();
    }
    return v;
  });

  return objp;
}

// js-json代码转实际对象
export function JsonCodeParse(jsonCode: string) {
  let resObj = {} as any;
  try {
    resObj = eval(`(${jsonCode})`);
  } catch (e) {
    console.error("JsonCodeParse error: ", jsonCode);
  }
  return resObj;
}

// 树的遍历
interface ITreeNode {
  children: ITreeNode[];
  [key: string]: any;
}
export function eachTreesNode(nodes: ITreeNode[], callFun?: (tempNode: ITreeNode) => void) {
  if (!nodes || nodes.length <= 0) {
    return;
  }

  for (let i = 0; i < nodes.length; i++) {
    const targetNode = nodes[i];
    if (callFun instanceof Function) {
      callFun(targetNode);
    }
    eachTreesNode(targetNode.children, callFun);
  }
}

// 分页删除后，返回可查询的页码
export function getPageNumAfterDelete(pageSize: number, total: number, deleteNum = 1) {
  const afterTotal = total - deleteNum;
  return Math.ceil(afterTotal / pageSize);
}

// 数组对比，返回增减项
export function getItemsByCompareArr(
  oldArr: (string | number)[],
  newArr: (string | number)[]
): {
  add: (string | number)[];
  remove: (string | number)[];
} {
  const res = {
    add: [],
    remove: [],
  };

  res.add = newArr.filter((item) => !oldArr.includes(item));

  res.remove = oldArr.filter((item) => !newArr.includes(item));

  return res;
}

// 移除Auth
export function removeAuth() {
  const count = localStorage.length;
  for (let index = 0; index < count; index++) {
    const key = localStorage.key(index);
    if (typeof key !== "undefined" && key !== null) {
      if (key.split(":")[0] === `${(window as any).authConfig.storagePrefix}user`) {
        localStorage.removeItem(key);
      }
    }
  }
}
// 生成n位随机数
export function randomString(n) {
  const str = "0123456789";
  let result = "";
  for (let index = n; index > 0; --index) {
    result += str[Math.floor(Math.random() * str.length)];
  }
  return result;
}

// 数组去重
export function unique(arr: Array<any>) {
  return lodash.uniqWith(arr, lodash.isEqual);
}
// 容错图片地址
export const errorImg = "";
// 处理图片

// 生成uuid
export function uuid2(len) {
  let chars = "0123456789abcdefghijklmnopqrstuvwxyz".split("");
  let uuid = [],
    i;
  const radix = chars.length;

  if (len) {
    // Compact form
    for (i = 0; i < len; i++) uuid[i] = chars[0 | (Math.random() * radix)];
  } else {
    // rfc4122, version 4 form
    let r;

    // rfc4122 requires these characters
    uuid[8] = uuid[13] = uuid[18] = uuid[23] = "-";
    uuid[14] = "4";

    // Fill in random data.  At i==19 set the high bits of clock sequence as
    // per rfc4122, sec. 4.1.5
    for (i = 0; i < 36; i++) {
      if (!uuid[i]) {
        r = 0 | (Math.random() * 16);
        uuid[i] = chars[i == 19 ? (r & 0x3) | 0x8 : r];
      }
    }
  }

  return uuid.join("");
}

/**
 * @description 16进制转RBG
 * @param {string} hex 16进制颜色
 */

export function hexToRgba(hex) {
  if (!hex) hex = "#ededed";
  const rgb = [
    parseInt("0x" + hex.slice(1, 3)),
    parseInt("0x" + hex.slice(3, 5)),
    parseInt("0x" + hex.slice(5, 7)),
  ];
  return rgb;
}
/**
 * @description RBGA进制转16
 * @param {string} rgb 16进制颜色
 */

export function RGBToHex(rgb) {
  let res = rgb; // 利用正则表达式去掉多余的部分，将rgb中的数字提取
  let hexRes = "#";
  let hex = ["0", "1", "2", "3", "4", "5", "6", "7", "8", "9", "A", "B", "C", "D", "E", "F"];
  let hexArr = [];
  for (let i = 0; i < res.length; i++) {
    if (res[i]) {
      if (res[i] > 16) {
        let leftIndex = (res[i] / 16) >> 0; // 向下取整
        let rightIndex = +res[i] % 16;
        hexArr.push(hex[leftIndex]);
        hexArr.push(hex[rightIndex]);
      } else {
        hexArr.push(0);
        hexArr.push(hex[res[i]]);
      }
    }
  }
  return (hexRes += hexArr.join("")); // #EDEDED
}

// 判断是否为空值/NaN/undefined/''/null
export function isEmpty(value) {
  if (
    value === undefined ||
    value === null ||
    value === "" ||
    (typeof value === "number" && isNaN(value))
  ) {
    return true;
  }
  if (typeof value === "object" && value.length === 0) {
    return true;
  }
  return false;
}

// 获取精度
export function getPrecision(num) {
  if (!num) return 0;
  const str = num.toString();
  const parts = str.split(".");
  return parts.length > 1 ? parts[1].length : 0;
}

/**
 * 计算在线通道数量
 * @param node 树节点
 * @returns 在线通道数量
 */
export const getOnlineChannelCount = (node: any): number => {
  if (node.type === "channel") {
    // 如果是通道节点，判断是否在线
    return node.status === "CONNECT_SUCCESS" ? 1 : 0;
  }
  if ((node.type === "directory" || node.type === "business-root") && node.children) {
    // 如果是区域节点或者视频根节点，递归统计子节点
    return node.children.reduce((count, child) => count + getOnlineChannelCount(child), 0);
  }
  return 0; // 其他类型节点不计入
};

/**
 * 计算总通道数量
 * @param node 树节点
 * @returns 总通道数量
 */
export const getTotalChannelCount = (node: any): number => {
  if (node.type === "channel") {
    // 如果是通道节点，计入总数
    return 1;
  }
  if ((node.type === "directory" || node.type === "business-root") && node.children) {
    // 如果是区域节点或者视频根节点，递归统计子节点
    return node.children.reduce((count, child) => count + getTotalChannelCount(child), 0);
  }
  return 0; // 其他类型节点不计入
};
