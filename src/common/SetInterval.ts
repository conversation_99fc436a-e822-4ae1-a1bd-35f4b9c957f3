class SetInterval {
  tasks: Map<any, any>;

  constructor() {
    this.tasks = new Map();
  }

  /**
   * @description: 添加定时器方法
   * @param {any} name
   * @param {number} wait 等待时间 默认一秒
   * @param {boolean} leading 是否先执行一次
   * @return {undefined}
   */
  add = async (name, callback, wait = 1000, leading = false) => {
    if (!name) {
      console.error('Setinterval => add => 必须传入name');
      return;
    }

    if (this.tasks.has(name)) {
      console.error(`Setinterval => add => 已有相同name名字定时器, 请更换name名 => ${name}`);
      return;
    }

    console.log(
      `%c添加定时器[${name}]成功, 刷新间隔为[${wait}]毫秒, ${
        leading ? '已' : '未'
      }开启先执行一次callback功能`,
      'color: #24c59d; font-weight: 700',
    );

    // 定时器前先执行一次函数
    try {
      // eslint-disable-next-line no-unused-expressions
      leading && (await callback());
    } catch (error) {
      console.error(
        `SetInterval => add => 定时器 [${name}] 执行出错, 错误原因 => ${error}`,
        'color: #24c59d; font-weight: 700',
      );
    }

    this.tasks.set(name, { wait, callback });
  };

  /**
   * @description: 将定时任务添加到队列中
   * @param {string} name 定时器名称
   * @return {*}
   */
  run = async (name) => {
    const inner = async () => {
      
      const task = this.tasks.get(name);
      if (task) {
        this[name] = setTimeout( () => {
          try {
            // console.log(`%c运行定时器[${name}]成功`,'color: blue; font-weight: 700')
            task.callback();
          } catch (error) {
            console.error(
              `SetInterval => run => inner => 定时器 [${name}] 执行出错， 错误原因 => ${error}`,
            );
          } finally {
            this.closeTimeout(name);
            inner();
          }
        }, task.wait);
      }
    };
    await inner();
  };

  /**
   * @description: 关闭某个定时器
   * @param {string} name 定时器名称
   * @return {*}
   */
  closeTimeout = (name) => {
    clearTimeout(this[name]);
    this[name] = null;
  };

  /**
   * @description: 外部调用关闭某个定时器
   * @param {any} name 定时器名称
   * @return {*}
   */
  close = (name) => {
    try {
      const task = this.tasks.get(name);

      if (task) {
        this.tasks.delete(name);
        this.closeTimeout(name);
        console.log(`%c关闭定时器[${name}]成功`, 'color: #24c59d; font-weight: 700');
      } else {
        console.error(`SetInterval => close => 未找到 [${name}] 定时器`);
      }
    } catch (error) {
      console.error('SetInterval => close => 删除task失败 => 失败原因 => ', error);
    }
  };
  // 暂停某个定时器
  pause = (name) => {
    const task = this.tasks.get(name);
    if (task) {
      this.closeTimeout(name);
      console.log(`%c暂停定时器[${name}]成功`, 'color: #24c59d; font-weight: 700');
    } else {
      console.error(`SetInterval => pause => 未找到 [${name}] 定时器`);
    }
  };



  /**
   * 检查是否有定时器
   * @param name
   * @returns true 有 false 没有
   */
  hasTimer = (name) => {
    const task = this.tasks.get(name);
    if(task){
      return true;
    }else{
      return false;
    }
  }
}

export default new SetInterval();
