/**
 * OAuth2 初始化参数接口
 */
interface IOAuth2Params {
  clientId: string; // OAuth 客户端标识符
  authHost: string; // 认证服务器主机 URL
  apiHost: string; // API 服务器主机 URL
  webTokenUrl: string; // Web 回调 URL
}

/**
 * OAuth2 配置参数接口
 */
interface IOAuth2Config {
  clientId: string; // OAuth 客户端标识符
  authUrl: string; // 授权端点 URL
  redirectUri: string; // 认证后服务器重定向 URI
  refreshTokenUrl: string; // 刷新令牌 URL
  validateTokenUrl: string; // 验证令牌 URL
  logoutUrl: string; // 登出 URL
  webTokenUrl: string; // Web 令牌回调 URL
  targetPath: string; // 成功认证后重定向的目标路径
}

/**
 * OAuth 令牌数据结构
 */
interface ITokenData {
  accessToken: string; // 访问令牌
  expiresIn: string; // 令牌过期时间戳
}

/**
 * OAuth2 会话存储键
 */
const OAUTH2_LOCAL_KEY = {
  OAuth2Token: "OAuth2Token", // 存储 OAuth2 令牌的键
};

/**
 *  OAuth2 用户信息
 */
const OAUTH2_USER_INFO_KEY = {
  OAuth2UserInfo: "ssoUser", // 存储 OAuth2 用户信息的键
};

/**
 * OAuth2 认证管理类 - 单例模式实现
 */
class OAuth2 {
  private static instance: OAuth2; // 单例实例
  private timer: any; // 定时器引用
  private config: IOAuth2Config; // 配置

  /**
   * 私有构造函数，防止直接实例化
   * @param params OAuth2 初始化参数
   */
  private constructor(params?: IOAuth2Params) {
    if (params) {
      this.initializeConfig(params);
    }
  }

  /**
   * 初始化 OAuth2 配置
   * @param params OAuth2 初始化参数
   */
  private initializeConfig(params: IOAuth2Params): void {
    const { clientId, authHost, apiHost, webTokenUrl } = params;
    this.config = {
      clientId,
      authUrl: `${authHost}/oauth2/authorize`,
      redirectUri: `${apiHost}/api/hsm/auth/tokenCallback`,
      refreshTokenUrl: `${apiHost}/api/hsm/auth/refresh`,
      validateTokenUrl: `${apiHost}/api/hsm/auth/validate`,
      logoutUrl: `${apiHost}/api/hsm/auth/logout`,
      webTokenUrl,
      targetPath: "/",
    };
    localStorage.setItem("oauth2Config", JSON.stringify(this.config));
  }

  /**
   * 获取 OAuth2 实例（单例模式）
   * @returns OAuth2 单例实例
   */
  public static getInstance(): OAuth2 {
    if (!OAuth2.instance) {
      OAuth2.instance = new OAuth2();
    }
    return OAuth2.instance;
  }

  /**
   * 初始化 OAuth2 实例并配置参数
   * @param params OAuth2 初始化参数
   * @returns OAuth2 单例实例
   */
  public static initialize(params: IOAuth2Params): OAuth2 {
    if (!OAuth2.instance) {
      OAuth2.instance = new OAuth2(params);
    } else {
      OAuth2.instance.initializeConfig(params);
    }

    const { webTokenUrl } = OAuth2.instance.config;
    if (
      !window.location.pathname.includes(webTokenUrl) &&
      !window.location.hash.includes(webTokenUrl)
    ) {
      const oauth2Token: ITokenData = localStorage.getItem(OAUTH2_LOCAL_KEY.OAuth2Token) ? JSON.parse(
        localStorage.getItem(OAUTH2_LOCAL_KEY.OAuth2Token) as string
      ) : null;
      if (!oauth2Token) {
        OAuth2.instance.authorize();
        return;
      }
      const { accessToken, expiresIn } = oauth2Token;
      if (!accessToken || !expiresIn || parseInt(expiresIn) - new Date().getTime() < 0) {
        OAuth2.instance.authorize();
        return;
      }
    }
    OAuth2.instance.addOAuthTimer();
    return OAuth2.instance;
  }

  /**
   * 执行 OAuth2 授权流程
   * 重定向用户到授权页面
   */
  public authorize() {
    const { clientId, redirectUri, authUrl, webTokenUrl, targetPath } = this.config;
    const tokenUrl = window.location.hash ? `/#${webTokenUrl}` : webTokenUrl;
    const redirectWeb = encodeURIComponent(
      `${window.location.origin}${tokenUrl}?path=${targetPath}`
    );
    const redirectUrl = encodeURIComponent(`${redirectUri}?redirect_web=${redirectWeb}`);
    const url = `${authUrl}?client_id=${clientId}&redirect_uri=${redirectUrl}&response_type=token`;
    window.location.href = url;
  }

  /**
   * 从 URL 获取访问令牌并存储
   * 通常在认证回调后调用
   */
  public getAccessToken(): boolean {
    const { webTokenUrl } = this.config;
    if (
      !window.location.pathname.includes(webTokenUrl) &&
      !window.location.hash.includes(webTokenUrl)
    ) {
      console.log("当前地址不是认证回调地址");
      return false;
    }
    // if (this.getToken()) {
    //   return false;
    // }
    let searchStr = "";
    if (window.location.hash) {
      const searchIndex = window.location.hash.indexOf("?");
      searchStr = window.location.hash.substring(searchIndex + 1);
    } else {
      searchStr = window.location.search.substring(1);
    }
    const searchParams = new URLSearchParams(searchStr);
    const accessToken = searchParams.get("access_token") || "";
    const expiresIn = searchParams.get("expires_in") || "0";
    const tokenData: ITokenData = { accessToken, expiresIn };
    if (accessToken && expiresIn !== "0") {
      localStorage.setItem(OAUTH2_LOCAL_KEY.OAuth2Token, JSON.stringify(tokenData));
    }

    if (!accessToken || expiresIn === "0") {
      console.warn("认证回调获取token失败");
      return false;
    }
    const targetPath = searchParams.get("path") || webTokenUrl;
    window.location.href = targetPath;
    return true;
  }

  /**
   * 刷新访问令牌
   * @returns 刷新后的令牌数据或 null（如果刷新失败）
   */
  private async refreshToken() {
    const oauth2Token: ITokenData = JSON.parse(
      localStorage.getItem(OAUTH2_LOCAL_KEY.OAuth2Token) as string
    );
    const { accessToken } = oauth2Token;

    const res = await fetch(this.config.refreshTokenUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: accessToken,
      },
    });
    const data = await res.json();
    if (data.code == 0) {
      const accessToken = data.data.access_token || "";
      const expiresIn = data.data.expires_in || "0";
      const tokenData: ITokenData = { accessToken, expiresIn };
      localStorage.setItem(OAUTH2_LOCAL_KEY.OAuth2Token, JSON.stringify(tokenData));
    }
    return data;
  }

  /**
   * 验证当前令牌是否有效
   * @returns 验证结果数据或 null（如果验证失败）
   */
  private async validateToken() {
    const oauth2Token: ITokenData = JSON.parse(
      localStorage.getItem(OAUTH2_LOCAL_KEY.OAuth2Token) as string
    );
    const { accessToken } = oauth2Token;

    const res = await fetch(this.config.validateTokenUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: accessToken,
      },
    });

    const data = await res.json();
    return data;
  }

  /**
   * 注销用户并重定向到授权页面
   * @returns 注销结果或 null（如果注销失败）
   */
  public async logout(authorize = true) {
    const oauth2Token: ITokenData = JSON.parse(
      localStorage.getItem(OAUTH2_LOCAL_KEY.OAuth2Token) as string
    );
    // 如果没有令牌，则直接重定向到授权页面
    if (oauth2Token === null && authorize) {
      this.authorize();
      return null;
    }
    const { accessToken } = oauth2Token;

    const res = await fetch(this.config.logoutUrl, {
      method: "GET",
      headers: {
        "Content-Type": "application/json",
        Authorization: accessToken,
      },
    });
    const data = await res.json();
    if (data.code !== 0) {
      console.warn("注销失败");
      return null;
    }

    localStorage.removeItem(OAUTH2_LOCAL_KEY.OAuth2Token);
    localStorage.removeItem(OAUTH2_USER_INFO_KEY.OAuth2UserInfo);

    if (authorize) {
      this.authorize();
    }
  }

  /**
   * 添加定时器，定期检查令牌状态
   * 如果令牌即将过期，则刷新
   * 如果令牌无效，则重新授权
   */
  public addOAuthTimer() {
    this.removeOAuthTimer();

    this.timer = setInterval(async () => {
      const oauth2Token: ITokenData = JSON.parse(
        localStorage.getItem(OAUTH2_LOCAL_KEY.OAuth2Token) as string
      );
      if (!oauth2Token) {
        this.authorize();
        return;
      }

      const { expiresIn } = oauth2Token;
      if (parseInt(expiresIn) - new Date().getTime() < 180 * 1000) {
        const refreshRes = await this.refreshToken();
        if (refreshRes && refreshRes.code !== 0) {
          this.authorize();
          return;
        }
      }

      const validateRes = await this.validateToken();
      // 未授权退出登录跳转到授权页面
      if (validateRes.code === 402) {
        this.logout(false);
        window.location.href = "/authorization";
        return;
      } else if (validateRes && validateRes.code !== 0) {
        this.authorize();
        return;
      }
    }, 3000);
  }

  /**
   * 清除 OAuth 定时器
   */
  public removeOAuthTimer() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  }

  /**
   * 设置认证成功后的目标路径
   * @param path 目标路径
   */
  public setTargetPath(path: string = "/") {
    this.config.targetPath = path;
  }

  /**
   * 获取 OAuth2 Token
   * @returns OAuth2 Token 数据
   */
  public getToken(): ITokenData {
    return JSON.parse(localStorage.getItem(OAUTH2_LOCAL_KEY.OAuth2Token) as string);
  }
}

export default OAuth2;
