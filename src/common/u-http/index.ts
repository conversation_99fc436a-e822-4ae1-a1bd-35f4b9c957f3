import axios from "axios";
import qs from "qs";
import { message } from "ant-design-vue";
import { useStore } from "vuex";
import { useRouter } from "vue-router";
import { ISendParam } from "./types";
import OAuth2 from "../oauth/OAuth2";
import { store } from "@/store";
let oneStore = null;
const router = useRouter();

message.config({
  duration: 0.5,
  maxCount: 1,
});

const instance = axios.create({
  timeout: 60000 * 5,
  headers: {
    "X-Requested-With": "XMLHttpRequest", // 设置为异步
    "Content-Type": "application/json",
  },
  withCredentials: false,
  baseURL: "",
});

// 添加请求拦截器
instance.interceptors.request.use(
  (config) => {
    // console.log('req-config', config);
    const con = config;
    /**
     * 拦截添加token
     */
    if ((window as any).g.plat_type === 0) {
      const ACCESS_TOKEN = JSON.parse(window.localStorage.getItem("hollysysIntelligent"));
      con.headers.Authorization = ACCESS_TOKEN?.token;
    } else {
      if (!(window as any).isDcs) {
        const { accessToken } = OAuth2.getInstance().getToken();
        // debugger
        con.headers["Authorization"] = accessToken;
      }
    }

    if (
      ["put", "post", "patch"].includes(con.method) &&
      con.headers["Content-Type"] &&
      con.headers["Content-Type"].includes("application/x-www-form-urlencoded")
    ) {
      con.data = qs.stringify(con.data);
    }
    // console.log('con', con);

    return con;
  },
  (error) => {
    console.log("req-config-err>>", error);
    return Promise.reject(new Error(error));
  }
);

// 添加响应拦截器
instance.interceptors.response.use(
  (response) => {
    const resData = response.data;
    const disposition = response.headers["content-disposition"];
    const { config } = response;
    if (config.responseType === "blob") {
      return {
        data: resData,
        disposition,
      };
    }
    if (resData.code === 109 || resData.code === 402) {
      // 登录过期 或 license过期打回登录页
      message.destroy();
      message.error({
        content: resData.msg || "未登录",
        duration: 2.5,
      });
      if (window.localStorage.getItem("hollysysIntelligent")) {
        window.localStorage.removeItem("hollysysIntelligent");
      }
      // 只在402时跳转到授权页面，其他情况直接退出登录
      if (!window.location.href.endsWith("/authorization") && resData.code === 402) {
        window.location.href = "/authorization";
      }
      if (window.localStorage.getItem("plantMode") === "true" && resData.code === 402) {
        OAuth2.getInstance().logout(false);
      }
    }

    if (resData.code !== 0 && resData.code !== 10000) {
      message.destroy();
      message.error({
        content: resData.msg || "服务异常",
        duration: 2.5,
      });
    }

    if (resData.code === 406) {
      message.destroy();
      message.error({
        content: resData.msg || "文件不合规，请核对后重试",
        duration: 2.5,
      });
    }

    return resData;
  },
  (error) => {
    if ((window as any).g.plat_type === 0) {
      console.error("res-config-err>>", error);
      const msg = "无法连接到服务器。请检查您的网络设置，或稍后重试。";
      message.destroy();
      message.error(msg, 1.5);
      return { status: -200, msg };
    }
  }
);

/**
 *
 * 下载时用json格式请求，服务端以blob返回
 * 客户端响应拦截reponseType:blob时直接通过，然后downloadByParseBlob解析blob并下载
 * 配置：other:{ responseType: 'blob',: true }
 * responseType: blob 下载
 * noLoading: true 不显示加载动画
 *
 * 上传时用form-data格式请求，参数用formData.append()添加
 * 配置：headers: {'Content-Type': 'multipart/form-data'}
 */
export function send(params: ISendParam) {
  const { method, url, data = {}, headers = {}, other = {} } = params;

  const store = useStore();
  if (store) {
    oneStore = store;
  }

  const reqParams: any = {
    method,
    url,
    headers,
    ...other,
  };

  // Only applicable for request methods 'PUT', 'POST', and 'PATCH'
  if (["put", "post", "patch", "delete"].includes(method)) {
    reqParams.data = data;
  } else {
    reqParams.params = data;
  }

  return instance.request(reqParams);
}

/**
 *
 * 解析blob，并下载
 * 如果是json，解析并回调
 * 如果是文件，直接下载
 *
 */
export function downloadByParseBlob({ data, disposition }, callback) {
  if (!data) {
    return;
  }

  if (data.type.includes("application/json")) {
    const reader = new FileReader();
    reader.onload = () => {
      const content = reader.result || "{}";
      const jsonObj = JSON.parse(content as string);
      if (callback instanceof Function) {
        callback(jsonObj);
      }
    };
    reader.readAsText(data);
    return;
  }
  const fileName = decodeURI(
    disposition.substring(disposition.toLowerCase().indexOf("filename=") + 9, disposition.length)
  );
  // console.log(fileName.substring(0,fileName.length-1).slice(1))
  // const fileName = '123.zip'
  const blobObj = new Blob([data], { type: "" });
  if ((window.navigator as any).msSaveBlob) {
    // for ie 10 and later
    try {
      (window.navigator as any).msSaveBlob(blobObj, fileName);
    } catch (e) {
      console.error("download-error", e);
    }
  } else {
    const blobUrl = URL.createObjectURL(blobObj);
    const eleLink = document.createElement("a");
    eleLink.download = fileName.substring(0, fileName.length - 1).slice(1);
    eleLink.href = blobUrl;
    eleLink.style.display = "none";
    eleLink.click();
    URL.revokeObjectURL(blobUrl);
  }
}
