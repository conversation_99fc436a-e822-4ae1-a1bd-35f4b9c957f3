import type { RuleObject } from "ant-design-vue/es/form";

// 中文校验
export const validateChinese = async (_rule: RuleObject, value: string) => {
  if (value === "") {
    return Promise.reject(new Error("中文不能为空"));
  }
  const myreg = /^[\u4e00-\u9fa5]{1,20}$/;
  if (!myreg.test(value)) {
    return Promise.reject(new Error("只能包含中文字符，20个字符以内"));
  }
};

// 名称校验
export const validateName = async (_rule: RuleObject, value: string) => {
  if (value === "") {
    return Promise.reject(new Error("名称不能为空"));
  }
  const myreg = /^(?![!@#$%^&*()+=\[\]{}\\|;:'",.<>?`~])[\w\u4e00-\u9fa5]{1,20}$/;
  if (!myreg.test(value)) {
    return Promise.reject(new Error("名称不能包含特殊字符，20个字符以内"));
  }
};
// 变量名称
export const validateValName = async (_rule: RuleObject, value: string) => {
  if (value === "") {
    return Promise.reject(new Error("名称不能为空"));
  }
  const myreg = /^[a-zA-Z0-9_]{1,20}$/;
  if (!myreg.test(value)) {
    return Promise.reject(new Error("名称只包含字母、数字和下划线，20个字符以内"));
  }
};

// 点域
export const validateNameSpace = async (_rule: RuleObject, value: string) => {
  if (value === "" || value === null) {
    return Promise.resolve();
  }
  const myreg = /^[a-zA-Z0-9_]{1,50}$/;
  if (!myreg.test(value)) {
    return Promise.reject(new Error("点域只包含字母、数字和下划线，且长度在50个字符以内"));
  }
};

// 点名
export const validateTag = async (_rule: RuleObject, value: string) => {
  if (value === "" || value === null) {
    return Promise.resolve();
  }
  const myreg = /^[a-zA-Z0-9_]{1,50}$/;
  if (!myreg.test(value) && (value !== "" || value !== null)) {
    return Promise.reject(new Error("点名只包含字母、数字和下划线，且长度在50个字符以内"));
  }
};

// 点项
export const validateItem = async (_rule: RuleObject, value: string) => {
  if (value === "" || value === null) {
    return Promise.resolve();
  }
  const myreg = /^[a-zA-Z0-9_]{1,10}$/;
  if (!myreg.test(value) && (value !== "" || value !== null)) {
    return Promise.reject(new Error("点项只包含字母、数字和下划线，且长度在10个字符以内"));
  }
};

export const validateDomainTagItem = async (_rule: RuleObject, value: string) => {
  if (value === "" || value === null) {
    return Promise.resolve();
  }

  // 正则表达式确保每个部分只包含字母、数字和下划线，并且长度在指定范围内
  const myreg = /^[a-zA-Z0-9_]{1,50}(\.[a-zA-Z0-9_]{1,50})?(\.[a-zA-Z0-9_]{1,10})?$/;

  if (!myreg.test(value)) {
    return Promise.reject(
      new Error(
        "域点项格式不正确，每个部分只包含字母、数字和下划线，且长度分别为50、50、10个字符以内"
      )
    );
  }

  // 进一步验证每个部分的长度
  const parts = value.split(".");
  if (parts.length !== 3) {
    return Promise.reject(new Error("域点项必须包含三个部分，格式为abc.abc.abc"));
  }

  if (parts[0].length > 50 || parts[1].length > 50 || parts[2].length > 10) {
    return Promise.reject(
      new Error(
        "域点项格式不正确，每个部分只包含字母、数字和下划线，且长度分别为50、50、10个字符以内"
      )
    );
  }

  return Promise.resolve();
};

// 颜色校验
export const validateColor = async (_rule: RuleObject, value: string) => {
  if (value === "" || value === null) {
    return Promise.reject("颜色不能为空");
  }
};

// ip校验
export const validateHost = async (_rule: RuleObject, value: string) => {
  const ipReg =
    /^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}$/;
  if (value === "") {
    // eslint-disable-next-line prefer-promise-reject-errors
    return Promise.reject("IP不能为空");
  }
  if (!ipReg.test(value)) {
    // eslint-disable-next-line prefer-promise-reject-errors
    return Promise.reject("请输入正确的IP格式");
  }
  return Promise.resolve();
};
