import themes from "./themes";

if (!(window as any).$wujie) {
  /**
   * 切换主题
   */
  (window as any).changeTheme = (themeKey: string) => {
    const themeObj = (themes as any)[themeKey];
    // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
    Object.keys(themeObj).forEach((cssKey) => {
      // eslint-disable-next-line @typescript-eslint/no-unsafe-argument
      document.documentElement.style.setProperty(cssKey, themeObj[cssKey]);
    });
    localStorage.setItem("localThemeKey", themeKey);
  };

  /**
   * 获取当前主题
   */
  (window as any).getCurTheme = () => {
    const localThemeKey: any = localStorage.getItem("localThemeKey");
    const curThemeKey: any = Object.keys(themes).includes(localThemeKey)
      ? localThemeKey
      : "vision";
    return { themeKey: curThemeKey, theme: (themes as any)[curThemeKey] };
  };

  /**
   * 初始化
   */
  const { themeKey } = (window as any).getCurTheme();
  (window as any).changeTheme(themeKey);
}
