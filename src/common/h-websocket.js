/**
 * @param url
 * websocket地址，会自动加上请求host及前缀
 *
 * @param reconnectNum
 * 重连次数，默认10
 *
 * @param reconnectDelay
 * 连接失败后，建立重连的间隔时间，单位毫秒
 *
 */
import { message } from 'ant-design-vue';

class HWebsocket {
  constructor(url, reconnectNum = 10, reconnectDelay = 6000) {
    this._path = url;
    this.ws = null;

    this._reconnectMaxNum = reconnectNum;
    this._reconnectDelay = reconnectDelay;
    this._canReconnect = true;
    this._canOneAfterMessage = true;
    this._reconnectCurNum = 0;
    this._reconnectId = null;

    this._create();
  }

  close() {
    if (this.ws) {
      if (this.ws.readyState === 1) {
        this.ws.close();
      } else {
        // 非连接状态，主动调用关闭，则先取消重连，然后断开
        this._canReconnect = false;
        if (this._reconnectId) {
          clearTimeout(this._reconnectId);
          this._reconnectId = null;
        }
        this.ws.close();
      }
    }
  }

  _create() {
    this.ws = new WebSocket(this._path);
    this.ws.onopen = this._onOpen.bind(this);
    this.ws.onmessage = this._onMessage.bind(this);
    this.ws.onclose = this._onClose.bind(this);
    this.ws.onerror = this._onError.bind(this);
  }

  _onOpen(e) {
    this._reconnectCurNum = 0;
    this._canOneAfterMessage = true;
    if (this.onOpen instanceof Function) {
      this.onOpen(e);
    }
  }

  _onMessage(e) {
    if (this.onMessage instanceof Function) {
      this.onMessage(e);
      if (this._canOneAfterMessage) {
        this._canOneAfterMessage = false;
        this._oneAfterMessage();
      }
    }
  }

  _onClose(e) {
    if (this.onClose instanceof Function) {
      this.onClose(e);
    }

    // 如果非正常关闭，弹出异常提示
    if (e.code !== 1000 && this._canReconnect && this._reconnectCurNum < 1) {
      // message.error('与服务器断开链接');
      this._oneError();
    }

    // 如果非正常关闭，并且需要重连，则重连
    if (e.code !== 1000 && this._canReconnect && this._reconnectCurNum < this._reconnectMaxNum) {
      this._reconnectCurNum++;

      this._reconnectId = setTimeout(() => {
        this._create();
        clearTimeout(this._reconnectId);
        this._reconnectId = null;
      }, this._reconnectDelay);

      console.log(`reconnect: ${this._reconnectCurNum}`);
    }
  }

  _onError(e) {
    console.log('hwebsocket-error', e);
  }

  // 只触发一次错误
  _oneError() {
    if (this.onOneError instanceof Function) {
      this.onOneError();
    }
  }

  // 第一次onMessage后触发
  _oneAfterMessage() {
    if (this.onOneAfterMessage instanceof Function) {
      this.onOneAfterMessage();
    }
  }
}

export default HWebsocket;
