// devicePixelRatio.js
class disableScaling {
    constructor() {
            //this.flag = false;
        }

        //获取页面缩放比例
        //    _getDevicePixelRatio() {
        //        let t = this;
        //    }
        //监听方法兼容写法

        //初始化页面比例
    init() {
        // let t = this;
        // if (t._getSystem()) { //判断设备，目前只在windows系统下校正浏览器缩放比例
        //     //初始化页面校正浏览器缩放比例
        //     t._correct();
        //     //开启监听页面缩放
        //     t._watch();
        // }

        window.onload = function(){
            document.addEventListener('keydown',function(event){
                if((event.ctrlKey === true || event.metaKey === true)
                    && (event.which === 61 || event.which === 107 || event.which === 173 || event.which === 109 || event.which === 187 || event.which === 189)
                ){
                    event.preventDefault();
                }
            },false)
        }

        // var scrollFunc = function(e){
        //     if(e.wheelDelta === '120' || e.wheelDelta === '-120'){
        //         event.returnValue = false;
        //     }else if(e.detail){
        //         event.returnValue = false;
        //     }
        // }

        // if(document.addEventListener){
        //     document.addEventListener('DOMMouseScroll',scrollFunc,false);
        // }
        document.addEventListener('mousewheel',function(event:any){
            if(event.ctrlKey === true || event.metaKey){
                event.preventDefault();
            }
        },{passive:false});
    }
}
export default disableScaling;