/**
 * 生成指定长度的UUID
 * @param len
 * @param radix
 * @returns uuid
 * eg: uRandom(8, 2)=>"01001010"; uRandom(8, 10)=>"47473046"; uRandom(8, 16)=>"098F4D35"
 */
const uRandom = (len = 1, radix: 2 | 10 | 16 = 10): string => {
  const chars = '0123456789'.split('');
  const uuid = [];
  for (let i = 0; i < len; i++) {
    uuid[i] = chars[0 | (Math.random() * radix)];
  }
  return uuid.join('');
};

export default uRandom;
