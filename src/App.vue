<template>
  <div id="app">
    <a-config-provider
      :locale="zhCN"
      component-size="small"
      :theme="{ token: { colorPrimary: themes['--primary-color'], borderRadius: 2, fontSize: 14,fontFamily:'PingFang SC' } }"
    >
      <router-view />
    </a-config-provider>
  </div>
</template>
<script lang="ts">
import {
  ref,
  computed,
  defineComponent,
  onBeforeMount,
  onBeforeUnmount,
  onMounted,
  onUnmounted,
  reactive,
} from "vue";
import { useRouter, useRoute } from "vue-router";
import { useStore } from "vuex";
import zhCN from "ant-design-vue/es/locale/zh_CN";
import { loginStatus } from "@/api/project";
// import zhCn from 'element-plus/dist/locale/zh-cn.mjs'
import dayjs from "dayjs";
import "dayjs/locale/zh-cn";
import SetInterval from "@/common/SetInterval";
import disableScaling from "./common/disableScaling";

import { message } from "ant-design-vue";

dayjs.locale("zh-cn");

export default defineComponent({
  setup() {
    const store = useStore();
    const router = useRouter();
    const route = useRoute();
    const isLoading = computed(() => store.state.design.loading);
    const loadingMsg = computed(() => store.state.design.loadingMsg);
    const loadingProps: {
      spinning: boolean;
    } = reactive({
      spinning: isLoading,
    });

    const channelBaseInfo = computed(() => store.state.design.channelBaseInfo);
    const channelConfigInfo = computed(() => store.state.design.channelConfigInfo);
    const presetBaseInfo = computed(() => store.state.design.presetBaseInfo);
    const scheduleConfigModel = computed(() => store.state.design.scheduleConfigModel);
    window.addEventListener("beforeunload", (event) => {
      const allList = [
        ...channelBaseInfo.value,
        ...channelConfigInfo.value,
        ...presetBaseInfo.value,
        ...scheduleConfigModel.value,
      ];
      if (allList.length !== 0) {
        event.preventDefault();
      }
    });

    function openLoginTimer() {
      if ((window as any).g.plat_type === 0) {
        SetInterval.add("loginTimer", toLogin, 5000 * 60);

        SetInterval.run("loginTimer");
      }
    }
    function closeLoginTimer() {
      if ((window as any).g.plat_type === 0) {
        SetInterval.close("loginTimer");
      }
    }
    function toLogin() {
      loginStatus().then((res) => {
        if (res.data === "false") {
          // DCSvideoAlarm or DCSChannelView or DCS or DCSPopup
          window.location.href = "/login";
          if (window.localStorage.getItem("hollysysIntelligent")) {
            window.localStorage.removeItem("hollysysIntelligent");
          }
        }
      });
    }
    new disableScaling().init();

    //---------------------------------------------------------themes-------------------------------------------------

    const themes = ref<any>([]);
    const wujie = (window as any).$wujie;
    if (wujie?.props) {
      themes.value = wujie?.props.theme;
    } else {
      const { theme } = (window as any).getCurTheme();   
      themes.value = theme;
    }
    wujie?.bus.$on("changetheme", function (theme: any) {
      themes.value = theme;
    });

    message.config({
      duration: 2,
      maxCount: 3,
    });

    onMounted(() => {
      if ((window as any).g.plat_type === 0) {
        openLoginTimer();
      }
    });
    onBeforeUnmount(() => {
      if ((window as any).g.plat_type === 0) {
        closeLoginTimer();
      }
    });

    return {
      loadingProps,
      loadingMsg,
      zhCN,
      themes,
    };
  },
});
</script>
<style lang="less">
html,
body {
  color: #fff;
  width: 100% !important;

  height: 100% !important;
  background-repeat: no-repeat;
  overflow: hidden;
}

#app {
  text-align: left;
  color: #2c3e50;
  padding: 0px;
  margin: 0px;
  top: 0px;
  left: 0px;
  height: 100%;
  max-height: 100%;
  min-height: 100%;
  overflow: hidden;
  min-width: 1900px;
}

/* Webkit浏览器 统一滚动条样式*/
&::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

/* 滚动条滑块 */
&::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.2);
}

/* 滚动条轨道 */
&::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
</style>
