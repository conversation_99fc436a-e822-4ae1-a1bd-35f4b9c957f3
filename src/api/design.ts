import { send } from "../common/u-http";
import { ISendParam, Irang, <PERSON><PERSON>, Iout } from "../common/u-http/types";

const baseUrl = (window as any).g.api;

// 获取树结构
export const getProjectTree = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/project/tree-and-state`,
  } as ISendParam);

//  forDCS
export const getProjectTreeForDCS = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/api-for-dcs/project/tree-and-state`,
  } as ISendParam);
// 添加树节点
export const addTreeNode = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/project-tree-node`,
    data,
  } as ISendParam);

// 批量添加树节点
export const addTreeNodeBatch = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/project-tree-node/batch`,
    data,
  } as ISendParam);

// 批量添加通道节点
export const addChannelBatch = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/channel-info/batch-create`,
    data,
  } as ISendParam);

// 删除树节点
export const delTreeNode = (data): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/project-tree-node?idList=${data}`,
  } as ISendParam);

export const clickTreeNode = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/channel-info/${data}`,
  } as ISendParam);

// 属性栏树节点配置
export const treeNodeConf = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/project-tree-node`,
    data,
  } as ISendParam);

// 导入通道
export const importChannel = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/channel-info/import-excel`,
    data,
  });

// 导出通道
export const exportChannel = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/channel-info/export-excel`,
    other: { responseType: "blob" },
  });

export const exportChannelTemp = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/channel-info/export-template`,
    other: { responseType: "blob" },
  });
// 获取RTSP流地址
export const rtspUrl = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/channel-info/rtsp-by-channel`,
    data,
  } as ISendParam);
// 获取摄像头厂商
export const cameraManufacturer = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/channel-info/camera-factory`,
    data,
  } as ISendParam);
// 属性栏视频通道配置
export const videoPathConf = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/channel-info`,
    data,
  } as ISendParam);

// 查询调度
export const getTimeSchedule = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/schedule-period?channelId=${data}`,
  } as ISendParam);

// 时间调度添加
export const addTimeSchedule = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/schedule-period`,
    data,
  } as ISendParam);

// 获取通道推流地址
export const getChannelVideoLocation = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/channel-info/preview-url?channelId=${data}`,
  } as ISendParam);

// 获取通道推流地址(DCS)
export const getChannelVideoLocationDCS = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/api-for-dcs/preview-info?channelId=${data}`,
  } as ISendParam);

// 获取当前分辨率
export const getVideoResolution = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/channel-info/scale?id=${data.id}&type=${data.type}`,
  } as ISendParam);

export const getDcsVideoResolution = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/api-for-dcs/scale?id=${data.id}&type=${data.type}`,
  } as ISendParam);

// 摄像头控制
export const PTZContorls = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/channel-info/start-control-operate?action=${data.action}&channelId=${data.channelId}`,
  } as ISendParam);
export const PTZStopContorls = (data, presetId = ""): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/channel-info/stop-control-operate?channelId=${data.channelId}&action=${data.action}`,
  } as ISendParam);

// 摄像头控制(DCS)
export const PTZContorlsDCS = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/api-for-dcs/start-control-operate?action=${data.action}&channelId=${data.channelId}`,
  } as ISendParam);
export const PTZStopContorlsDCS = (data, presetId = ""): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/api-for-dcs/stop-control-operate?channelId=${data.channelId}&action=${data.action}`,
  } as ISendParam);

// 跳转到预置点
export const jumpToPresetPoint = (presetId): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/os/mes/jump-to-preset?presetId=${presetId} `,
  } as ISendParam);

// 获取图像列表
// /channel-zero-calibration/select-pic-list
export const getHistoryPic = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/channel-zero-calibration/select-pic-list/?channelId=${data}`,
  } as ISendParam);

// 删除图片
export const delHistoryPic = (data): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/channel-zero-calibration/deletePicByChannelId?imgPath=${data.imgPath}&channelId=${data.channelId}`,
  } as ISendParam);

// 保存通道截图
export const saveHistoryPic = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/channel-zero-calibration/batchInsert`,
    data,
  } as ISendParam);

// 更改通道截图
export const editHistoryPic = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/channel-zero-calibration`,
    data,
  } as ISendParam);

/**
 * @description 模型通道配置
 */
export const getModelConf = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/preset-info/for-config/?presetId=${data}`,
  } as ISendParam);

export const accuratePTZ = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/onvif/jump-to-ptz`,
    data,
  } as ISendParam);

// 保存ptz
export const savePTZApi = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/preset-info/save-ptz`,
    data,
  } as ISendParam);

// 回跳ptz
export const backToPTZApi = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/preset-info/jump`,
    data,
  } as ISendParam);

/**
 * @description 模版
 */
export const perspectiveTransform = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/process-configure/correct`,
    data,
  } as ISendParam);

export const changeMatchStatus = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/process-configure/switch-match-enable`,
    data,
  } as ISendParam);

/**
 * @description 模型及参数
 */
export const addAlgorithm = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/preset-info/add-algorithm`,
    data,
  } as ISendParam);

export const getAlgorithmConf = (data, sceneId = ""): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/preset-info/algorithm-configure?algorithmInstanceId=${data}&sceneId=${sceneId}`,
  } as ISendParam);

/**
 * @description 获取图像列表
 */

export const getAlgorithmPic = (data, type = "SCHEDULE"): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/scene-custom-pic/${data}?type=${type}`,
  } as ISendParam);

export const uploadAlgorithmPic = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/scene-custom-pic`,
    data,
  } as ISendParam);

// 截图
export const screenshot = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/channel-info/crop`,
    data,
  } as ISendParam);

// 通道列表当前截图
export const channelScreenshot = (data, isCarryPtz): Promise<any> =>
  send({
    method: "get",
    url: isCarryPtz
      ? `${baseUrl}/channel-info/channel-pic/${data}?isCarryPtz=${isCarryPtz}`
      : `${baseUrl}/channel-info/channel-pic?channelId=${data}`,
  } as ISendParam);

// 零点校验
export const zeroPointCalibration = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/channel-zero-calibration/zero?channelId=${data}`,
  } as ISendParam);

// 提交校准时间
export const submitCalibrationTime = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/schedule-zero-calibration`,
    data,
  } as ISendParam);
// 获取校准时间
export const getCalibrationTime = (channelId: string): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/schedule-zero-calibration/time/${channelId}`,
  } as ISendParam);

// 获取校准图片
export const getCalibrationPic = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/channel-zero-calibration/${data}`,
  } as ISendParam);

// 新增场景
export const addSence = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/scene-definition`,
    data,
  } as ISendParam);

// 编辑场景
export const editSence = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/scene-definition`,
    data,
  } as ISendParam);

// 编辑场景名称

export const editSenceName = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/scene-definition/sceneName`,
    data,
  } as ISendParam);

// 删除场景
export const delSence = (data): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/scene-definition?idList=${data}`,
  } as ISendParam);

// 透视变换
export const savePerspective = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/preset-info/correct-param`,
    data,
  } as ISendParam);

// 多场景同步
export const sync = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/process-configure/copy-params`,
    data,
  } as ISendParam);

// 算法配置应用(保存)
export const applyAlgorithm = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/preset-info/algorithm-configure`,
    data,
  } as ISendParam);

export const delAlgorithm = (data): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/preset-info/delete-algorithm?algorithmInstanceIds=${data}`,
  } as ISendParam);

// 算法执行(识别)
export const algorithmDiscriminate = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/process-execute/test-algorithm`,
    data,
  } as ISendParam);

export const getImage = (url): Promise<any> =>
  send({
    method: "get",
    url,
  } as ISendParam);

export const saveSchedule = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/preset-info/schedule-config`,
    data,
  } as ISendParam);

/**
 *
 * @description  算法输出
 */
// 获取全局变量
export const getGlobal = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/preset-info/global-info?algorithmInstanceId=${data}`,
  } as ISendParam);

// 获取报警
export const getAlarm = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/preset-info/alarm-info?algorithmInstanceId=${data}`,
  } as ISendParam);

// 更新报警
export const editAlarm = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/algorithm-instance-output/alarm`,
    data,
  } as ISendParam);

// 更新全局变量
export const editGlobal = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/algorithm-instance-output/global`,
    data,
  } as ISendParam);

// 更新输出列表
export const editOutput = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/algorithm-instance-output`,
    data,
  } as ISendParam);

// 实例列表
export const getInstanceList = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/preset-info/list`,
  } as ISendParam);

// 更新预置点

export const editInstance = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/preset-info`,
    data,
  } as ISendParam);

// 批量更新预置点
export const batchEditInstance = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/preset-info/update-status`,
    data,
  } as ISendParam);
