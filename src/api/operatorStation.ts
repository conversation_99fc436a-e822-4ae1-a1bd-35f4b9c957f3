import { send } from "../common/u-http";
import { ISendParam, Irang, <PERSON><PERSON>, I<PERSON> } from "../common/u-http/types";

const baseUrl = (window as any).g.api;

// 主页指标
export const getleftTop = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/operate-statistical/left-top`,
  } as ISendParam);

export const getCenterTop = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/operate-statistical/center-top`,
  } as ISendParam);

export const getRightTop = (id): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/operate-statistical/right-top?algorithmInstanceId=${id.algorithmInstanceId}&algorithmDetailId=${id.algorithmDetailId}`,
  } as ISendParam);

export const getCenterBottom = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/operate-statistical/center-bottom`,
  } as ISendParam);
export const getRightBottom = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/operate-statistical/right-bottom`,
  } as ISendParam);
// 获取算法列表
export const getleftBottomList = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/operate-statistical/left-bottom-list`,
  } as ISendParam);

export const getleftBottom = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/operate-statistical/left-bottom?algorithmInstanceId=${data.algorithmInstanceId}&algorithmDetailId=${data.algorithmDetailId}&hour=${data.hour}`,
  } as ISendParam);

export const get7DayAlarm = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/operate-statistical/trend-of-alarm`,
  } as ISendParam);

export const get7DayAlarmTop = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/operate-statistical/top-alarm`,
  } as ISendParam);

export const getOneDayAlarm = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/operate-statistical/real-time-count?startTime=${data.startTime}&endTime=${data.endTime}`,
  } as ISendParam);

const getUrl = (data, model) => {
  if (model === "channel") {
    return `${baseUrl}/execute-record/all?startTime=${data.startTime}&endTime=${data.endTime}&size=${data.size}&current=${data.current}&channelName=${data.channelName}`;
  }
  if (model === "preset") {
    return `${baseUrl}/execute-record/all?startTime=${data.startTime}&endTime=${data.endTime}&size=${data.size}&current=${data.current}&presetName=${data.presetName}`;
  }
};

export const getVideoUrl = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/channel-info/list-page?size=${data.size}&current=${data.current}`,
  } as ISendParam);

// 更新手动或自动
export const changeMode = (data: { id: string; mode: string }): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/channel-info/switch-mode`,
    data,
  } as ISendParam);

// 更新手动或自动
export const changeAutoOrManual = (data: { channelId: string; mode: string }): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/api-for-dcs/channel/switch-mode`,
    data,
  } as ISendParam);

// 更新手动或自动(DCS)
export const changeModeDCS = (data: { id: string; mode: string }): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/api-for-dcs/switch-mode`,
    data,
  } as ISendParam);

export const getAlarmTable = (data?): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/alarm-record/all?confirm=${data.confirm}&startTime=${data.startTime}&endTime=${data.endTime}&targetId=${data.targetId}&size=${data.size}&current=${data.current}`,
  } as ISendParam);

export const getAlarmTableForDcs = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/api-for-dcs/alarm/page-realtime`,
    data,
  } as ISendParam);

export const getAlarmTableForDcsHis = (data?): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/api-for-dcs/alarm/page-history`,
    data,
  } as ISendParam);

export const getExecuteListForDcs = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/api-for-dcs/algorithm-record/page-all`,
    data,
  } as ISendParam);

export const getPresetListForDcs = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/api-for-dcs/preset-record/page-all`,
    data,
  } as ISendParam);

// 获取错误列表
export const getErrorList = (data?): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/api-for-dcs/alarm/all?startTime=${data.startTime}&endTime=${data.endTime}&size=${data.size}&type=${data.type}&targetId=${data.targetId}&current=${data.current}&source=error`,
  } as ISendParam);

export const comfirmAlarm = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/alarm-record`,
    data,
  } as ISendParam);

export const comfirmAlarmForDcs = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/api-for-dcs/alarm/update`,
    data,
  } as ISendParam);

  export const falseAlarmForDcs = (id): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/api-for-dcs/alarm/false-alarm?id=${id}`,
  } as ISendParam);

export const inspectionRecordList = (data, model): Promise<any> =>
  send({
    method: "get",
    url: getUrl(data, model),
  } as ISendParam);

export const uploadPic = (data: { presetId: string; proxyPath: string }): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/scene-custom-pic/failed-upload`,
    data,
  } as ISendParam);

/**
 * @description 调度监控
 */
export const getTaskList = (name): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/schedule-task/all?taskName=${name}`,
  } as ISendParam);

export const getDCSTaskList = (name): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/api-for-dcs/task/all?taskName=${name}`,
  } as ISendParam);

export const getDCSTaskDetail = (taskId): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/api-for-dcs/task/detail?taskId=${taskId}`,
  } as ISendParam);

export const getTaskNode = (data, isDcs): Promise<any> =>
  send({
    method: "get",
    url: isDcs
      ? `${baseUrl}/api-for-dcs/task/detail?taskId=${data}`
      : `${baseUrl}/schedule-task/task-node/by-task-id?taskId=${data}`,
  } as ISendParam);

// startTime=${data.startTime}&endTime=${data.endTime}&size=${data.size}&current=${data.current}
// mes 暂停调度
export const pauseMesTask = (data: { taskId: string }): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/os/mes/task/pause`,
    data,
  } as ISendParam);

// mes 恢复调度
export const resumeMesTask = (data: { taskId: string }): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/os/mes/task/resume`,
    data,
  } as ISendParam);


