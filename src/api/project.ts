import { reactive } from "vue";
import { send } from "../common/u-http";
import { ISendParam, Irang, Iins, Iout } from "../common/u-http/types";

const baseUrl = (window as any).g.api;
/**
 * 登录控制
 */

// 校验登录状态
export const loginStatus = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/portal/is-login`,
  } as ISendParam);
// 登录
export const userLogin = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/portal/login`,
    data,
  } as ISendParam);

// 修改密码
export const changePassWord = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/system-setting/change-password`,
    data,
  } as ISendParam);
// 推出登录
export const logoutLogin = (): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/portal/logout`,
  } as ISendParam);

// 获取验证码
export const getCaptcha = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/portal/captcha`,
  } as ISendParam);

// 单点登录
// sso登录获取userName
export const getUserName = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/api/hsm/auth/user-info`,
  } as ISendParam);

  // 检查平台登录接口连通性
export const checkDbaseLogin = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/test-network/dbase-login?host=${data}`,
  } as ISendParam);

// 检查平台用户名密钥
export const checkDbaseClientInfo = (dbBaseClientId, dbBaseSecret, dbBaseIp): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/test-network/dbase-clientInfo?dbBaseClientId=${dbBaseClientId}&dbBaseSecret=${dbBaseSecret}&dbBaseIp=${dbBaseIp}`,
  } as ISendParam);

// 检查是否为第一次配置
export const checkFirstConfig = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/common-config/first-config`,
  } as ISendParam);

// 第一次配置完成后，更新配置状态
export const finishFirstConfig = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/common-config/config-finish`, 
  } as ISendParam);

// 检查单点登录接口是否可用
export const needDbaseConfig = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/common-config/dbase`,
  } as ISendParam);  

// 获取登录模式
export const getLoginMode = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/common-config/loginMode`,
  } as ISendParam);  
/**
 * 工程管理
 */

export const getProjectInfo = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/project`,
  });
export const addProject = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/project`,
    data,
  } as ISendParam);

export const editProject = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/project`,
    data,
  } as ISendParam);

export const deleteProject = (data): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/project?idList=${data}`,
  } as ISendParam);

export const getProject = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/project/list-page?size=${data.size}&current=${data.current}`,
  } as ISendParam);

// for DCS
export const getProjectForDCS = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/api-for-dcs/project/list-page?size=${data.size}&current=${data.current}`,
  } as ISendParam);

export const importProject = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/project/import`,
    data,
  } as ISendParam);
// 覆盖导入
export const overimportProject = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/project/overwrite-import`,
    data,
  } as ISendParam);

export const exportProject = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/project/export?projectId=${data}`,
    other: { responseType: "blob" },
  } as ISendParam);

export const saveAllConfig = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/project/save-all`,
    data,
  } as ISendParam);

export const deployOrStop = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/project/deploy-or-stop`,
    data,
  } as ISendParam);

export const onlineOrOffline = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/project/online-or-offline`,
    data,
  } as ISendParam);

export const algorithmModel = (data?): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/algorithm/list?algorithmName=${data}`,
  } as ISendParam);

export const algorithmModelEdit = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/algorithm`,
    data,
  } as ISendParam);

export const algorithmModelAdd = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/algorithm`,
    data,
  } as ISendParam);
export const algorithmModelDel = (data): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/algorithm?algorithmId=${data}`,
  } as ISendParam);
export const ModelAdd = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/algorithm/model`,
    data,
  } as ISendParam);
export const ModelUpdate = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/algorithm/model`,
    data,
  } as ISendParam);
export const ModelDel = (data): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/algorithm/model?modelId=${data}`,
  } as ISendParam);
export const listModel = (data?): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/algorithm/list-model?size=${data.size}&current=${data.current}&modelName=${data.modelName}`,
  } as ISendParam);

// 点项监控获取全局变量列表
export const monitorList = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/preset-info/list-all-output`,
    // url: `${baseUrl}/global-variable/listener-variable`,
    data,
  } as ISendParam);

/**
 * @description 配置
 */
/**
 * 调度配置
 */
// 获取巡检任务列表
export const getTaskList = (name): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/schedule-task/all?taskName=${name}`,
  } as ISendParam);

export const getTaskTreeInterface = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/schedule-task/tree`,
  } as ISendParam);

export const getMesTaskTreeInterface = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/os/mes/task/tree`,
  } as ISendParam);

// 通过任务ID获取该任务的巡检点信息
export const getThisTaskPoint = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/schedule-task/task-detail?taskId=${data}`,
  } as ISendParam);

export const getMesThisTaskPoint = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/os/mes/task/detail?taskId=${data}`,
  } as ISendParam);
// 新增巡检任务
export const addTask = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/schedule-task/tree/node`,
    data,
  } as ISendParam);
// 编辑巡检任务名称
export const editTaskName = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/schedule-task/tree/node`,
    data,
  } as ISendParam);
// 编辑巡检详情
export const editTask = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/schedule-task/detail`,
    data,
  } as ISendParam);
// 删除巡检任务
export const deleteTask = (data): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/schedule-task/tree/node?nodeId=${data}`,
  } as ISendParam);
// 查询全部巡检点
export const getTaskPoint = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/schedule-task/task-node/all`,
  } as ISendParam);
/**
 * 时间配置
 */
export const getSystemTime = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/npt-manage/date-now`,
  } as ISendParam);
// 设置时间
export const setComputerTime = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/npt-manage/system-date`,
    data,
  } as ISendParam);
// NPT
export const getNPT = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/npt-manage/ntp-info`,
  } as ISendParam);
export const setNPT = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/npt-manage/ntp-info`,
    data,
  } as ISendParam);
// 通知配置
export const getNoticeList = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/notice-config/list`,
  } as ISendParam);
export const saveNotice = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/notice-config`,
    data,
  } as ISendParam);
/**
 * 报警配置
 */
export const getAllAlarm = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/alarm-config/list-by-preset`,
    data,
  } as ISendParam);

export const saveAllAlarm = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/alarm-config`,
    data,
  } as ISendParam);

/**
 * 全局配置
 */

export const getOverConf = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/common-config`,
  } as ISendParam);
export const saveOverConf = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/common-config`,
    data,
  } as ISendParam);

export const validateConf = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/common-config/validate`,
  } as ISendParam);

/**
 * 点项配置
 */
// 绑定树结构
export const getBindTree = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/global-variable/output-param-tree`,
  } as ISendParam);

// 绑定算法输出
export const bindOutputParam = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/global-variable/bind-output-param`,
    data,
  } as ISendParam);

// 取消绑定算法输出
export const unbindOutputParam = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/global-variable/unbind-output-param`,
    data,
  } as ISendParam);

// 添加修改
export const changeAddOrEdit = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/global-variable/bind`,
    data,
  } as ISendParam);

// 删除
export const unBinding = (data): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/global-variable/un-bind?id=${data}`,
  } as ISendParam);

// 获取点项列表
export const pointList = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/global-variable/page-all`,
    data,
  } as ISendParam);

// 添加变量
export const changeVariable = (data, type): Promise<any> =>
  send({
    method: type,
    url: `${baseUrl}/global-variable`,
    data,
  } as ISendParam);

//删除变量
export const deleteVariable = (data): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/global-variable?globalVariableId=${data}`,
  } as ISendParam);
/**
 * @description 控制流
 */
// export const getControlFlow = (): Promise<any> =>
//   send({
//     method: "get",
//     url: `${baseUrl}/project-tree-node/control-url`,
//   } as ISendParam);

/**
 * @description 接口配置
 */

// 获取应用列表
export const getInterfaceList = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/third-app/list-page?size=${data.size}&current=${data.current}&appName=${data.name}`,
  } as ISendParam);

// 添加应用
export const addInterface = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/third-app`,
    data,
  } as ISendParam);

// 编辑应用
export const editInterface = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/third-app`,
    data,
  } as ISendParam);

// 删除应用
export const delInterface = (data): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/third-app?idList=${data}`,
  } as ISendParam);

// 获取第三方接口
export const getThirdInterFace = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/third-app/list-interface`,
  } as ISendParam);

// 绑定第三方接口
export const bindInterface = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/third-app/bind-interfaces`,
    data,
  } as ISendParam);

// 检查绑定第三方接口
export const checkBindInterface = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/third-app/list-bind-interface?appId=${data}`,
  } as ISendParam);

// 更新启用
export const appAvail = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/third-app/switch-available`,
    data,
  } as ISendParam);

/**
 * @description 日志
 */
// 操作日志
export const opList = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/operate-log/list-page?size=${data.size}&current=${data.current}&startTime=${data.startTime}&endTime=${data.endTime}&searchText=${data.searchText}`,
  } as ISendParam);

/**
 * @description 故障图片管理
 */
export const getAllClassifyList = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/classify-config/all`,
  } as ISendParam);

export const addClassifyList = (data: { name: string }): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/classify-config`,
    data,
  } as ISendParam);

export const deleteClassifyList = (data): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/classify-config?classifyConfigId=${data}`,
  } as ISendParam);

export const uploadImgToClassify = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/classify-config/import-images`,
    data,
  } as ISendParam);

export const deleteImage = (data): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/classify-config/delete-image?classifyConfigId=${data.id}&imageUrl=${data.url}`,
  } as ISendParam);

/**
 * hik通道配置
 */
// 获取hik通道列表
export const getHikChannelList = (data): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/hik-channel-info/all?size=${data.size}&current=${data.current}`,
  } as ISendParam);

// 添加hik通道算法
export const addHikChannelAl = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/hik-channel-info/logic/save`,
    data,
  } as ISendParam);

// 编辑hik通道算法
export const editHikChannelAlgorithm = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/hik-channel-info/logic/batch`,
    data,
  } as ISendParam);

// 删除hik通道算法
export const deleteHikChannelAlgorithm = (data): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/hik-channel-info/algorithm?id=${data.id}&srcIndex=${data.srcIndex}`,
  } as ISendParam);

export const deleteHikChannel = (id): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/hik-channel-info?srcIndexs=${id}`,
  } as ISendParam);

export const syncHik = (): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/hik-channel-info/sync`,
  } as ISendParam);

// hik点项配置
export const setPointConf = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/hik-channel-info/update/point`,
    data,
  } as ISendParam);

// 订阅HIK
export const hikSubscribeList = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/hik-channel-info/subscribe`,
  } as ISendParam);

export const hikSubscribe = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/hik-channel-info/subscribe`,
    data,
  } as ISendParam);

// 算法
// 获取算法列表
export const getHikAl = (params): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/hik-logic-info?size=${params.size}&current=${params.current}`,
  } as ISendParam);

// 圣瞳同步算法
export const shengTongSyncAl = (): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/hik-logic-info`,
  } as ISendParam);

// 添加算法
export const addHikAl = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/hik-logic-info`,
    data,
  } as ISendParam);
// 删除算法
export const deleteHikAl = (data): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/hik-logic-info?idList=${data}`,
  } as ISendParam);

// 编辑算法
export const editHikAl = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/hik-logic-info`,
    data,
  } as ISendParam);

// 规则
export const addRule = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/api/v1/inspection/alarm/rule`,
    data,
  } as ISendParam);

export const deleteRule = (id): Promise<any> =>
  send({
    method: "delete",
    url: `${baseUrl}/api/v1/inspection/alarm/rule?id=${id}`,
  } as ISendParam);

export const editRuleJson = (data): Promise<any> =>
  send({
    method: "put",
    url: `${baseUrl}/api/v1/inspection/alarm/rule`,
    data,
  } as ISendParam);
// 查看证书授权信息
export const getAuthorizationInfo = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/license/authorization-info`,
  });

// 上传证书文件
export const updateLicenseFile = (data): Promise<any> =>
  send({
    method: "post",
    url: `${baseUrl}/license/upload-license-file`,
    data,
  } as ISendParam);

// 校验证书
export const checkAuthorization = (): Promise<any> =>
  send({
    method: "get",
    url: `${baseUrl}/license/check-license`,
  });
