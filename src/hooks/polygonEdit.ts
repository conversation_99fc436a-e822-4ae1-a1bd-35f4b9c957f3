import { fabric } from "fabric";

const drawObject = (canvas: fabric.Canvas, pointsCoordinate: any) => {
  // 初始图形位置
console.log(pointsCoordinate, "pointsCoordinate");
  pointsCoordinate.forEach((point, index) => {
    const pointObj = new fabric.Circle({
      left: point.x, // 距离左边的距离
      top: point.y, // 距离上边的距离
      fill: "blue", // 填充的颜色
      originX: "center",
      originY: "center",
      radius: 5, // 点的大小
      hasControls: false,
      hasBorders: false,
      objectCaching: false,
      cid: `circle-${index}`, // 自定义属性
    });
    canvas.add(pointObj);
    canvas.renderAll();
  });

  const polygon = new fabric.Polygon(pointsCoordinate, {
    fill: "",
    strokeWidth: 3,
    stroke: "red",
    objectCaching: false,
    transparentCorners: false,
    cornerColor: "blue",
    hasControls: false,
    hasBorders: false,
  });

  canvas.add(polygon);
  canvas.sendToBack(polygon);
  canvas.renderAll();
  return polygon;
};

const newPolygon = (canvas: fabric.Canvas, pointsCoordinate: any) => {
  const polygon = new fabric.Polygon(pointsCoordinate, {
    fill: "",
    strokeWidth: 3,
    stroke: "red",
    objectCaching: false,
    transparentCorners: false,
    cornerColor: "blue",
    hasControls: false,
    hasBorders: false,
  });

  canvas.add(polygon);
  canvas.sendToBack(polygon);
  canvas.renderAll();
  return polygon;
};

const initPolygon = (canvasRef, pointsCoordinate, getPersCb: Function) => {
  let canvas = new fabric.Canvas(canvasRef, {
    selection: false,
  }) as fabric.Canvas;

  // 新建一个图形
  drawObject(canvas, pointsCoordinate);
  function moveLimit(moveObj) {
    moveObj.setCoords();
    console.log(moveObj.getBoundingRect());
    if (moveObj.left !== undefined) {
      if (moveObj.getBoundingRect().left < 0) {
        // moveObj.left = 0
        moveObj.left = Math.max(moveObj.left, moveObj.left - moveObj.getBoundingRect().left);
      } else if (
        moveObj.getBoundingRect().left + moveObj.getBoundingRect().width >
        canvas.getWidth()
      ) {
        // moveObj.left = canvas.getWidth() - 200;
        moveObj.left = Math.min(
          moveObj.left,
          canvas.getWidth() -
            moveObj.getBoundingRect().width +
            moveObj.left -
            moveObj.getBoundingRect().left
        );
      }
    }

    if (moveObj.top !== undefined) {
      if (moveObj.getBoundingRect().top < 0) {
        // moveObj.top = 0;
        moveObj.top = Math.max(moveObj.top, moveObj.top - moveObj.getBoundingRect().top);
      } else if (
        moveObj.getBoundingRect().top + moveObj.getBoundingRect().height >
        canvas.getHeight()
      ) {
        // moveObj.top = canvas.getHeight() - 200;
        moveObj.top = Math.min(
          moveObj.top,
          canvas.getHeight() -
            moveObj.getBoundingRect().height +
            moveObj.top -
            moveObj.getBoundingRect().top
        );
      }
    }
  }

  canvas.on("object:moving", (event: any) => {
    // 阻止对象移动到画布之外
    let moveObj = event.target as fabric.Object;
    moveLimit(moveObj);

    if (moveObj.type === "circle") {
      // 如果存在自定义属性和指定值就执行以下代码（本例主要筛选出刚刚创建的几个圆形）
      if ("cid" in moveObj && moveObj.cid.match(RegExp(/circle-/))) {
        const polygon = canvas.getObjects().find((obj) => obj.type === "polygon");
        // 通过cid可以判断出当前操作的是哪个圆形，并且圆形的cid最后一位对应多边形指定的顶点
        let index = moveObj.cid.split("-")[1];
        // // 获取多边形定点
        let points = polygon.points;
        // // 修改指定点顶点的x坐标
        points[index].x = moveObj.left;
        // 修改指定定点的y坐标
        points[index].y = moveObj.top;
        // 刷新画布
        polygon.setCoords();
        canvas.renderAll();
      }
    }
    // point移动同步

    if (moveObj.type === "polygon") {
      const POLYGON = moveObj;
      var matrix = POLYGON.calcTransformMatrix();
      var transformedPoints = POLYGON.get("points")
        .map(function (p) {
          return new fabric.Point(p.x - POLYGON.pathOffset.x, p.y - POLYGON.pathOffset.y);
        })
        .map(function (p) {
          return fabric.util.transformPoint(p, matrix);
        });
      const points = canvas.getObjects().filter((obj) => obj.type === "circle");
      points.forEach((point, index) => {
        const nowPoint = transformedPoints[index];
        point.set({
          left: nowPoint.x,
          top: nowPoint.y,
        });
      });

      points.forEach((point, index) => {
        point.setCoords();
      });

      // 重新生成
      // canvas.remove(POLYGON);
      // newPolygon(canvas, transformedPoints);

      canvas.renderAll();
    }
  });

  canvas.on("mouse:up", (event: any) => {
    if (!event.target) return;
    if (event.target.type === "circle") {
      // 圆形移动结束后，更新多边形顶点坐标
      const polygon = canvas.getObjects().find((obj) => obj.type === "polygon");
      const points = canvas
        .getObjects()
        .filter((obj) => obj.type === "circle")
        .map((res) => {
          return {
            x: res.left,
            y: res.top,
          };
        });
      canvas.remove(polygon);
      newPolygon(canvas, points);

      getPersCb();
    }
    if (event.target.type === "polygon") {
      const POLYGON = event.target;
      var matrix = POLYGON.calcTransformMatrix();
      var transformedPoints = POLYGON.get("points")
        .map(function (p) {
          return new fabric.Point(p.x - POLYGON.pathOffset.x, p.y - POLYGON.pathOffset.y);
        })
        .map(function (p) {
          return fabric.util.transformPoint(p, matrix);
        });
      canvas.remove(POLYGON);
      newPolygon(canvas, transformedPoints);
      getPersCb();
    }
    // const polygon = event.target;

    // var matrix = polygon.calcTransformMatrix();
    // var transformedPoints = polygon
    //   .get("points")
    //   .map(function (p) {
    //     return new fabric.Point(p.x - polygon.pathOffset.x, p.y - polygon.pathOffset.y);
    //   })
    //   .map(function (p) {+
    //     return fabric.util.transformPoint(p, matrix);
    //   });
    // polygon.set("points", transformedPoints);
    // polygon.get("points").forEach((point, index) => {
    //   const absolutePoint = fabric.util.transformPoint(
    //     {
    //       x: polygon.points[index].x - polygon.pathOffset.x,
    //       y: polygon.points[index].y - polygon.pathOffset.y,
    //     },
    //     polygon.calcTransformMatrix()
    //   );
    //   const polygonBaseSize = getObjectSizeWithStroke(polygon);
    //   const newX = (polygon.points[index].x - polygon.pathOffset.x) / polygonBaseSize.x;
    //   const newY = (polygon.points[index].y - polygon.pathOffset.y) / polygonBaseSize.y;
    //   console.log(newX, newY, absolutePoint);
    //   polygon.setPositionByOrigin(absolutePoint, newX + 0.5, newY + 0.5);
    // });

    // polygon.setCoords();
    // canvas.renderAll();
    // console.log(polygon.get("points"));
  });

  // clear
  const clear = () => {
    canvas.clear();
    canvas = null;
  };

  const getObject = () => {
    console.log(canvas.getObjects());
  };

  return {
    canvas,
    clear,
    getObject,
  };
};
export default initPolygon;
