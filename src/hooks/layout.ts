/**
 * @description 拖拽布局hook
 * <AUTHOR>
 */
import {
  computed, ref, watch, reactive, onMounted,
} from 'vue';
import { useStore } from 'vuex';

const layout = () => {
  const store = useStore();
  const zIndex = computed(() => store.state.design.zIndex);
  const triggerHeight = ref(false);
  let dragDom: HTMLQuoteElement = null;
  const dragName = ref('');
  const hideAll = ref(false);
  const leftShow = ref(false);
  const topShow = ref(false);
  const bottomShow = ref(false);
  const rightShow = ref(false);
  const sidrBeforeHide = ref(true);
  const configBeforeHide = ref(true);
  const siderInit = reactive({
    w: 355,
    h: 0,
    x: 0,
    y: -1,
    isDefault: true,
  });

  const configInit = reactive({
    w: 400,
    h: 0,
    x: 0,
    y: -1,
    isDefault: true,
  });
  const panelInit = reactive({
    w: 0,
    h: 40,
    x: 0,
    y: -1,
    isDefault: true,
  });
  const logInit = reactive({
    w: 0,
    h: 208,
    x: 0,
    y: 0,
    isDefault: true,
  });
  // 设置实际值，用来计算拖拽 log panel时，受到别的组件影响
  const siderData = reactive({
    w: 0,
    h: 0,
    x: 0,
    y: 0,
    draggable: false,
    show: true,
    disabledH: false,
  });
  const configData = reactive({
    w: 0,
    h: 0,
    x: 0,
    y: 0,
    draggable: false,
    show: true,
    disabledH: false,
  });
  const panelData = reactive({
    w: 0,
    h: 0,
    x: 0,
    y: 0,
    draggable: false,
    show: true,
  });
  const logData = reactive({
    w: 0,
    h: 0,
    x: 0,
    y: 0,
    draggable: false,
    show: true,
  });
  const initDom = () => {
    const content: HTMLQuoteElement = document.querySelector('.jcontent');
    // 110 200
    const siderWidth = siderInit.w;
    const configWidth = configInit.w;
    const bodyHeight = document.body.offsetHeight;
    const contentHeight = triggerHeight.value ? bodyHeight : bodyHeight-150;
    const contentWidth = content.clientWidth;
    siderInit.h = contentHeight;
    logInit.x = siderWidth;
    logInit.y = contentHeight - logInit.h + 47;
    logInit.w = contentWidth - siderWidth - configWidth;
    panelInit.x = siderWidth - 2;
    configInit.h = contentHeight;
    panelInit.w = contentWidth - siderWidth - configWidth;
    configInit.x = siderWidth + panelInit.w - 4;
    siderInit.isDefault = true;
    logInit.isDefault = true;
    configInit.isDefault = true;
    panelInit.isDefault = true;

    siderData.x = siderInit.x;
    siderData.y = siderInit.y;
    siderData.w = siderInit.w;
    siderData.h = siderInit.h - 44;

    configData.x = configInit.x;
    configData.y = configInit.y;
    configData.w = configInit.w;
    configData.h = configInit.h - 44;

    logData.x = logInit.x;
    logData.y = logInit.y;
    logData.w = logInit.w;
    logData.h = logInit.h;

    panelData.x = panelInit.x;
    panelData.y = panelInit.y;
    panelData.w = panelInit.w;
    panelData.h = panelInit.h;
  };

  const triggercomp = (trigger) => {
    if (trigger) {
      siderInit.h += 90;
      configInit.h += 90;
      logInit.y += 90;
    } else {
      siderInit.h -= 90;
      configInit.h -= 90;
      logInit.y -= 90;
    }
    if (siderInit.isDefault) {
      siderData.h = siderInit.h;
    }
    if (configInit.isDefault) {
      configData.h = configInit.h;
    }
    if (logInit.isDefault) {
      logData.y = logInit.y;
    }
  };

  // setTimeout(() => {
  //   initDom();
  // }, 6000);
  // eslint-disable-next-line @typescript-eslint/no-unused-vars
  const panelLogwidth = (sider, config) => {
    const content: HTMLQuoteElement = document.querySelector('.jcontent');
    const contentWidth = content.clientWidth;
    if (config.isDefault && sider.isDefault) {
      return contentWidth - sider.w - config.w;
    }
    if (config.isDefault && !sider.isDefault) {
      console.log('adada2')
      return contentWidth - config.w;
    }
    if (sider.isDefault && !config.isDefault) {
      return contentWidth - sider.w;
    }
    return contentWidth;
  };
  const panelLogthLeft = (sider, config) => {
    if (config.isDefault && sider.isDefault) {
      return sider.w - 2;
    }
    if (config.isDefault && !sider.isDefault) {
      return 0;
    }
    if (sider.isDefault && !config.isDefault) {
      return sider.w - 2;
    }
    return 0;
  };

  const siderPlace = () => {
    if (siderData.x === 0.1) {
      siderData.x = 0;
      siderData.y = -1;
      siderData.w = siderInit.w - 0.1;
      siderData.h = siderInit.h - 0.1;
    } else {
      siderData.x = 0.1;
      siderData.y = -1.1;
      siderData.w = siderInit.w + 0.1;
      siderData.h = siderInit.h + 0.1;
    }
    siderInit.isDefault = true;
    compPanelLog();
  };
  const compPanelLog = () => {
    // 会影响在默认区的 panel和log ;
    // 同时影响 吸附区
    panelInit.w = panelLogwidth(siderInit, configInit);
    panelInit.x = panelLogthLeft(siderInit, configInit);
    if (panelInit.isDefault) {
      panelData.w = panelInit.w;
      panelData.x = panelInit.x;
    }
    logInit.w = panelLogwidth(siderInit, configInit);
    logInit.x = panelLogthLeft(siderInit, configInit);
    if (logInit.isDefault) {
      logData.w = logInit.w;
      logData.x = logInit.x;
    }
  };
  const siderInjection = (dragContent: HTMLQuoteElement) => {
    if (!dragContent) return;
    const { left, top, height } = dragContent.style;
    const leftVal = parseInt(left.split('px')[0]);
    const topVal = parseInt(top.split('px')[0]);
    const heightVal = parseInt(height.split('px')[0]);
    if (
      leftVal <= siderInit.w
      && ((topVal >= -heightVal && topVal >= 0) || (heightVal + topVal >= 0 && topVal < 0))
    ) {
      siderPlace();
    } else {
      siderInit.isDefault = false;
    }
    compPanelLog();
  };
  const showHideSider = (value = null) => {
    siderData.show = value !== null ? value : !siderData.show;
    if (!siderData.show) {
      // 记录隐藏前是否在默认区
      sidrBeforeHide.value = siderInit.isDefault;
      siderInit.isDefault = false;
      compPanelLog();
    } else {
      siderInit.isDefault = sidrBeforeHide.value;
      compPanelLog();
    }
  };
  const panelPlace = () => {
    if (panelData.y === panelInit.y + 0.1) {
      panelData.x = panelInit.x - 1;
      panelData.y = panelInit.y - 0.1;
      panelData.w = panelInit.w - 0.1;
      panelData.h = panelInit.h - 0.1;
    } else {
      panelData.x = panelInit.x + 1;
      panelData.y = panelInit.y + 0.1;
      panelData.w = panelInit.w + 0.1;
      panelData.h = panelInit.h + 0.1;
    }
    panelInit.isDefault = true;
  };
  const panelInjection = (dragContent: HTMLQuoteElement) => {
    if (!dragContent) return;
    const { top, height } = dragContent.style;
    const topVal = parseInt(top.split('px')[0]);
    const heightVal = parseInt(height.split('px')[0]);
    if (topVal <= heightVal && topVal >= -heightVal) {
      panelPlace();
    } else {
      panelInit.isDefault = false;
    }
  };
  const showHidePanel = (value = null) => {
    panelData.show = value !== null ? value : !panelData.show;
    // if (!panelData.show) {
    //   panelInit.isDefault = false;
    // } else {
    //   panelInit.isDefault = true;
    // }
  };
  const logPlace = () => {
    if (logData.y === logInit.y + 0.1) {
      logData.x = logInit.x - 0.1;
      logData.y = logInit.y - 0.1;
      logData.w = logInit.w - 0.1;
      logData.h = logInit.h - 0.1;
    } else {
      logData.x = logInit.x + 0.1;
      logData.y = logInit.y + 0.1;
      logData.w = logInit.w + 0.1;
      logData.h = logInit.h + 0.1;
    }
    logInit.isDefault = true;
  };
  const logInjection = (dragContent: HTMLQuoteElement) => {
    if (!dragContent) return;
    const { top, height } = dragContent.style;
    const topVal = parseInt(top.split('px')[0]);
    const heightVal = parseInt(height.split('px')[0]);
    if (topVal >= logInit.y - heightVal) {
      logPlace();
    } else {
      logInit.isDefault = false;
    }
  };
  const showHideLog = () => {
    logData.show = !logData.show;
    // if (!logData.show) {
    //   logInit.isDefault = false;
    // } else {
    //   logInit.isDefault = true;
    // }
  };
  const configPlace = () => {
    if (configData.y === 0.1) {
      configData.x = configInit.x - 0.1;
      configData.y = -1;
      configData.w = configInit.w - 0.1;
      configData.h = configInit.h - 0.1;
    } else {
      configData.x = configInit.x + 0.1;
      configData.y = -1.1;
      configData.w = configInit.w + 0.1;
      configData.h = configInit.h + 0.1;
    }
    configInit.isDefault = true;
  };
  const configjection = (dragContent: HTMLQuoteElement) => {
    if (!dragContent) return;
    const {
      left, top, height, width,
    } = dragContent.style;
    const leftVal = parseInt(left.split('px')[0]);
    const topVal = parseInt(top.split('px')[0]);
    const heightVal = parseInt(height.split('px')[0]);
    const widthVal = parseInt(width.split('px')[0]);
    if (
      leftVal + widthVal >= configInit.x
      && ((topVal >= -heightVal && topVal >= 0) || (heightVal + topVal >= 0 && topVal < 0))
    ) {
      configPlace();
    } else {
      configInit.isDefault = false;
    }
    compPanelLog();
  };
  const showHideConfig = (status:boolean) => {
    configData.show = status;
    if (!configData.show) {
      // 记录隐藏前是否在默认区
      configBeforeHide.value = configInit.isDefault;
      configInit.isDefault = false;
      compPanelLog();
    } else {
      configInit.isDefault = configBeforeHide.value;
      compPanelLog();
    }
  };
  const siderWatch = (dragContent: HTMLQuoteElement) => {
    const { left } = dragContent.style;
    const leftVal = parseInt(left.split('px')[0]);
    if (leftVal <= siderInit.w + 20) {
      leftShow.value = true;
    } else {
      leftShow.value = false;
    }
  };
  const configWatch = (dragContent: HTMLQuoteElement) => {
    const { left, width } = dragContent.style;
    const leftVal = parseInt(left.split('px')[0]);
    const widthVal = parseInt(width.split('px')[0]);
    if (leftVal + widthVal >= configInit.x - 20) {
      rightShow.value = true;
    } else {
      rightShow.value = false;
    }
  };
  const panelWatch = (dragContent: HTMLQuoteElement) => {
    const { top } = dragContent.style;
    const topVal = parseInt(top.split('px')[0]);
    if (topVal <= panelInit.h + 20) {
      topShow.value = true;
    } else {
      topShow.value = false;
    }
  };
  const logWatch = (dragContent: HTMLQuoteElement) => {
    const { top, height } = dragContent.style;
    const topVal = parseInt(top.split('px')[0]);
    const heightVal = parseInt(height.split('px')[0]);
    if (topVal >= logInit.y - heightVal - 20) {
      bottomShow.value = true;
    } else {
      bottomShow.value = false;
    }
  };

  const siderResize = (data: any) => {
    data.isDefault = siderInit.isDefault;
    // 不在默认区，不影响任何元素
    if (!data.isDefault) return;
    const width = panelLogwidth(data, configInit);
    // 会影响在默认区的 panel和log ; 影响默认位置和实际位置 同时高度不变
    siderData.disabledH = true;
    if (panelInit.isDefault) {
      panelData.x = data.w - 2;
      panelData.w = width;
    }
    panelInit.x = data.w;
    panelInit.w = width;
    if (logInit.isDefault) {
      logData.x = data.w - 2;
      logData.w = width;
    }
    logInit.x = data.w;
    logInit.w = width;
  };
  const configResize = (data: any) => {
    data.isDefault = configInit.isDefault;
    // 不在默认区，不影响任何元素
    if (!data.isDefault) return;
    const width = panelLogwidth(siderInit, data);
    // 会影响在默认区的 panel和log ; 影响默认位置和实际位置 同时高度不变
    configData.disabledH = true;
    if (panelInit.isDefault) {
      panelData.w = width;
    }
    panelInit.w = width;
    if (logInit.isDefault) {
      logData.w = width;
    }
    logInit.w = width;
  };
  const layoutDraging = () => {
    const dragContent: HTMLQuoteElement = document.querySelector('.dragging');
    dragDom = dragContent;
    dragContent.style.zIndex = zIndex.value;
    if (dragName.value === 'sider') {
      siderWatch(dragDom);
    } else if (dragName.value === 'panel') {
      panelWatch(dragDom);
    } else if (dragName.value === 'log') {
      logWatch(dragDom);
    } else if (dragName.value === 'config') {
      configWatch(dragDom);
    }
  };
  const layoutDragEnd = (data) => {
    if (!dragDom) return;
    if (dragName.value === 'sider') {
      siderInjection(dragDom);
    } else if (dragName.value === 'panel') {
      panelInjection(dragDom);
    } else if (dragName.value === 'log') {
      logInjection(dragDom);
    } else if (dragName.value === 'config') {
      configjection(dragDom);
    }
    dragDom = null;
    store.commit('setzIndex');
    dragName.value = '';
    leftShow.value = false;
    topShow.value = false;
    bottomShow.value = false;
    rightShow.value = false;
  };
  const layoutDragStrat = (data: string) => {
    dragName.value = data;
  };
  const layoutResizing = (data: any) => {
    if (dragName.value === 'sider') {
      siderResize(data);
    } else if (dragName.value === 'panel') {
      // panelWatch(dragDom);
    } else if (dragName.value === 'log') {
      // logWatch(dragDom);
      resizeLogDiv(data);
    } else if (dragName.value === 'config') {
      // configWatch(dragDom);
      configResize(data);
    }
  };
  const layoutResizeEnd = (data) => {
    if (dragName.value === 'sider') {
      siderData.disabledH = false;
      // siderData = data;
    } else if (dragName.value === 'panel') {
      // panelWatch(dragDom);
    } else if (dragName.value === 'log') {
      // logWatch(dragDom);
    } else if (dragName.value === 'config') {
      // configWatch(dragDom);
      configData.disabledH = false;
    }
    dragName.value = '';
  };
  const dragSwitch = (data) => {
    data.draggable = !data.draggable;
  };
  // 所有窗口
  const showHideAll = () => {
    if (hideAll.value) {
      siderData.show = false;
      configData.show = false;
      panelData.show = false;
      logData.show = false;
    } else {
      siderData.show = true;
      configData.show = true;
      panelData.show = true;
      logData.show = true;
    }
    showHideSider();
    // showHideConfig();
    showHideLog();
    showHidePanel();
    hideAll.value = !hideAll.value;
  };
  // 恢复视图
  const recover = () => {
    siderData.draggable = false;
    configData.draggable = false;
    panelData.draggable = false;
    logData.draggable = false;

    siderData.show = true;
    configData.show = true;
    panelData.show = true;
    logData.show = true;
    hideAll.value = false;
    siderPlace();
    configPlace();
    compPanelLog();
    panelPlace();
    logPlace();
  };
  const resizeDomChange = () => {
    const content: HTMLQuoteElement = document.querySelector('.jcontent');
    const otherContnet: HTMLQuoteElement = document.querySelector('.otherContnet');
    const contentHeight = document.body.offsetHeight;
    const contentWidth = content.clientWidth;
    otherContnet.style.width = `${contentWidth}px`;
    otherContnet.style.height = `${contentHeight}px`;
    initDom();
  };

  const resizeLogDiv = (data) => {
    // const logIds = ['project_log_div', 'run_info_div', 'history_info_div', 'console_log_div'];
    const logIds = ['project_log_div', 'run_info_div', 'history_info_div'];
    for (let i = 0; i < logIds.length; i++) {
      const logDom: HTMLQuoteElement = document.querySelector(`#${logIds[i]}`);
      if (logDom && (logDom.parentNode as HTMLQuoteElement).style.display !== 'none') {
        logDom.style.maxHeight = `${data.h * 0.9}px`;
        store.commit('setLogInit', data.h * 0.9);
        break;
      }
      // eslint-disable-next-line no-continue
      continue;
    }
  };

  onMounted(() => {
    resizeDomChange();
  });
  return {
    leftShow,
    siderInit,
    siderData,
    configInit,
    configData,
    panelInit,
    logInit,
    logData,
    panelData,
    topShow,
    bottomShow,
    rightShow,
    layoutDraging,
    layoutDragEnd,
    layoutDragStrat,
    layoutResizing,
    layoutResizeEnd,
    dragSwitch,
    recover,
    showHideSider,
    showHidePanel,
    showHideLog,
    showHideConfig,
    triggercomp,
    showHideAll,
    resizeDomChange,
  };
};

export default layout;
