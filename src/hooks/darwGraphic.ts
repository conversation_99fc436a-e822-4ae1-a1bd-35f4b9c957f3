/**
 * @description 绘制图形hook
 * <AUTHOR>
 */

import {
  computed, ref, watch, reactive, onMounted,
} from 'vue';
import { fabric } from 'fabric';
import { useStore } from 'vuex';

const darWGraphic = (canvasId) => {
  const canvas = new fabric.Canvas(canvasId);
  /**
   * @description 生成多边形
   */
  const doDrawing = ref<boolean>(false); // 多边形绘制状态
  const polygonMode = ref<boolean>(false); // 是否是多边形模型
  const pointArray = ref<any[]>([]); // 多边形点的集合
  const lineArray = ref<any[]>([]); // 多边形线的集合
  const activeShape = ref<any>(false); // 添加预览图形
  const activeLine = ref<any>(''); // 暂时留存绘制的线
  const line = ref<any>({}); // 暂时留存的线
  const drawType = ref<string>(''); // 绘制图形的类型
  const mouseFrom = ref<any>({}); // 图形起始点
  const mouseTo = ref<any>({}); // 图形终点
  // 绘制多边形
  const drawPolygon = () => {
    console.log(canvas, '33333');
    drawType.value = 'polygon';
    polygonMode.value = true;
    canvas.isDrawingMode = false;
  };
  // 获取鼠标点击的坐标
  const transformMouse = (mouseX, mouseY) => ({ x: mouseX, y: mouseY });
  // 生成多边形
  const generatePolygon = () => {
    const points = [];
    // eslint-disable-next-line array-callback-return
    pointArray.value.forEach((point, index) => {
      points.push({
        x: point.left,
        y: point.top,
      });
      canvas.remove(point);
    });
    // eslint-disable-next-line array-callback-return
    lineArray.value.forEach((lines, index) => {
      canvas.remove(lines);
    });
    canvas.remove(activeShape.value).remove(activeLine.value);
    const polygon = new fabric.Polygon(points, {
      stroke: '#E34F51',
      strokeWidth: 2,
      fill: 'rgba(255, 255, 255, 0)',
      opacity: 1,
      hasBorders: true,
      hasControls: false,
    });
    canvas.add(polygon);
    activeLine.value = false;
    activeShape.value = null;
    polygonMode.value = false;
    doDrawing.value = false;
    drawType.value = '';
    line.value = {};
    pointArray.value = [];
    lineArray.value = [];
    // canvas.isDrawingMode = true;
  };

  // 添加多边形的点
  const addPoint = (e) => {
    console.log(e, 'adada');
    const random = Math.floor(Math.random() * 10000);
    const id = new Date().getTime() + random;
    const circle = new fabric.Circle({
      radius: 5,
      fill: '#ffffff',
      stroke: '#333333',
      strokeWidth: 0.5,
      left: (e.pointer.x || e.e.layerX) / canvas.getZoom(),
      top: (e.pointer.y || e.e.layerY) / canvas.getZoom(),
      selectable: false,
      hasBorders: false,
      hasControls: false,
      originX: 'center',
      originY: 'center',
      id,
      objectCaching: false,
    });
    if (pointArray.value.length === 0) {
      circle.set({
        fill: 'red',
      });
    }
    const points = [
      (e.pointer.x || e.e.layerX) / canvas.getZoom(),
      (e.pointer.y || e.e.layerY) / canvas.getZoom(),
      (e.pointer.x || e.e.layerX) / canvas.getZoom(),
      (e.pointer.y || e.e.layerY) / canvas.getZoom(),
    ];

    line.value = new fabric.Line(points, {
      strokeWidth: 2,
      fill: '#999999',
      stroke: '#999999',
      class: 'line',
      originX: 'center',
      originY: 'center',
      selectable: false,
      hasBorders: false,
      hasControls: false,
      evented: false,

      objectCaching: false,
    });

    if (activeShape.value) {
      const pos = canvas.getPointer(e.e);
      const pointss = activeShape.value.get('points');
      pointss.push({
        x: pos.x,
        y: pos.y,
      });
      const polygon = new fabric.Polygon(pointss, {
        stroke: '#333333',
        strokeWidth: 1,
        fill: '#cccccc',
        opacity: 0.3,

        selectable: false,
        hasBorders: false,
        hasControls: false,
        evented: false,
        objectCaching: false,
      });
      canvas.remove(activeShape.value);
      canvas.add(polygon);
      activeShape.value = polygon;
      canvas.renderAll();
    } else {
      const polyPoint = [
        {
          x: (e.pointer.x || e.e.layerX) / canvas.getZoom(),
          y: (e.pointer.y || e.e.layerY) / canvas.getZoom(),
        },
      ];
      const polygon = new fabric.Polygon(polyPoint, {
        stroke: '#333333',
        strokeWidth: 1,
        fill: '#cccccc',
        opacity: 0.3,

        selectable: false,
        hasBorders: false,
        hasControls: false,
        evented: false,
        objectCaching: false,
      });
      activeShape.value = polygon;
      canvas.add(polygon);
    }
    activeLine.value = line.value;
    pointArray.value.push(circle);
    lineArray.value.push(line.value);
    canvas.add(line.value);
    canvas.add(circle);
  };

  // 鼠标按下事件
  const mousedown = (e) => {
    console.log('===>', canvas);
    const xy = e.pointer || transformMouse(e.e.offsetX, e.e.offsetY);
    if (drawType.value === 'polygon') {
      doDrawing.value = true;
      canvas.skipTargetFind = false;
      try {
        if (pointArray.value.length > 1) {
          if (e.target && e.target.id === pointArray.value[0].id) {
            generatePolygon();
          }
        }
        if (polygonMode.value) {
          addPoint(e);
        }
      } catch (error) {
        console.log('=====>', error);
      }
    }
  };

  const mouseup = (e) => {
    // const xy = e.pointer || transformMouse(e.e.offsetX, e.e.offsetY);
    // this.mouseTo.x = xy.x;
    // this.mouseTo.y = xy.y;
    // this.drawingObject = null;
    // this.moveCount = 1;
    // if (this.drawType != "polygon") {
    //   this.doDrawing = false;
    // }
  };
  const mousemove = (e) => {
    if (drawType.value === 'polygon') {
      if (activeLine.value && activeLine.value.class === 'line') {
        const pointer = canvas.getPointer(e.e);
        activeLine.value.set({ x2: pointer.x, y2: pointer.y });

        const points = activeShape.value.get('points');
        points[pointArray.value.length] = {
          x: pointer.x,
          y: pointer.y,
          zIndex: 1,
        };
        activeShape.value.set({
          points,
        });
        canvas.renderAll();
      }
      canvas.renderAll();
    }
  };
  /**
   * @description 绘制各种图形
   */
  const drawAnyGraphic = (graphic) => {
    if (graphic === 'Rect') {
      const rect = new fabric.Rect({
        left: 200,
        top: 200,
        fill: '',
        stroke: 'red',
        width: 100,
        height: 100,
        storkeWidth: 4,
        objectCaching: false,
        transparentCorners: false,
        cornerColor: 'blue',
      });
      canvas.add(rect);
      canvas.renderAll();
    }
  };
  /**
   * 删除图形
   */
  const deleteObj = () => {
    // eslint-disable-next-line array-callback-return
    canvas.getActiveObjects().map((item) => {
      canvas.remove(item);
    });
  };
  return {
    canvas,
    mousedown,
    mouseup,
    mousemove,
    drawPolygon,
    deleteObj,
    drawAnyGraphic,
  };
};

export default darWGraphic;
