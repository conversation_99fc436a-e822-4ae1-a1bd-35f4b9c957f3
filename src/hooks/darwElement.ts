import { fabric } from "fabric";

function findLongestString(arr) {
  if (arr.length === 0) return null; // 如果数组为空，返回null

  let longestString = arr[0]; // 假设第一个字符串是最长的

  for (let i = 1; i < arr.length; i++) {
    // 从第二个字符串开始比较
    if (arr[i].length > longestString.length) {
      // 如果当前字符串比记录的最长字符串还长
      longestString = arr[i]; // 更新最长字符串
    }
  }

  return longestString; // 返回找到的最长字符串
}

function hasNewline(s) {
  return /\n|\r/.test(s);
}
function getTextWidth(text, font) {
  let canvas =
    (getTextWidth as any).canvas ||
    ((getTextWidth as any).canvas = document.createElement("canvas"));
  let context = canvas.getContext("2d");
  context.font = font;
  if (hasNewline(text)) {
    let lines = text.split("\n");
    let widths = lines.map(function (line) {
      return context.measureText(line).width;
    });
    return Math.max.apply(null, widths);
  } else {
    return context.measureText(text).width;
  }
}

const drawGraph = () => {
  const draw = (canvas, coefficientX, coefficientY, width, data) => {
    canvas.clear();
    try {
      for (let i = 0; i < data.length; i++) {
        // try {
        switch (data[i].dataType) {
          case "SQUARE":
            const rectPoint = data[i].coords.map((item) => ({
              x: item.x * coefficientX,
              y: item.y * coefficientY,
            }));
            // osdConfig
            const osdConfigSQUARE = data[i].osdConfig;
            // 创建一个多边形
            const rect = new fabric.Polygon(rectPoint, {
              fill: "",
              stroke: data[i].isAlarm
                ? `rgb(${osdConfigSQUARE.colorWarn[0]},${osdConfigSQUARE.colorWarn[1]},${osdConfigSQUARE.colorWarn[2]})`
                : `rgb(${osdConfigSQUARE.colorNormal[0]},${osdConfigSQUARE.colorNormal[1]},${osdConfigSQUARE.colorNormal[2]})`, // 边框的颜色
              strokeWidth: osdConfigSQUARE.fontThickness,
              hasBorders: false,
              hasControls: false,
              selectable: false,
              type: "SQUARE",
            });
            // 将矩形添加到canvas画布上
            canvas.add(rect);
            canvas.renderAll();
            break;
          case "CIRCLE":
            const ci = data[i].coords;
            // osdConfig
            const osdConfigCIRCLE = data[i].osdConfig;
            // 创建一个圆对象
            const circle = new fabric.Ellipse({
              left: ci.center.x * coefficientX, // 距离左边的距离
              top: ci.center.y * coefficientY, // 距离上边的距离
              fill: "", // 填充的颜色
              originX: "center",
              originY: "center",
              stroke: data[i].isAlarm
                ? `rgb(${osdConfigCIRCLE.colorWarn[0]},${osdConfigCIRCLE.colorWarn[1]},${osdConfigCIRCLE.colorWarn[2]})`
                : `rgb(${osdConfigCIRCLE.colorNormal[0]},${osdConfigCIRCLE.colorNormal[1]},${osdConfigCIRCLE.colorNormal[2]})`, // 边框的颜色
              strokeWidth: osdConfigCIRCLE.fontThickness, // 边框的宽度
              rx: ci.radius * coefficientX,
              ry: ci.radius * coefficientY,
              hasBorders: false,
              hasControls: false,
              selectable: false,
              type: "CIRCLE",
            });

            // 将圆添加到canvas画布上
            canvas.add(circle);
            canvas.renderAll();
            break;
          case "POINT":
            const up = data[i].coords;
            const osdConfigPOINT = data[i].osdConfig;
            // 创建一个圆对象
            const point = new fabric.Circle({
              left: up.x * coefficientX, // 距离左边的距离
              top: up.y * coefficientY, // 距离上边的距离
              fill: data[i].isAlarm
                ? `rgb(${osdConfigPOINT.colorWarn[0]},${osdConfigPOINT.colorWarn[1]},${osdConfigPOINT.colorWarn[2]})`
                : `rgb(${osdConfigPOINT.colorNormal[0]},${osdConfigPOINT.colorNormal[1]},${osdConfigPOINT.colorNormal[2]})`, // 填充的颜色
              originX: "center",
              originY: "center",
              radius: 10, // 点的大小
              hasBorders: false,
              hasControls: false,
              selectable: false,
              type: "POINT",
            });

            // 将圆添加到canvas画布上
            canvas.add(point);
            canvas.renderAll();
            break;
          case "LINE_ARROW":
            const la = data[i].coords;
            const osdConfigLINE_ARROW = data[i].osdConfig;
            // 创建一条直线
            let path_A = "";
            let a_x1 = la.start.x * (coefficientX as number),
              a_x2 = la.end.x * (coefficientX as number),
              a_y1 = la.start.y * (coefficientX as number),
              a_y2 = la.end.y * (coefficientX as number);
            let w = a_x2 - a_x1,
              h = a_y2 - a_y1,
              sh = Math.cos(Math.PI / 4) * 16;
            let sin = h / Math.sqrt(Math.pow(w, 2) + Math.pow(h, 2));
            let cos = w / Math.sqrt(Math.pow(w, 2) + Math.pow(h, 2));

            let w1 = (16 * sin) / 4,
              h1 = (16 * cos) / 4,
              centerx = sh * cos,
              centery = sh * sin;
            path_A = " M " + a_x1 + " " + a_y1;
            path_A += " L " + a_x2 + " " + a_y2;
            path_A += " L " + (a_x2 - centerx + w1 * 2) + " " + (a_y2 - centery - h1 * 2);

            path_A += " L " + (a_x2 - centerx - w1 * 2) + " " + (a_y2 - centery + h1 * 2);
            path_A += " L " + a_x2 + " " + a_y2;
            const line_a = new fabric.Path(path_A, {
              stroke: data[i].isAlarm
                ? `rgb(${osdConfigLINE_ARROW.colorWarn[0]},${osdConfigLINE_ARROW.colorWarn[1]},${osdConfigLINE_ARROW.colorWarn[2]})`
                : `rgb(${osdConfigLINE_ARROW.colorNormal[0]},${osdConfigLINE_ARROW.colorNormal[1]},${osdConfigLINE_ARROW.colorNormal[2]})`,
              strokeWidth: osdConfigLINE_ARROW.fontThickness,
              fill: data[i].isAlarm
                ? `rgb(${osdConfigLINE_ARROW.colorWarn[0]},${osdConfigLINE_ARROW.colorWarn[1]},${osdConfigLINE_ARROW.colorWarn[2]})`
                : `rgb(${osdConfigLINE_ARROW.colorNormal[0]},${osdConfigLINE_ARROW.colorNormal[1]},${osdConfigLINE_ARROW.colorNormal[2]})`,
              hasBorders: false,
              hasControls: false,
              selectable: false,
              type: "LINE",
            });

            // 将圆添加到canvas画布上
            canvas.add(line_a);
            canvas.renderAll();
            break;
          case "LINE":
            const le = data[i].coords;

            const osdConfigLINE = data[i].osdConfig;
            // 创建一条直线
            let path = "";
            let x1 = le.start.x * (coefficientX as number),
              x2 = le.end.y * (coefficientX as number),
              y1 = le.start.x * (coefficientX as number),
              y2 = le.end.y * (coefficientX as number);

            path = "M " + x1 + " " + y1 + " L " + x2 + " " + y2;

            const line = new fabric.Path(path, {
              stroke: data[i].isAlarm
                ? `rgb(${osdConfigLINE.colorWarn[0]},${osdConfigLINE.colorWarn[1]},${osdConfigLINE.colorWarn[2]})`
                : `rgb(${osdConfigLINE.colorNormal[0]},${osdConfigLINE.colorNormal[1]},${osdConfigLINE.colorNormal[2]})`,
              strokeWidth: osdConfigLINE.fontThickness,
              fill: data[i].isAlarm
                ? `rgb(${osdConfigLINE.colorWarn[0]},${osdConfigLINE.colorWarn[1]},${osdConfigLINE.colorWarn[2]})`
                : `rgb(${osdConfigLINE.colorNormal[0]},${osdConfigLINE.colorNormal[1]},${osdConfigLINE.colorNormal[2]})`,
              hasBorders: false,
              hasControls: false,
              selectable: false,
              type: "LINE",
            });

            // 将圆添加到canvas画布上
            canvas.add(line);
            canvas.renderAll();

            break;
          case "STRING":
            // debugger;
            const tx = data[i].coords;
            const osdConfigSTRING = data[i].osdConfig;
            let textWidth = Number(osdConfigSTRING.fontSize * coefficientX) * data[i].text.length; // 文字宽度

            if (hasNewline(data[i].text)) {
              const texts = data[i].text.split("\n");
              // const textLengths = texts.map(function (line) {
              //   return line.length;
              // });
              const longestText = findLongestString(texts);
              textWidth = Number(osdConfigSTRING.fontSize * coefficientX) * longestText.length;
            }

            let textW1 = 0;
            let textW2 = 0;

            console.log(textWidth, coefficientX, "textWidth");

            // 超出画布的一半
            if (textWidth > width / 2) {
              textW1 = width / 2;
            } else {
              textW1 = getTextWidth(
                data[i].text,
                `${osdConfigSTRING.fontSize * coefficientX}px system-ui`
              );
              // textW1 = textWidth;
            }
            // 超出画布的边界
            if (tx[0] * coefficientX + textWidth > width) {
              textW2 = width - tx[0] * coefficientX;
            } else {
              textW2 = getTextWidth(
                data[i].text,
                `${osdConfigSTRING.fontSize * coefficientX}px system-ui`
              );
              // textW2 = textWidth;
            }

            const textW = Math.min(textW1, textW2);

            // const textWidth = 300 ;
            // 创建一个文本
            const text = new fabric.Textbox(data[i].text, {
              left: tx.x * (coefficientX as number), // 距离左边的距离
              top: tx.y * (coefficientY as number), // 距离上边的距离
              width: textW + 10, // 字体容器宽度
              fontSize: osdConfigSTRING.fontSize * coefficientX,
              fill: `rgb(${osdConfigSTRING.fontColor[0]},${osdConfigSTRING.fontColor[1]},${osdConfigSTRING.fontColor[2]})`,
              backgroundColor: osdConfigSTRING.fontBgColor
                ? `rgb(${osdConfigSTRING.fontBgColor[0]},${osdConfigSTRING.fontBgColor[1]},${osdConfigSTRING.fontBgColor[2]})`
                : "rgb(0,0,0,0)", // 容器背景颜色

              hasBorders: false,
              hasControls: false,
              selectable: false,
              splitByGrapheme: true,
            });
            // 设置文字位置原点计算方式

            text.set("originX", (data[i].textPosition as string).split(":")[0]);
            text.set("originY", (data[i].textPosition as string).split(":")[1]);
            // text.set("originY", 'bottom');
            // text.set("originX", 'center');
            text.setPositionByOrigin(
              { x: tx.x * (coefficientX as number), y: tx.y * (coefficientY as number) },
              (data[i].textPosition as string).split(":")[0],
              (data[i].textPosition as string).split(":")[1] === "top" ? "top" : "bottom"
            );

            // text.setCoords();
            // 将圆添加到canvas画布上
            canvas.add(text);
            canvas.renderAll();

            //----------------------------------------------
            break;
          case "ELLIPSE":
            const ell = data[i].coords;

            const osdConfigELLIPSE = data[i].osdConfig;
            // 创建一个椭圆
            const ellipseOption = new fabric.Ellipse({
              top: ell.center.y * coefficientY,
              left: ell.center.x * coefficientX,
              rx: ell.axes[0] * coefficientX,
              ry: ell.axes[1] * coefficientY,
              // angle: ell.angle,
              originX: "center",
              originY: "center",
              fill: "",
              stroke: data[i].isAlarm
                ? `rgb(${osdConfigELLIPSE.colorWarn[0]},${osdConfigELLIPSE.colorWarn[1]},${osdConfigELLIPSE.colorWarn[2]})`
                : `rgb(${osdConfigELLIPSE.colorNormal[0]},${osdConfigELLIPSE.colorNormal[1]},${osdConfigELLIPSE.colorNormal[2]})`, // 边框的颜色
              strokeWidth: osdConfigELLIPSE.fontThickness, // 边框的宽度
              hasBorders: false,
              hasControls: false,
              selectable: false,
            });
            ellipseOption.rotate(ell.angle);
            // 将椭圆加到canvas上
            canvas.add(ellipseOption);
            canvas.renderAll();
            break;
          case "POLYGON":
            const poly = data[i].coords.map((item) => ({
              x: item.x * coefficientX,
              y: item.y * coefficientY,
            }));
            // osdConfig
            const osdConfigPOLYGON = data[i].osdConfig;
            // 创建一个多边形
            const polygon = new fabric.Polygon(poly, {
              fill: "",
              stroke: `rgb(${osdConfigPOLYGON.colorNormal[0]},${osdConfigPOLYGON.colorNormal[1]},${osdConfigPOLYGON.colorNormal[2]})`, // 边框的颜色
              strokeWidth: osdConfigPOLYGON.fontThickness,
              hasBorders: false,
              hasControls: false,
              selectable: false,
            });
            canvas.add(polygon);
            canvas.renderAll();
            break;
          default:
            break;
        }
        // } catch {
        //   console.error("参数异常");
        // }
      }
    } catch (e) {
      console.error(e);
    }
  };
  return {
    draw,
  };
};
export default drawGraph;
