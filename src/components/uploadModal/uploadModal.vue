<template>
  <a-modal
    :title="'导入通道'"
    :visible="modelInfo.visible"
    :width="600"
    :mask-closable="false"
    :ok-text="'导入'"
    :cancel-text="'关闭'"
    @ok="modelInfo.handleOk"
    @cancel="modelInfo.handleCancel"
    :after-close="modelInfo.handleAfterClose"
    :confirm-loading="modelInfo.confirmLoading"
  >
    <a style="float: right" @click="exportTemplate">导出通道模板</a>

    <a-form
      ref="formRef"
      :model="form"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :rules="rules"
    >
      <a-form-item label="导入方式" name="overwrite">
        <a-radio-group v-model:value="form.overwrite">
          <a-radio :value="false">
            <span>增量导入</span>
            <a-tooltip title="遇到相同IP通道节点，会跳过导入操作，保留原通道的数据不变">
              <InfoCircleOutlined />
            </a-tooltip>
          </a-radio>
          <a-radio :value="true">
            <span>覆盖导入</span>
            <a-tooltip title="遇到相同IP通道节点，按提交的数据更新该通道信息">
              <InfoCircleOutlined />
            </a-tooltip>
          </a-radio>
        </a-radio-group>
      </a-form-item>
      <a-form-item label="导入文件包" name="fileList">
        <a-upload
          :multiple="false"
          :accept="'.xlsx,'"
          :beforeUpload="beforeUpload"
          :show-upload-list="true"
          @change="changeFileList"
          :fileList="form.fileList"
          :maxCount="1"
        >
          <a-button> <UploadOutlined /> 上传 </a-button>
        </a-upload>
      </a-form-item>
    </a-form>
    <!-- -------------------------------------------------上传进度与日志----------------------------------- -->
    <a-progress :percent="percent" class="progress-bar" />
    <div class="log-container" id="log">
      <a-empty :description="'暂无导入日志'" v-if="logList.length === 0" />
      <div v-for="(item, index) in logList" :key="index" class="log-item">
        <IconFont :type="getMsgType(item)" :class="getMsgType(item)" class="iconfont"></IconFont>
        <span class="log-message">{{ item.timestamp }}-{{ item.msg }}</span>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import HWebsocket from "@/common/h-websocket";
import uEmitter from "@/common/u-event-bus";
import { message } from "ant-design-vue";
import { ref } from "vue";
import type { FormInstance } from "ant-design-vue";
import type { Rule } from "ant-design-vue/es/form";
import IconFont from "@/components/c-iconfont";
import { UploadOutlined, InfoCircleOutlined } from "@ant-design/icons-vue";
import {  exportChannelTemp } from "@/api/design";
import { downloadByParseBlob } from "@/common/u-http";

const props = defineProps({
  uploadFunc: Function,
});

// ----------------------------------------------弹窗信息----------------------------------------------
const isFileChanged = (file: File): Promise<boolean> => {
  return new Promise((resolve, reject) => {
    file
      .slice(0, 1)
      .arrayBuffer()
      .then(() => resolve(false))
      .catch(() => resolve(true));
  });
};
const modelInfo = ref({
  visible: false,
  title: "导入",
  width: 600,
  maskClosable: false,
  confirmLoading: false, // 上传按钮loading
  // 上传操作
  handleOk: async () => {
    const validate = await formRef.value?.validateFields();
    const isChanged = await isFileChanged(form.value.fileList[0].originFileObj);
    if (isChanged) {
      message.warn("上传文件发生变更，请重新选择文件", 2);
      form.value.fileList = [];
      logList.value = [];
      percent.value = 0;

      return;
    }
    if (validate) {
      customRequest(form.value);
    }
  },
  // 取消
  handleCancel: () => {
    modelInfo.value.visible = false;
  },
  // 关闭弹窗后续操作
  handleAfterClose: () => {
    form.value.fileList = [];
    logList.value = [];
    percent.value = 0;
    modelInfo.value.confirmLoading = false;
    form.value.overwrite = false;
    // formRef.value?.resetFields();
    formRef.value?.clearValidate();
    if (excelFileWs) {
      excelFileWs.close();
    }
    // uEmitter.off("refreshProjectTreebyUpload");
  },
});

const handleOpenModal = () => {
  modelInfo.value.visible = true;
};

// ----------------------------------------------表单信息-----------------------------------------------
const formRef = ref<FormInstance>();
const form = ref({
  overwrite: false, // 是否覆盖导入
  fileList: [],
});
const labelCol = { span: 7 };
const wrapperCol = { span: 16 };
const validateFileList = async (_rule: Rule, value: any) => {
  if (value.length === 0) {
    return Promise.reject(new Error("请选择文件"));
  }
};
const rules: Record<string, Rule[]> = {
  fileList: [{ required: true, validator: validateFileList, trigger: "change" }],
};

// ----------------------------------------------上传文件-----------------------------------------------

// 上传文件检查
const beforeUpload = (file: any) => {
  const isExcel = file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

  const maxSize = 200 * 1024; // 200KB
  (async () => {
    if (!isExcel || file.size > maxSize) {
      return Promise.reject(); // 阻止上传
    }
  })();

  return false;
};

const changeFileList = (fileList: any) => {
  const isExcel =
    fileList.file.type === "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet";

  const maxSize = 200 * 1024; // 200KB
  if (!isExcel || fileList.file.size > maxSize) {
    message.warn("仅支持上传xlsx文件，文件大小不能超过200KB");
    return; // 阻止上传
  }
  form.value.fileList = fileList.fileList;
};

// 上传文件请求
let excelFileWs = null;
const customRequest = async (form) => {
  percent.value = 0;
  logList.value = [];
  modelInfo.value.confirmLoading = true;
  const formData = new FormData();
  formData.append("excelFile", form.fileList[0].originFileObj);
  formData.append("isOverwrite", form.overwrite);
  // 建立长连接
  excelFileWs = new HWebsocket(`${(window as any).g.wsApi}/websocket/import-excel`);
  // 监听上传进度
  excelFileWs.onMessage = (evt) => {
    const wsdata = JSON.parse(evt.data);
    updatePercent(wsdata);
    if (wsdata.progress === 1) {
      message.success("操作成功"); // 上传成功
      modelInfo.value.confirmLoading = false;
      excelFileWs.close(); // 关闭websocket连接
      console.log("websocket连接关闭");
      uEmitter.emit("refreshProjectTreebyUpload"); // 刷新工程树
    }
  };
  // 上传文件
  const res = await props.uploadFunc(formData);
  if (res.code !== 0) {
    excelFileWs.close(); // 关闭websocket连接
    modelInfo.value.confirmLoading = false;
  }
};

// 上传进度与日志
const percent = ref(0); // 进度

const logList = ref([]); // 日志列表

const getMsgType = (item: any) => {
  switch (item.level) {
    case "ERROR":
      return "icon-error-fill";
    case "WARN":
      return "icon-warning-filling";
    case "INFO":
      return "icon-info";
    default:
      return "icon-success-filling";
  }
};
const updatePercent = (wsdata: any) => {
  if (wsdata.progress) {
    percent.value = Math.round(wsdata.progress) * 100;
  }
  if (wsdata.msg) {
    logList.value.push(wsdata);
  }
};

// 导出通道模板
const exportTemplate = async () => {
  try {
    const resData =  await exportChannelTemp();
    downloadByParseBlob(resData, (jsonObj) => {
      message.error(jsonObj.message);
    });
  } catch (err) {
    console.log(err);
  }
};

defineExpose({
  handleOpenModal,
});
</script>

<style scoped lang="less">
.ant-form-item-control-input-content > span {
  display: flex;
  align-items: center;
}
:deep(.ant-upload-list-item-name) {
  width: 250px !important;
}
.progress-bar {
  width: 80%;
  margin-left: 10%;
}
.log-container {
  width: 80%;
  margin-left: 10%;
  margin-top: 10px;
  height: 300px;
  border: 1px solid #ddd;
  overflow-y: auto;
  .log-item {
    display: flex;
    width: 100%;
    line-height: 2;
    .iconfont {
      font-size: 18px;
      margin-right: 8px; /* 根据需要调整图标和文字之间的间距 */
      margin-top: 5px;
    }
    .log-message {
      white-space: pre-wrap;
      word-break: break-word;
      font-family: "Fira Code", "Consolas", monospace;
      font-size: 14px;
    }
  }
}
.log-container::-webkit-scrollbar {
  width: 8px !important;
}
.icon-error-fill {
  color: var(--errorMsgColor);
}
.icon-warning-filling {
  color: var(--warningMsgColor);
}
.icon-success-filling {
  color: var(--successMsgColor);
}
.icon-info {
  color: var(--infoMsgColor);
}
</style>
