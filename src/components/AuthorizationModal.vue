<template>
    <div class="authorizationModal">
        <a-modal
            :open="visible"
            centered
            :closable="!props.isExpire"
            :keyboard="!props.isExpire"
            :maskClosable="false"
            @update:visible="handleUpdateVisible">
            <template #title>
                <div class="modal-title">
                  授权信息
                  <span v-if="props.isExpire" class="expired-badge">已过期</span>
                </div>
            </template>
            <div class="info-item">
                <span class="info-label">HiaVision</span>
                <span v-if="HiaVision" :class="{'green-background': HiaVision === '正式版', 'yellow-background': HiaVision !== '正式版'}">{{ HiaVision }}</span>
            </div>
            <div class="info-item">
                <span class="info-label">产品版本</span>{{ version }}
            </div>
            <div class="info-item">
                <span class="info-special-label">授权服务器</span>{{ serverIP }}
            </div>
            <div class="info-item">
                <span class="info-label">授权日期</span>{{ authTime }}
            </div>
            <div class="info-item">
                <span class="info-label">失效日期</span>{{ expireDate }}
            </div>
            <div class="machine-code">
                <p>机器码</p>
                <a-textarea v-model:value="appCode" :autoSize="{ minRows: 3, maxRows: 3 }" readOnly></a-textarea>
            </div>
            <div class="info-item">
                <span class="info-upload">上传证书</span>
                <a-upload
                    :fileList="fileList"
                    :beforeUpload="beforeUpload"
                    @remove="removeFile"
                    :showUploadList="{ showPreviewIcon: false, showRemoveIcon: true }"
                    accept=".lic"
                    :maxCount="1">
                        <a-button
                        style="width: 50px; border: 1px solid #1d41ab; color: #1d41ab; margin-left: 10px;margin-top: 4px"
                        title="上传证书"
                        >
                        <UploadOutlined />
                        </a-button>
                </a-upload>
            </div>
            <div style="display: flex;">
                <p>详情</p>
                <a-button type="link" @click="toggleCollapse" class="btn">
                    <template v-if="isCollapsed">
                        <DownOutlined />
                    </template>
                    <template v-else>
                        <UpOutlined />
                    </template>
                </a-button>
            </div>
            <div v-show="!isCollapsed" class="table-container">
               <a-table
                :columns="columns"
                :dataSource="dataSource"
                :pagination="false"
                bordered>
                <template #bodyCell="{ column, record }">
                    <template v-if="column.dataIndex === 'authorizationDescription' && record.authorizationItem === '算法' && record.authorizationDescription">
                        <div v-for="(item, index) in record.authorizationDescription" :key="index">
                          <span v-if="item">支持：{{ item }}</span>
                        </div>
                    </template>
                </template>
            </a-table>
            </div>
            <template #footer>
                <a-button type="primary" @click="updateAuthorization">更新授权</a-button>
            </template>
        </a-modal>
    </div>
</template>

<script setup>
import { useStore } from 'vuex';
import { message } from 'ant-design-vue';
import { getAuthorizationInfo, updateLicenseFile } from '@/api/project';
import {
  defineProps, defineEmits, ref, watch, computed 
} from 'vue';
import { UpOutlined, DownOutlined, UploadOutlined } from '@ant-design/icons-vue';
import router from '@/router';

const store = useStore();
const props = defineProps({
  isExpire: {
    type: Boolean,
    default: false,
    required: true,
  },
});

const visible = computed(() => store.state.design.authorizationModalVisible);

// hiavision信息
const HiaVision = ref('');

// 版本信息
const version = ref('');

// 授权服务器
const serverIP = ref('');

// 授权时间
const authTime = ref('');

// 最近到期
const expireDate = ref('');

// 机器码
const appCode = ref('');

const fileList = ref([]);

// 表格数据
const dataSource = ref([
  { authorizationItem: '通道', authorizationLimit: '', authorizationDescription: '' },
  { authorizationItem: '算法', authorizationLimit: '', authorizationDescription: '' },
]);

// 表格列
const columns = [
  {
    title: '授权项',
    dataIndex: 'authorizationItem',
    width: '160px',
    align: 'center',
  },
  {
    title: '授权额度',
    dataIndex: 'authorizationLimit',
    width: '170px',
    align: 'center',
  },

  {
    title: '授权描述',
    dataIndex: 'authorizationDescription',
    width: '450px',
    align: 'left',
  },
];

// 监听 visible 属性的变化
watch(visible, (newVal) => {
  if (newVal) {
    getAuthorizationInfo().then((res) => {
      if (res.code === 0) {
        // 如果证书过期不显示除了机器码外的任何信息
        if (!props.isExpire) {
          setModalData(res.data);
        } else {
          initModalData();
        }

        // 机器码
        appCode.value = res.data?.appCode ?? '';
      }
    });
  }
});

// 上传前检查
const beforeUpload = (file) => {
  const fileSuffix = file.name.split('.').pop();
  if (!['lic', 'LIC'].includes(fileSuffix)) {
    message.warn('请上传lic格式文件');
    return;
  }

  if (file.size > 20 * 1024) {
    message.warn('文件大小不能超过20KB');
    return;
  }
  // 清空旧文件
  fileList.value = [];

  fileList.value.push(file);
  return false;
};

// 移除文件
const removeFile = (file) => {
  fileList.value = [];
};

// 是否展开
const isCollapsed = ref(true);

// 切换展开
const toggleCollapse = () => {
  isCollapsed.value = !isCollapsed.value;
};

const emit = defineEmits(['closeModal']);

// 更新授权
const updateAuthorization = () => {
  if (fileList.value.length === 0 || !fileList.value) {
    message.info('请上传授权证书');
    return;
  }
  const formData = new FormData();
  formData.append('licenseFile', fileList.value[0]);
  updateLicenseFile(formData).then((res) => {
    if (res.code === 0) {
      !props.isExpire && setModalData(res.data);
      // 机器码
      appCode.value = res.data?.appCode ?? '';
      message.success('更新成功');
      props.isExpire && emit('closeModal', false);
      // 如果单点登陆模式 授权完直接跳平台登录页 否则就正常跳设计页
     if (window.localStorage.getItem("plantMode") === "true") { 
        const config = JSON.parse(
          localStorage.getItem("oauth2Config"));
        const { clientId, redirectUri, authUrl, webTokenUrl, targetPath } = config;
        const tokenUrl = window.location.hash ? `/#${webTokenUrl}` : webTokenUrl;
        const redirectWeb = encodeURIComponent(
          `${window.location.origin}${tokenUrl}?path=${targetPath}`
        );
        const redirectUrl = encodeURIComponent(`${redirectUri}?redirect_web=${redirectWeb}`);
        const url = `${authUrl}?client_id=${clientId}&redirect_uri=${redirectUrl}&response_type=token`;
        window.location.href = url;
     } else {
        router.push({ path: '/design' });
     }

    }
  });
};
const handleUpdateVisible = (val) => {
  initModalData();
  store.commit('setAuthorizationModalVisible', val);
  emit('closeModal', val);
};


// 设置弹框数据  机器码另赋值
const setModalData = (data) => {
  // hiavision信息
  HiaVision.value = data?.systemVersion ?? '';
  // 版本信息
  version.value = data?.productVersion ?? '';
  // 授权日期
  authTime.value = data?.authTime?.split(' ')[0] ?? '';
  // 过期日期
  expireDate.value = data?.expireTime?.split(' ')[0] ?? '';
  // 最大算法数量
  const maxAlgorithmSize = data?.algorithmCodes?.length ?? 0;
  // 服务器ip
  const url = window.g.api
  const ip = new URL(url).hostname
  serverIP.value = ip ?? '';
  // 授权详情表格信息
  dataSource.value.forEach((item) => {
    if (item.authorizationItem === '算法') {
      item.authorizationLimit = `${data?.algorithmCount ?? 0}/${maxAlgorithmSize}`; 
      item.authorizationDescription = data?.algorithmCodes ?? [];
    } else if (item.authorizationItem === '通道') {
      item.authorizationLimit = `${data?.channelNodeCount ?? 0}/${data?.maxChannelSize ?? 0}`;
      item.authorizationDescription = '支持：接入指定数量的摄像头/预置点';
    }
  });
}

const initModalData = () => {
  // 初始化数据
  HiaVision.value = '';
  appCode.value = '';
  version.value = '';
  expireDate.value = '';
  serverIP.value = '';
  authTime.value= '';
  dataSource.value.forEach((item) => {
    item.authorizationLimit = '';
    item.authorizationDescription = '';
  });
  fileList.value = [];
  isCollapsed.value = true;
};

</script>

<style lang="less" scoped>
.table-container {
    height: 200px;
    overflow-y: auto;
}

.info-item {
    display: flex;
    align-items: center;
    margin-bottom: 20px;
}

.machine-code {
    margin-bottom: 20px;
}

.btn {
    margin-left: 40%;
}

.info-label {
    margin-right: 40%;
}

.info-upload {
    margin-right: 39%;
}

.info-special-label {
    margin-right: 37%;
}

.green-background {
    background-color: #78d778;
    color: white;
    padding: 2px 5px;
    border-radius: 3px;
}

.yellow-background {
    background-color: #ffd700;
    color: black;
    padding: 2px 5px;
    border-radius: 3px;
}
.modal-title {
  display: flex;
  justify-content: space-between;
}
.expired-badge {
  background: #ff4d4f;
  padding: 2px 8px;
  border-radius: 4px;
}
:deep(.ant-upload-text-icon) {
    display: none;
}
</style>
