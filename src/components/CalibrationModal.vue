<template>
  <div>
    <a-modal
      v-model:open="calibrationModal"
      :footer="false"
      :title="title"
      width="1258px"
      @cancel="closeModal"
      :maskClosable="false"
      destroyOnClose
    >
      <div class="mainBox">
        <div class="leftPart">
          <div
            style="width: 960px; height: 500px; top: 100px; position: absolute"
            v-if="!selectImg"
          >
            <a-empty description="暂未选取截图"></a-empty>
          </div>
          <div id="fabricBox">
            <canvas id="fabricGraphic" width="960" height="540"></canvas>
          </div>
          <div class="imageList">
            <a-image-preview-group>
              <div
                v-for="(item, index) in historyImgList"
                :key="index"
                class="activeImg"
                :class="{ selecedshotPic: item.src === selectImg }"
                @dblclick="addHistoryPic(index)"
              >
                <a-image
                  :src="item.src"
                  style="width: 192px; height: 108px"
                  @click="setCanvasBg(item.src)"
                  :preview="false"
                  class="activeImg"
                ></a-image>
              </div>

              <!-- <a-image style="width:150px;height:100px" src="https://aliyuncdn.antdv.com/logo.png" />
          <a-image style="width:150px;height:100px" src="https://aliyuncdn.antdv.com/logo.png" /> -->
            </a-image-preview-group>
          </div>
        </div>
        <div class="rightPart">
          <div style="margin-bottom: 10px">
            <span>图像源：</span>
            <a-select
              v-model:value="getImgMode"
              style="width: 100px;margin-left:13px"
              size="small"
              @change="handleImgMode"
            >
              <a-select-option value="identificationFailed">周期截图</a-select-option>
              <a-select-option value="localUpload">本地上传</a-select-option>
              <!-- <a-select-option value="modelFailed">匹配失败</a-select-option> -->
            </a-select>
            <UploadFile
              @refreshImgList="refreshImgList"
              :preset-id="activeNodeKey"
              v-if="getImgMode === 'localUpload'"
            ></UploadFile>
          </div>
          <!-- <div class="graphicTemplate">
            <div>截图模板:</div>
            <div>
              <a-tooltip>
                <template #title>{{ template1.title }}</template>
                <IconFont
                  :type="template1.type"
                  style="font-size: 24px"
                  @click="template1.onclick"
                ></IconFont>
              </a-tooltip>
            </div>
          </div> -->
          <div>
            <span style="margin-right: 10px">场景名称:</span
            ><a-input
              v-model:value="senceName"
              placeholder="请输入场景名称"
              style="width: 150px"
            ></a-input>
          </div>
          <div class="confirmBtn">
            <a-button @click="closeModal" style="margin-right: 10px">取消</a-button>
            <a-button type="primary" @click="handleOk">提交</a-button>
          </div>
          <p style="margin-top: 10px; color: red">
            <strong>提示：</strong><br />
            双击第一张小图可获取当前预置点最新截图。
          </p>
        </div>
      </div>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, watch, onMounted, nextTick, toRaw, computed } from "vue";
import { useStore } from "vuex";
import { fabric } from "fabric";
import { message } from "ant-design-vue";
import IconFont from "@/components/c-iconfont";
import UploadFile from "./uploadFile.vue";
// import { getHistoryPic } from '@/api/design';
import {
  getAlgorithmConf,
  getAlgorithmPic,
  screenshot,
  addSence,
  editSence,
  channelScreenshot,
  saveHistoryPic,
  editHistoryPic,
} from "@/api/design";

export default defineComponent({
  props: {
    isCalibrationModalOpen: {
      type: Boolean,
      default: false,
    },
    title: {
      type: String,
      default: "场景配置",
    },
    echoCoordinate: {
      type: Array,
      default: () => [],
    },
    channelId: {
      type: String,
      default: "",
    },
  },
  components: {
    IconFont,
    UploadFile,
  },
  setup(props, { emit }) {
    const store = useStore();
    const activeNodeKey = computed(() => store.state.design.activeTreeKey); // 当前激活的预置点ID

    const calibrationModal = ref<boolean>(false); // 控制弹窗

    const senceName = ref<string>(""); // 场景名称

    const currentSence = ref<any>(null); // 当前场景

    const openCalibrationModal = async (sence = null, algorithmPic: any) => {
      calibrationModal.value = true;
      nextTick(async () => {
        document.onkeydown = (e) => {
          // 键盘 delete 除所选元素
          if (e.keyCode === 46) {
            deleteObj();
          }
        };
        getImgMode.value = "identificationFailed"; // 默认为周期截图

        historyImgList.value = algorithmPic.data.pics.map((item, index) => ({
          src: item,
        }));

        // 初始化画布
        const canvasEl = document.getElementById("fabricGraphic");
        canvas.value = new fabric.Canvas(canvasEl);
        // 设置画布背景
        setCanvasBg(historyImgList.value[0].src);
        // 取消loading
        emit("cancelLoading", false);
        // 赋值场景名称
        senceName.value = sence.name;
        currentSence.value = sence;
      });
    };
    // 历史图片列表
    const addHistoryPic = (index) => {
      if (index === 0) {
        channelScreenshot(props.channelId, false).then((res) => {
          if (res.code === 0) {
            historyImgList.value[0] = { src: res.data };
            setCanvasBg(historyImgList.value[0].src);
            message.success("更新成功");
          }
        });
      }
    };
    // 图片列表
    // 获取图片
    const getImgMode = ref<string>("identificationFailed");
    // 更新图片列表
    const handleImgMode = (val) => {
      if (val === "identificationFailed") {
        getAlgorithmPic(activeNodeKey.value).then((res) => {
          if (res.code === 0 && res.data !== null) {
            historyImgList.value = res.data.pics.map((item, index) => ({
              src: item,
            }));
            setCanvasBg(historyImgList.value[0].src);
          }
        });
      }
      if (val === "localUpload") {
        getAlgorithmPic(activeNodeKey.value, "MANUAL").then((res) => {
          if (res.code === 0 && res.data !== null) {
            historyImgList.value = res.data.pics.map((item, index) => ({
              src: item,
            }));
            setCanvasBg(historyImgList.value[0].src);
          }
        });
      }
      if (val === "modelFailed") {
        getAlgorithmPic(activeNodeKey.value, "TEMPLATE_FAILED").then((res) => {
          if (res.code === 0 && res.data !== null) {
            historyImgList.value = res.data.pics.map((item, index) => ({
              src: item,
            }));
            setCanvasBg(historyImgList.value[0].src);
          }
        });
      }
    };
    // 本地上传刷新
    const refreshImgList = (val) => {
      // 刷新图片列表
      getAlgorithmPic(activeNodeKey.value, "MANUAL").then((res) => {
        if (res.code === 0 && res.data !== null) {
          historyImgList.value = res.data.pics.map((item, index) => ({
            src: item,
          }));
          setCanvasBg(historyImgList.value[0].src);
        }
      });
    };
    // 所选择图片
    const selectImg = ref<string>("");
    const historyImgList = ref([{ src: "" }]);
    // 点击图片事件
    const setCanvasBg = (src) => {
      return new Promise<void>((resolve, reject) => {
        // 所选图片复制
        selectImg.value = src;
        // 更改canvas背景
        const callback = (image, isError) => {
          // 设置图片背景在水平方向上的缩放比例
          image.scaleX = canvas.value.width / image.width;
          // 设置图片背景在竖直方向上的缩放比例
          image.scaleY = canvas.value.height / image.height;
          scale.x = canvas.value.width / image.width;
          scale.y = canvas.value.height / image.height;

          let coordinate = null;
          let width = null;
          let height = null;

          // 绘制矩形,有tmpSquare参数的为真实场景
          if (currentSence.value.tmpSquare.length) {
            coordinate = currentSence.value.tmpSquare.map((item) => {
              return { x: item.x * scale.x, y: item.y * scale.y };
            });
            width = Math.abs(coordinate[0].x - coordinate[2].x);
            height = Math.abs(coordinate[0].y - coordinate[2].y);
          } else {
            width = 200;
            height = 200;
            coordinate = [{ x: 200, y: 200 }];
          }

          const rect = new fabric.Rect({
            left: coordinate[0].x,
            top: coordinate[0].y,
            fill: "",
            stroke: "#E34F51",
            strokeWidth: 2,
            width: width,
            height: height,
            hasControls: true,
          });
          if (canvas.value.getObjects().length === 0) {
            toRaw(canvas.value).add(rect);
            dontMove(rect);
          }

          canvas.value.setBackgroundImage(image);
          canvas.value.renderAll();
        };
        fabric.Image.fromURL(src, callback);
        resolve();
      });
    };
    // 初始化canvas
    const canvas = ref(null);

    // 模版列表
    const template1 = ref({
      title: "矩形",
      type: "icon-xingzhuang-juxing",
      graphic: "Rect",
      onclick: () => {
        addGraphic();
      },
    });

    // 截图列表
    const sreenshotImgList = ref([]);
    // 添加图片容器
    const addEmpty = () => {
      if (sreenshotImgList.value.length >= 1) {
        message.warn("最多只能添加一个图片容器", 2);
        return;
      }
      sreenshotImgList.value.push({ url: "", coordinate: null, pan: null, tilt: null, zoom: null });
    };
    const selecedPicIndex = ref<any>(0); // 选择的图片
    // 选择图片
    const selectPic = (index) => {
      console.log(index);
      selecedPicIndex.value = index;
    };

    /**
     * @description 绘制图形
     */
    // 添加矩形
    const addGraphic = () => {
      if (canvas.value.getObjects().length > 0) {
        message.warn("您已经添加过图形了");
        return;
      }
      const rect = new fabric.Rect({
        left: 500,
        top: 200,
        fill: "",
        stroke: "#E34F51",
        strokeWidth: 2,
        width: 200,
        height: 200,
        hasControls: true,
      });
      toRaw(canvas.value).add(rect);
      dontMove(rect);
    };
    const dontMove = (rect) => {
      rect.on("mouseup", (ev) => {
        // 获取画布视口边界
        let canvasBoundaries = canvas.value.calcViewportBoundaries();

        // 矩形本身
        let obj = ev.target;
        console.log(canvas.value.viewportTransform);

        // 矩形的边界
        let objBoundingRect = obj.getBoundingRect();

        const zoom = canvas.value.viewportTransform[0];

        const scaleX = (canvasBoundaries.br.x * zoom) / objBoundingRect.width;
        const scaleY = (canvasBoundaries.br.y * zoom) / objBoundingRect.height;
        // const scaleToFit = Math.min(scaleX, scaleY);

        if (scaleX < 1) {
          // 应用缩放比例
          obj.set({
            scaleX: obj.scaleX * scaleX,
          });
          canvas.value.renderAll();
        }

        if (scaleY < 1) {
          // 应用缩放比例
          obj.set({
            scaleY: obj.scaleY * scaleY,
          });
        }

        // 超出左边
        if (objBoundingRect.left < canvasBoundaries.tl.x) {
          // ev.target.left = canvasBoundaries.tl.x;
          obj.set({ left: 0 });
        }

        // 超出右边
        if (objBoundingRect.left + objBoundingRect.width > canvasBoundaries.br.x) {
          obj.left = canvasBoundaries.br.x - obj.getScaledWidth();
        }

        // 超出上边
        if (objBoundingRect.top < canvasBoundaries.tl.y) {
          // ev.target.top = canvasBoundaries.tl.y;
          obj.set({ top: 0 });
        }

        // 超出下边
        if (objBoundingRect.top + objBoundingRect.height > canvasBoundaries.br.y) {
          obj.top = canvasBoundaries.br.y - obj.getScaledHeight();
        }

        obj.strokeUniform = true;

        // 刷新画布
        toRaw(canvas.value).renderAll();
      });
      toRaw(canvas.value).setActiveObject(rect);
    };
    // 删除图形
    const deleteObj = () => {
      // eslint-disable-next-line array-callback-return
      canvas.value.getActiveObjects().map((item) => {
        canvas.value.remove(item);
      });
    };

    // 关闭按钮
    const closeModal = () => {
      if (canvas.value) {
        canvas.value.clear();
        canvas.value.dispose();
        canvas.value = null;
      }

      calibrationModal.value = false;
      sreenshotImgList.value = [];
      selectImg.value = "";
      selecedPicIndex.value = 0;
      emit("closeModal", sreenshotImgList.value);
    };
    let scale = {
      x: 0.75,
      y: 0.5625,
    };

    // ------------------------------------------------------提交------------------------------------------------------------
    const handleOk = async () => {
      // 校验
      const regex = /^[a-zA-Z0-9\u4e00-\u9fa5_]{1,10}$/;

      if (!regex.test(senceName.value)) {
        message.warn("场景名称只允许为1-10位字母、汉字、数字、下划线组合", 2);
        return;
      }

      if (canvas.value.getObjects().length !== 0) {
        // const squareCoordinate = canvas.value.getObjects()[0].aCoords;
        const bounds = canvas.value.getObjects()[0].getBoundingRect(true);
        const topLeft = {
          x: bounds.left,
          y: bounds.top,
        };
        const topRight = {
          x: bounds.left + bounds.width,
          y: bounds.top,
        };
        const bottomRight = {
          x: bounds.left + bounds.width,
          y: bounds.top + bounds.height,
        };
        const bottomLeft = {
          x: bounds.left,
          y: bounds.top + bounds.height,
        };
        const data = {
          filePath: selectImg.value,
          square: [
            {
              x: Math.abs(Math.round(topLeft.x)) / scale.x,
              y: Math.abs(Math.round(topLeft.y)) / scale.y,
            },
            {
              x: Math.abs(Math.round(bottomLeft.x)) / scale.x,
              y: Math.abs(Math.round(bottomLeft.y)) / scale.y,
            },
            {
              x: Math.abs(Math.round(bottomRight.x)) / scale.x,
              y: Math.abs(Math.round(bottomRight.y)) / scale.y,
            },
            {
              x: Math.abs(Math.round(topRight.x)) / scale.x,
              y: Math.abs(Math.round(topRight.y)) / scale.y,
            },
          ],
        };

        const shotRes = await screenshot(data);

        if (shotRes.code === 0) {
          if (currentSence.value.value.includes("fakeSceneId")) {
            const addData = {
              benchPic: selectImg.value, // 基准图
              id: "",
              presetId: activeNodeKey.value,
              name: senceName.value,
              tmpPic: shotRes.data, // 截取图
              tmpSquare: data.square,
            };
            const addSceneRes = await addSence(addData);
            // 接口完成后
            emit("submittingEvent", { name: senceName.value, sence: currentSence.value });
            if (addSceneRes.code === 0) {
              emit("freshImg", {
                src: shotRes.data,
                id: currentSence.value.value,
                newId: addSceneRes.data.id,
                selectImg: selectImg.value,
                tmpSquare: addData.tmpSquare,
              });
              message.success("添加成功");
              closeModal();
            }
          } else {
            const editData = {
              benchPic: selectImg.value, // 基准图
              id: currentSence.value.value,
              presetId: activeNodeKey.value,
              name: senceName.value,
              tmpPic: shotRes.data, // 截取图
              tmpSquare: data.square,
            };
            const editRes = await editSence(editData);
            // 接口完成后
            emit("submittingEvent", { name: senceName.value, sence: currentSence.value });
            if (editRes.code === 0) {
              emit("freshImg", {
                src: shotRes.data,
                id: currentSence.value.value,
                selectImg: selectImg.value,
                tmpSquare: editData.tmpSquare,
                name: senceName.value,
              });
              message.success("更新成功");
              closeModal();
            }
          }
        }
      } else {
        message.warn("您还没有绘制框图");
      }
    };

    return {
      activeNodeKey,
      currentSence,
      calibrationModal,
      senceName,
      openCalibrationModal,
      setCanvasBg,
      historyImgList,
      selectImg,
      canvas,
      template1,
      sreenshotImgList,
      closeModal,
      handleOk,
      addEmpty,
      selecedPicIndex,
      selectPic,
      addHistoryPic,
      getImgMode,
      handleImgMode,
      refreshImgList,
    };
  },
});
</script>

<style scoped lang="less">
.mainBox {
  width: 100%;
  height: 100%;
  display: flex;
  box-sizing: border-box;
}
.leftPart {
  width: 80%;
  height: 670px;
}
.rightPart {
  width: 20%;
  height: 670px;
  margin-left: 10px;
}
#fabricBox {
  width: 960px;
  height: 540px;
  border: 1px solid black;
}
.imageList {
  display: flex;
  overflow-x: scroll;
  margin-top: 2px;
  max-width: 960px;
}
.imageList::-webkit-scrollbar {
  height: 8px;
}
.imageList::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.2);
}
.imageList::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
.activeImg {
  border: 3px solid rgba(0, 0, 0, 0);
}
.graphicTemplate {
  width: 100%;
  height: 40px;
  display: flex;
  flex-direction: row;
  gap: 10px;
}
.imagePreview {
  overflow-y: auto;
  height: 420px;
  overflow-x: hidden;
}
.imagePreview::-webkit-scrollbar {
  width: 5px;
}
.imagePreview::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.2);
}
.imagePreview::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
.addImgBtn {
  width: 100%;
  border-radius: 5px;
  text-align: center;
  margin-top: 5px;
  border: 1px solid black;
  cursor: pointer;
}
.addImgBtn:hover {
  border-color: #1ab5fe;
}
.confirmBtn {
  margin-top: 20px;
  display: flex;
}
.emptyDiv {
  width: 230px;
  height: 120px;
  background: rgb(204, 235, 248);
}
.selecedPic {
  border: 3px solid blue;
  width: 236px;
  height: 126px;
}
.selecedshotPic {
  border: 4px solid #1ab5fe;
}
</style>
