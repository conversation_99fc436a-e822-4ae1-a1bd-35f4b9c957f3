<template>
  <a-upload
    v-model:file-list="fileList"
    :max-count="10"
    :showUploadList="false"
    :beforeUpload="beforeUpload"
    :accept="allowedFileTypes"
  >
    <a-button size="small">
      <UploadOutlined></UploadOutlined>
      上传
    </a-button>
  </a-upload>
</template>
<script lang="ts">
import {
  defineComponent, ref,
} from 'vue';
import type { UploadChangeParam } from 'ant-design-vue';
import { message, Modal } from 'ant-design-vue';
import {  UploadOutlined } from '@ant-design/icons-vue';
import { uploadAlgorithmPic } from '@/api/design';

export default defineComponent({
  components: { UploadOutlined },
  props:{
    presetId:{
      type:String,
      default:''
    }
  },
  setup(props, ctx) {
    const fileList = ref([]);
    const beforeUpload = (file) => {
      console.log(file,'asdasd');
      // fileList.value = [file];
      getUploadFile(file);
      return false;
    };
    const allowedFileTypes = "image/png, image/jpg, image/jpeg"
    const fileChange = (info: UploadChangeParam) => {
      if (info.file.status !== 'uploading') {
        console.log(info.file);
        console.log(fileList.value);
      }
      if (info.file.status === 'done') {
        console.log('done');
      }
      if (info.file.status === 'success') {
        console.log('success');
      }
      if (info.file.status === 'error') {
        console.log('error');
      }
    };
    const getUploadFile = (file) => {
      const upFile = file;

      if (!file.type || !allowedFileTypes.includes(file.type)) {
        // 可以在这里添加提示信息
        message.warn('不支持的文件类型，请重新选择！',2);
        return;
      }

      const formData = new FormData();

      formData.append('sceneFiles', upFile as any);
      formData.append('presetId', props.presetId as string);
      formData.append('model', 'MANUAL');
      // console.log(fileList.value, formData.get('sceneFiles'), 'up');

      uploadAlgorithmPic(formData).then((res) => {
        if (res.code === 0) {
          message.success('操作成功');
          ctx.emit('refreshImgList', `${Math.random()}`);
        }
      });
    };
    return {
      fileList,
      allowedFileTypes,
      fileChange,
      beforeUpload,
    };
  },
});
</script>
<style lang="less" scoped>
.btn {
  font-size: 16px;
  margin-left: 10px;
  cursor: pointer;
}
</style>
