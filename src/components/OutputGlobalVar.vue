<template>
  <div>
    <a-modal
      v-model:visible="visible"
      :title="title"
      okText="保存"
      cancelText="关闭"
      :maskClosable="false"
      width="1000px"
      destroyOnClose
      @ok="handleSave"
    >
      <a-table :columns="columns" :dataSource="dataSource" :pagination="false" bordered>
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'outputValTemp'">
            <a-popover title="结果模板" trigger="click" placement="right">
              <template #content>
                <div style="display: flex;">
                  <a-textarea
                  v-model:value="record.outputValTemp"
                  :rows="10"
                  class="textarea"
                  :id="`text_${record.id}`"
                  :autoSize="true"
                  ></a-textarea>
                  <div style="margin-left: 10px;width: 100px;">
                    <a-space direction="vertical">
                      <h3>可用变量</h3>
                      <a-tooltip>
                        <template #title>数值</template>
                        <a-button @click="addValue(`text_${record.id}`,'VALUE',record.id)" size="small">VALUE</a-button>
                      </a-tooltip>
                      <a-tooltip>
                        <template #title>报警描述</template>
                        <a-button @click="addValue(`text_${record.id}`,'ADESC',record.id)" size="small">ADESC</a-button>
                      </a-tooltip>
                      <a-tooltip>
                        <template #title>报警条件</template>
                        <a-button @click="addValue(`text_${record.id}`,'ALEVEL',record.id)" size="small">ALEVEL</a-button>
                      </a-tooltip>
                      <a-tooltip>
                        <template #title>质量位</template>
                        <a-button @click="addValue(`text_${record.id}`,'STATUS',record.id)" size="small">STATUS</a-button>
                      </a-tooltip>
                    </a-space>

                  </div>
                </div>
                
              </template>
              <a>输出模版</a>
            </a-popover>
          </template>
          <template v-if="column.dataIndex === 'isBind'">
            <a-switch v-model:checked="record.isBind" size='small'></a-switch>
          </template>
          <template v-if="column.dataIndex === 'valName'">
            <a-tooltip v-model:visible="showErrorId" title="不能为空不能有特殊字符" trigger="contextmenu">
              <span style="color: red;font-size: 14px;">*</span><a-input v-model:value="record.valName" size='small' :maxlength="10" style="width: 90%;margin-left: 5px;" @change="verificationName(record.id, record.valName)"></a-input>
            </a-tooltip>
            
            <!-- <div class="ErrMessage" v-if="showErrorId === record.id">不能为空不能有特殊字符</div> -->
          </template>
          <template v-if="column.dataIndex === 'nickName'">
            <a-input v-model:value="record.nickName" size='small' :maxlength="255" show-count></a-input>
          </template>
          <template v-if="column.dataIndex === 'description'">
            <a-input v-model:value="record.description" size='small' :maxlength="255"></a-input>
          </template>
          <template v-if="column.dataIndex === 'pointName'">
            <a-tooltip v-if="record.pointName.length > 10">
            <template #title>{{ record.pointName }}</template>
              {{ record.pointName.slice(0,10)+'...' }}
          </a-tooltip>
          <span v-else>
            {{ record.pointName }}
          </span>
          </template>
        </template>
      </a-table>
    </a-modal>
  </div>
</template>

<script lang="ts">
import type { ColumnsType } from 'ant-design-vue/es/table/interface';
import { computed, defineComponent, ref } from 'vue';
import { message } from 'ant-design-vue';
import { getGlobal, editGlobal } from '@/api/design';

const columns: ColumnsType = [
  {
    title: '识别点名',
    dataIndex: 'pointName',
    width:150
  },
  {
    title: '类型',
    dataIndex: 'valType',
  },
  {
    title: '是否绑定',
    dataIndex: 'isBind',
    width:100
  },
  {
    title: '变量名',
    dataIndex: 'valName',
    width:200
  },
  {
    title: '别名',
    dataIndex: 'nickName',
  },
  {
    title: '描述',
    dataIndex: 'description',
  },
  {
    title: '模版',
    dataIndex: 'outputValTemp',
  },
];

export default defineComponent({
  setup(props) {
    const visible = ref<boolean>(false);
    const title = ref<string>('');
    const dataSource = ref();
    // const outputsTemplate = () => {};
    const openModal = (name, id) => {
      title.value = `绑定全局变量->${name}`;
      getGlobal(id).then((res) => {
        if (res.code === 0) {
          dataSource.value = res.data;
          showErrorId.value = false;
          visible.value = true;
        }
      });
    };


    const addValue = (dom:string,value:string, id:string)=>{
      const inputElement:HTMLInputElement = document.getElementById(dom) as HTMLInputElement;
      // 获取光标位置
      const startPos = inputElement.selectionStart; // 获取光标的起始位置
      const endPos = inputElement.selectionEnd; // 获取光标的结束位置
        // console.log(startPos,endPos)
      // // 获取输入框的当前值
      const currentValue = inputElement.value;
      // // 构造要插入的文本
      const textToInsert = `{{${value}}}`;

      // // 将文本插入到光标位置
      const newValue =  currentValue.substring(0, startPos) + textToInsert + currentValue.substring(endPos);

      // // 更新输入框的值
      inputElement.value = newValue;
      dataSource.value.forEach(element => {
        if(element.id === id){
          element.outputValTemp = newValue;
        }
      });

      // // 重新设置光标位置
      inputElement.selectionStart = startPos + textToInsert.length;
      inputElement.selectionEnd = startPos + textToInsert.length;
    }

    // 输入校验
    const showErrorId = ref<boolean>(false);
    const verificationName = (id,name)=>{
      const myreg = /^(?![!@#$%^&*()+=\[\]{}\\|;:'",.<>?`~])[\w\u4e00-\u9fa5]{1,10}$/;
      if (
        name === ''
        || name=== null
        || !myreg.test(name)
      ) {
        showErrorId.value = true;
        return true;
      } else {
        showErrorId.value = false;
        return false;
      }
    }
    const handleSave = () => {
      let count:number = 0;
      dataSource.value.forEach((item)=>{
        if(verificationName(item.id,item.valName)){
          count++
        }
      })
      if(count){
        return;
      }
      editGlobal(dataSource.value).then((res) => {
        if (res.code === 0) {
          message.success('更新成功');
          visible.value = false;
        }
      });
    };
    return {
      visible,
      title,
      columns,
      dataSource,
      addValue,
      openModal,
      handleSave,
      showErrorId,
      verificationName,
    };
  },
});
</script>

<style scoped>
.textarea {
  resize: none;
}
.ErrMessage{
  color: #ff4d4f;
}
</style>
