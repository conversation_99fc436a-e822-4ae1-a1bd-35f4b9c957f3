<template>
  <div class="canvas-box" id="canvasParent" v-if="props.showCanvas">
    <canvasCluster
      :canvasId="props.canvasClusterId"
      :channelId="currentChannelId"
      ref="canvasClusterRef"
    ></canvasCluster>
  </div>
  <div class="video-loading-box">
    <a-spin :spinning="loading" tip="正在加载视频...">
      <template #indicator>
        <LoadingOutlined></LoadingOutlined>
      </template>
      <div :id="props.id" class="videoBox"></div>
    </a-spin>
  </div>
</template>

<script setup lang="ts">
import { onMounted, ref, onBeforeUnmount, nextTick } from "vue";
import { getChannelVideoLocationDCS } from "@/api/design";
import canvasCluster from "./videoOsdDrawer.vue";
import uEmitter from "@/common/u-event-bus";
import { isEmpty } from "@/common/utils";
import { LoadingOutlined } from "@ant-design/icons-vue";

const props = defineProps({
  id: {
    type: String,
    default: "videoContainer",
  },
  canvasClusterId: {
    type: String,
    default: "videoCanvas",
  },
  showCanvas: {
    type: Boolean,
    default: true,
  },
});

let easyplayer = null; // 视频播放器实例
const config = {
  isLive: true, // 是否直播
  bufferTime: 0.2, // 缓存时长
  stretch: false,
  MSE: false,
  WCS: false,
  hasAudio: false,
  loadTimeReplay: 10,
  loadTimeOut: 3600,
};

const loading = ref(false); // 视频加载中

const currentChannelId = ref(""); // 当前视频的channelId

const videoRatio = ref({
  width: 0,
  height: 0,
}); // 视频分辨率

let timer = null;
let lastTime = 0;
// let

function monitorVideoPlayback() {
  let preTime = lastTime;

  timer = setInterval(async () => {
    if (lastTime === 0) return;
    if (lastTime === preTime) {
      console.warn("检测到视频播放时间未更新，可能出现卡顿！");

      playVideo(currentChannelId.value);
    } else {
      preTime = lastTime;
    }
  }, 3000);
}
// 创建视频播放器
const createVideo = () => {
  const container = document.getElementById(props.id);

  easyplayer = new (window as any).EasyPlayerPro(container, {
    isLive: config.isLive, //默认 true
    bufferTime: 0.3, // 缓存时长
    stretch: true,
    MSE: config.MSE,
    WCS: config.WCS,
    hasAudio: config.hasAudio,
    watermark: { text: { content: "" }, right: 10, top: 10 },
  });
  // 获取视频分辨率
  easyplayer.on("videoInfo", function (audioInfo) {
    console.log("videoInfo", audioInfo);
    videoRatio.value.width = audioInfo.width;
    videoRatio.value.height = audioInfo.height;
    if (props.showCanvas) {
      //画布分辨率
      canvasClusterRef.value.changeScale(videoRatio.value.width, videoRatio.value.height);
      // 更新ws连接byChannelId
      canvasClusterRef.value.reconnectWsByChannelId(currentChannelId.value);
    }
  });
  // 断流的时候触发
  easyplayer.on("play", function (event) {
    console.log("play", event);
    loading.value = false;
    if (timer) {
      timer && clearInterval(timer);
      timer = null;
    }
  });

  easyplayer.on("error", function (ts) {
    loading.value = true;
    timer = setInterval(async () => {
      playVideo(currentChannelId.value);
    }, 3000);
  });
};

// 播放视频
const emit = defineEmits(["videoError", "destroyVideoFinish"]); // 获取流失败上报事件
const playVideo = async (id: string) => {
  loading.value = true;
  // 先清空画布
  if (canvasClusterRef.value) {
    canvasClusterRef.value.clearCanvas();
  }

  // 先断开ws
  if (props.showCanvas) {
    canvasClusterRef.value.closeWs();
  }

  // 赋值当前视频的channelId
  currentChannelId.value = id; // 赋值当前视频的channelId
  // 获取视频地址
  const url = await getChannelVideoLocationDCS(id);
  if (url.code !== 0) {
    emit("videoError", "获取视频参数错误，请稍后或配置后再试");
    return;
  }

  // 开始播放视频
  await easyplayer.play(window.location.origin + url.data.previewUrl);
};

// 切换视频
// 销毁视频播放器
const destroyVideo = async () => {
  await easyplayer.destroy();
  easyplayer = null;
  emit("destroyVideoFinish", () => {
    console.log("销毁视频播放器成功");
  });
  // if (jessibuca === null) {
  //   createVideo();
  // }
};

// 导入通道树时触发
uEmitter.on("refreshProjectTree", () => {
  if (isEmpty(currentChannelId.value)) {
    return;
  }
  playVideo(currentChannelId.value);
});

defineExpose({
  createVideo,
  playVideo,
  destroyVideo,
});

onMounted(() => {
  createVideo();
  monitorVideoPlayback();
});

onBeforeUnmount(() => {
  if (easyplayer) {
    destroyVideo();
    timer && clearInterval(timer);
  }
});

// ---------------------------------------------------osd/canvas------------------------------------

const canvasClusterRef = ref(null);
</script>

<style lang="less" scoped>
.videoBox {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
}

.canvas-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

.video-loading-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
}

:deep(.ant-spin-nested-loading) {
  width: 100%;
  height: 100%;
}

:deep(.ant-spin-container) {
  width: 100%;
  height: 100%;
}

:deep(.easyplayer-loading) {
  display: none !important;
}
</style>
