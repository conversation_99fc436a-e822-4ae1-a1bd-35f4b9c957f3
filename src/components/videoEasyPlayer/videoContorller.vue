<template>
  <div class="videoControl">
    <div class="controls">
      <!-- 全屏 -->
      <div class="fullScreen">
        <!-- <a-tooltip placement="top" title="全屏">
          <CIconFont type="icon-quanping" class="controlcon hoverStyle"></CIconFont>
        </a-tooltip> -->
        <a-radio-group v-model:value="isAuto" @change="changeAutoMode">
          <a-radio value="AUTOMATIC" style="color: #2298fc"> 自动 </a-radio>
          <a-radio value="MANUAL" style="color: #2298fc"> 手动 </a-radio>
        </a-radio-group>
      </div>
      <!-- 静音 -->
      <div class="sound"></div>
      <!-- PTZ控制-->
      <div class="PtzControl">
        <a-tooltip placement="top" title="PTZ控制">
          <CIconFont
            type="icon-shexiangtoukongzhi"
            class="controlcon hoverStyle"
            :class="{ 'ptz-control-active': openPTZControl }"
            @click="ptzControl"
          ></CIconFont>
        </a-tooltip>
      </div>
      <!-- 暂停/调度 -->
      <!-- <div class="autoControl">
        <div class="auto-menu">
          <div
            class="auto-menu-text hoverStyle"
            @mouseenter="openAutoMenu"
            @mouseleave="closeAutoMenu"
          >
            {{ currentAutoMode }}
          </div>
          <div id="auto-menu-list" @mouseenter="openAutoMenu" @mouseleave="closeAutoMenu">
            <div
              class="auto-menu-item"
              :class="{ 'auto-menu-item-active': currentAutoMode === '调度' }"
              @click="resumeTask('AUTOMATIC', '调度')"
            >
              调度
            </div>
            <div
              class="auto-menu-item"
              :class="{ 'auto-menu-item-active': currentAutoMode === '暂停' }"
              @click="pauseTask('MANUAL', '暂停')"
            >
              暂停
            </div>
          </div>
        </div>
      </div> -->
    </div>
    <!-- ---------------------------------------ptz控件-------------------------------- -->
    <ptzControlTools v-show="openPTZControl" :channel-id="channelId" class="ptz-control-tools"></ptzControlTools>
  </div>
</template>

<script setup lang="ts">
// import { pauseMesTask, resumeMesTask } from "@/api/operatorStation";
import uEmitter from "@/common/u-event-bus";
import { ref, watch } from "vue";
import ptzControlTools from "@/components/ptzControlTools.vue";
import { changeMode, changeModeDCS } from "@/api/operatorStation";
import { message } from "ant-design-vue";

const props = defineProps({
  channelId: {
    type: String,
  },
});

const emits = defineEmits(["taskPause"]);

// ----------------------------------------------------------------------切换手动自动----------------------------------------------------
const openAutoMenu = () => {
  const menuListDom = document.getElementById("auto-menu-list");
  menuListDom.style.visibility = "visible";
  menuListDom.style.opacity = "1";
};
const closeAutoMenu = () => {
  const menuListDom = document.getElementById("auto-menu-list");
  menuListDom.style.visibility = "hidden";
  menuListDom.style.opacity = "0";
};

const currentAutoMode = ref("调度");

// 自动恢复
uEmitter.on("MesChannelMode", () => {
  currentAutoMode.value = "调度";
});

const taskPause = (isPause: boolean) => {
  emits("taskPause", isPause);
};

// 恢复调度

// ----------------------------------------------------------摄像头控制------------------------------------------------------

const openPTZControl = ref(false);
const ptzControl = () => {
  if (!props.channelId) return;
  openPTZControl.value = !openPTZControl.value;
};

// ----------------------------------------------------------手动/自动切换-------------------------------------------------

const isAuto = ref("MANUAL");

const changeAutoMode = (value: string) => {
  const data = {
    id: props.channelId,
    mode: isAuto.value,
  };
  changeModeDCS(data).then((res) => {
    if (res.code === 0) {
      message.success("更新成功");
    }
  });
};

uEmitter.on("MesChannelMode", (value: string)=>{
  isAuto.value = value;
});
</script>

<style scoped lang="less">
.videoControl {
  width: 100%;
  height: 100%;
  position: relative;
}
.videoControl:hover .controls {
  display: flex;
}
.controls {
  background-color: #161616;
  box-sizing: border-box;
  display: none;
  flex-direction: row-reverse;
  gap: 10px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 28px;
  width: 100%;
  padding-left: 13px;
  padding-right: 13px;
  color: #a8aca2;
  opacity: 0.8;
  visibility: visible;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.controlcon {
  font-size: 26px;
  color: #a8aca2;
  align-self: center;
  cursor: pointer;
}

.autoControl {
  align-self: center;
}
.auto-menu {
  display: flex;
  position: relative;
}
.auto-menu-text {
  font-size: 14px;
  min-width: 30px;
  height: 20px;
  line-height: 20px;
  cursor: pointer;
  text-align: center;
}
#auto-menu-list {
  position: absolute;
  left: 50%;
  bottom: 100%;
  visibility: hidden;
  opacity: 0;
  transform: translateX(-50%);
  transition: visibility 0.3s, opacity 0.3s;
  background-color: rgba(0, 0, 0, 0.5);
  border-radius: 4px;
  overflow: hidden;
}

.auto-menu-item {
  display: block;
  height: 25px;
  line-height: 25px;
  margin: 0;
  padding: 0 10px;
  cursor: pointer;
  font-size: 14px;
  text-align: center;
  width: 50px;
  color: hsla(0, 0%, 100%, 0.5);
  transition: color 0.3s, background-color 0.3s;
}
.auto-menu-item-active {
  color: #2298fc;
}

.ptz-control-active {
  color: #2298fc;
}

.ptz-control-tools{
  position: absolute;
  bottom: 30px;
  right: 0px;
  width: 300px;
}
</style>
