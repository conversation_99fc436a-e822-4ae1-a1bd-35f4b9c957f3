<template>
  <a-modal
    title="新建通道"
    :visible="visible"
    :mask-closable="false"
    @ok="handleOk"
    @cancel="handleCancel"
    :destroy-on-close="true"
    :width="700"
  >
    <a-form
      :model="channelFrom"
      :label-col="labelCol"
      :wrapper-col="wrapperCol"
      :rules="channelRules"
      ref="channelFormRef"
    >
      <a-form-item label="通道前缀" name="namePrefix">
        <a-input v-model:value="channelFrom.namePrefix" placeholder="请输入通道前缀" />
      </a-form-item>
      <a-form-item label="通道连接IP" name="address">
        <a-input v-model:value="channelFrom.address" placeholder="请输入通道连接IP" />
      </a-form-item>
      <a-form-item label="用户名" name="username">
        <a-input v-model:value="channelFrom.username" placeholder="请输入用户名" />
      </a-form-item>
      <a-form-item label="密码" name="password">
        <a-input v-model:value="channelFrom.password" placeholder="请输入密码" />
      </a-form-item>
      <a-form-item label="接入协议" name="cameraCtrlProtocol">
        <a-select v-model:value="channelFrom.cameraCtrlProtocol" style="width: 100%">
          <a-select-option value="ONVIF">ONVIF</a-select-option>
          <a-select-option value="SDK_HIK">海康控制协议</a-select-option>
          <a-select-option value="SDK_DH">大华控制协议</a-select-option>
          <a-select-option value="SDK_YS">宇视控制协议</a-select-option>
        </a-select>
      </a-form-item>
    </a-form>
    <a-button @click="getChannelInfo" class="rtspBtn" :loading="rtspParsing">获取通道RTSP</a-button>
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      bordered
      rowKey="rtspUrl"
      :scroll="{ y: 620 }"
      :row-selection="rowSelection"
      :pagination="false"
      v-if="dataSource.length"
    ></a-table>
  </a-modal>
</template>

<script setup lang="ts">
import { ref } from "vue";
import type { Rule, FormInstance } from "ant-design-vue/es/form";
import type { TableColumnsType } from "ant-design-vue";
import { validateHost } from "@/common/rules";
import { rtspUrl, addChannelBatch } from "@/api/design";
import { message, Modal } from "ant-design-vue/lib";

// 弹窗状态
const visible = ref(false);
const nodeInfo = ref(null);
const showModal = (treeNode: any) => {
  visible.value = true;
  console.log(treeNode, "treeNode");
  nodeInfo.value = treeNode;
};

// ----------------------------------------------通道表单-------------------------------------------------
interface CHANNELFROM {
  namePrefix: string;
  address: string;
  username: string;
  password: string;
  cameraCtrlProtocol: string;
}
const channelFrom = ref<CHANNELFROM>({
  namePrefix: "",
  address: "",
  username: "",
  password: "",
  cameraCtrlProtocol: "ONVIF",
});

const channelFormRef = ref<FormInstance>();
const labelCol = { span: 6 };
const wrapperCol = { span: 16 };

const prefixValidator = async (_rule: Rule, value: string) => {
  const reg = /^(?![!@#$%^&*()+=\[\]{}\\|;:'",.<>?`~])[\w\u4e00-\u9fa5]{1,20}$/;
  if (!reg.test(value)) {
    return Promise.reject("通道前缀只能包含字母数字汉字且不能为空");
  }
  if (value.length > 15) {
    return Promise.reject("通道前缀不能超过15个字符");
  } else {
    return Promise.resolve();
  }
};
const channelRules: Record<string, Rule[]> = {
  address: [{ required: true, validator: validateHost, trigger: "change" }],
  namePrefix: [{ required: true, validator: prefixValidator, trigger: "change" }],
  username: [{ required: true, trigger: "change" }],
  password: [{ required: true, trigger: "change" }],
};

const clearForm = () => {
  nodeInfo.value = null;
  channelFormRef.value.resetFields();
  rowSelection.value.selectedRowKeys = [];
  dataSource.value = [];
};

// --------------------------------------------------------获取通道号Rtsp地址---------------------------------

const rtspParsing = ref<boolean>(false); // 按钮loading

// 获取Channel信息
const getChannelInfo = async () => {
  const validate = await channelFormRef.value.validateFields();
  if (validate) {
    const data = {
      address: channelFrom.value.address,
      username: channelFrom.value.username,
      password: channelFrom.value.password,
      cameraCtrlProtocol: channelFrom.value.cameraCtrlProtocol,
    };
    rtspParsing.value = true;
    const res = await rtspUrl(data);
    if (res.code === 0) {
      dataSource.value = Object.values(res.data).flat();
    }
    rtspParsing.value = false;
  }
};

const columns: TableColumnsType = [
  {
    title: "通道号",
    dataIndex: "channelNum",
    width: 80,
  },
  {
    title: "RTSP地址",
    dataIndex: "rtspUrl",
  },
];

const dataSource = ref([]);

const rowSelection = ref({
  checkStrictly: false,
  selectedRowKeys: [],
  onChange: (selectedRowKeys: string[], selectedRows) => {
    console.log(`selectedRowKeys: ${selectedRowKeys}`, "selectedRows: ", selectedRows);
    rowSelection.value.selectedRowKeys = selectedRowKeys;
  },
});

const handleOk = async () => {
  if (rowSelection.value.selectedRowKeys.length === 0) {
    message.warning("未选择RTSP地址");
    return;
  }
  const validate = await channelFormRef.value.validateFields();
  if (validate) {
    const rtspStream = dataSource.value.filter((item) =>
      rowSelection.value.selectedRowKeys.includes(item.rtspUrl)
    );

    console.log(rowSelection.value.selectedRowKeys, dataSource.value, rtspStream);
    const data = {
      ...channelFrom.value,
      parentId: nodeInfo.value.id,
      rtspStreamModels: rtspStream,
    };

    const res = await addChannelBatch(data);
    if (res.code === 0) {
      message.success("操作成功");
      emits("addChannelBatch", res.data);
      clearForm();
      visible.value = false;
    }
  }
};
const handleCancel = () => {
  clearForm();
  visible.value = false;
};

defineExpose({
  showModal,
});

const emits = defineEmits(["addChannelBatch"]);
</script>

<style lang="less" scoped>
.rtspBtn {
  margin-left: 492px;
  margin-bottom: 10px;
}
</style>
