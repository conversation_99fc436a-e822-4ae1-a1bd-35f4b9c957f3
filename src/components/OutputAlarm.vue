<template>
  <div>
    <a-modal
      v-model:visible="visible"
      title="报警配置"
      okText="保存"
      cancelText="关闭"
      :maskClosable="false"
      width="600px"
      destroyOnClose
      @ok="saveAlarm"
    >
      <a-tabs v-model:activeKey="activeKey" size="small" type="card">
        <a-tab-pane v-for="item in tabList" :key="item.alarmId" :tab="`${item.pointName}`" @change="">
          <div>
            <span>点名：</span
            ><a-textarea
              v-model:value="item.pointName"
              size="small"
              style="width: 40%"
              readonly
              :autoSize="true"
            ></a-textarea>
            <span style="margin-left: 10px">描述：</span
            ><a-textarea v-model:value="item.desc" size="small" style="width: 40%" readonly :autoSize="true"></a-textarea>
          </div>
          <div class="alarmType">{{ alarmTypeName[item.alarmType] }}</div>
          <div>
            <table class="table" aria-describedby="报警配置">
              <thead>
                <tr>
                  <th scope="col"></th>
                  <th scope="col">界限值</th>
                  <th scope="col">报警文本</th>
                </tr>
              </thead>
              <tbody>
                <tr v-for="(tr, index) in item.alarm" :key="index">
                  <td scope="row" style="border: 1px solid rgb(190, 190, 190); width: 100px">
                    <a-checkbox v-model:checked="tr.isBind" :disabled="!item.overrideAlarm">{{ grade[tr.type] }}</a-checkbox>
                  </td>
                  <td style="border: 1px solid rgb(190, 190, 190)">
                    <a-input-number v-model:value="tr.val" size="small" :disabled="!item.overrideAlarm" :placeholder="tr.placeHolder" :controls="false" style="width: 100%;"></a-input-number>
                  </td>
                  <td style="border: 1px solid rgb(190, 190, 190)">
                    <a-input v-model:value="tr.desc" size="small" :disabled="!item.overrideAlarm" :maxlength="255"></a-input>
                  </td>
                </tr>
              </tbody>
            </table>
          </div>
          <div class="ICSstyle">
            <a-form
              :model="item.formState"
              :rules="rules"
              ref="formRef"
              :wrapper-col="{ span: 12 }"
            >
              <a-form-item label="ICS上报" name="reportToIcs">
                <a-checkbox v-model:checked="item.formState.reportToIcs" @change="changeICS(item)"></a-checkbox>
              </a-form-item>

              <a-form-item label="开启质量位" name="qualityEnable" v-if="item.formState.reportToIcs">
                <a-checkbox v-model:checked="item.formState.qualityEnable"></a-checkbox>
              </a-form-item>
              <a-form-item label="点域" name="namespace" v-if="item.formState.reportToIcs">
                <a-input v-model:value="item.formState.namespace" autocomplete="off" size="small"></a-input>
              </a-form-item>
              <a-form-item label="点名" name="tag" v-if="item.formState.reportToIcs">
                <a-input v-model:value="item.formState.tag" autocomplete="off" size="small"></a-input>
              </a-form-item>
              <a-form-item label="点项" name="item" v-if="item.formState.reportToIcs">
                <a-input v-model:value="item.formState.item" autocomplete="off" size="small"></a-input>
              </a-form-item>
            </a-form>
          </div>
        </a-tab-pane>
        <template #renderTabBar="{ DefaultTabBar, ...props }">
          <component :is="DefaultTabBar" v-bind="props" :style="{ height: '25px' }" />
        </template>
      </a-tabs>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref } from 'vue';
import { message } from 'ant-design-vue';
import { getAlarm, editAlarm } from '@/api/design';
import type { FormInstance } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';

export default defineComponent({
  setup(props, { emit }) {
    const visible = ref<boolean>(false);
    const tabList = ref([]);
    const activeKey = ref('aaa');
    const algorithmInstanceId = ref<string>('');
    const getAlarmList = (id) => {
      algorithmInstanceId.value = id;
      getAlarm(id).then((res) => {
        if (res.code === 0) {
          if (res.data.infos.length !== 0) {
            tabList.value = res.data.infos;
            activeKey.value = res.data.infos[0].alarmId;
            visible.value = true;
            tabList.value.forEach((item)=>{
              item.formState = {
                reportToIcs: item.reportToIcs,
                qualityEnable: item.qualityEnable,
                namespace: item.namespace,
                tag: item.tag,
                item: item.item,
              }
            })
          }
        }
      });
    };
    const saveAlarm = () => {
      const data = {
        algorithmInstanceId: algorithmInstanceId.value,
        infos: tabList.value,
      };
      data.infos.forEach((item)=>{
        item.reportToIcs = item.formState.reportToIcs;
        item.qualityEnable = item.formState.qualityEnable;
        item.namespace = item.formState.namespace;
        item.tag = item.formState.tag;
        item.item = item.formState.item;
      })
      // formRef.value.validateFields().then(()=>{
        editAlarm(data).then((res) => {
          if (res.code === 0) {
            let count = 0;
            message.success('更新成功');

            tabList.value.forEach((item) => {
              item.alarm.forEach((element) => {
                if (element.isBind) {
                  count++;
                }
              });
            });

            emit('changeAlarm', algorithmInstanceId.value, count);
            visible.value = false;
          }
        });
      // })

    };
    // 报警名称
    const alarmTypeName = {
      OFF_LIMIT_ALARM: '越限报警',
      DEVIATION_ALARM: '偏差报警',
      RATE_OF_CHANGE_ALARM: '变化率报警',
    };
    const grade = {
      HH: '高高',
      H: '高',
      L: '低',
      LL: '低低',
    };


    // ===========================ics上报=======================================

    const formRef = ref<FormInstance>(null);

    const formState = reactive({
      reportToIcs:false,
      namespace:'',
      tag:'',
      item:'',
    })


    const validatePoint = async (_rule: any, value: string) => {
      const varObj = {
        namespace:'点域',
        tag:'点名',
        item:'点项',
      }
      if (value === '' || value === null) {
        return Promise.reject(`${varObj[_rule.field]}不能为空`);
      }
    };
    const rules = {
      namespace: [{ required: true, validator: validatePoint, trigger: 'change' }],
      tag: [{ required: true, validator: validatePoint, trigger: 'change' }],
      item: [{ required: true, validator: validatePoint, trigger: 'change' }],
    };

    const changeICS = (item)=>{
     item.formState.namespace = '';
     item.formState.tag = '';
     item.formState.item = '';
    }
    return {
      tabList,
      activeKey,
      getAlarmList,
      saveAlarm,
      visible,
      alarmTypeName,
      grade,
      formState,
      formRef,
      rules,
      changeICS,
    };
  },
});
</script>

<style scoped>
.alarmType {
  width: 30%;
  background-color: #1d41ab;
  border: 1px solid black;
  border-radius: 5px 5px 5px 5px;
  text-align: center;
  vertical-align: middle;
  margin-top: 10px;
  color: white;
}
.table {
  width: 100%;
  border-collapse: collapse;
  border: 2px solid rgb(200, 200, 200);
  letter-spacing: 1px;
  margin-top: 10px;
}
:deep(.ant-form-item){
  margin-bottom:5px
}
</style>
