<template>
  <Transition name="modal">
    <div v-if="show" class="modal-mask">
      <div :class="!isDcs?'modal-container':'modal-containerForDcs'">
        <div class="modal-header" v-if="!isDcs">
          <slot name="header">
            <span>截图</span>
            <div class="closeX" @click="close()"></div>
          </slot>
        </div>
        <div class="modal-header" v-else>
          <slot name="header">
            <!-- <close-circle-outlined @click="close()" class="closeX" /> -->
            <div class="closeXList" @click="close()">
              <CloseCircleOutlined></CloseCircleOutlined>
            </div>
          </slot>
        </div>
        <div class="modal-body">
          <slot name="body">default body</slot>
        </div>

        <!-- <div class="modal-footer">
          <slot name="footer">
            <a-button
              class="modal-default-button"
              @click="close()"
            >确定</a-button>
          </slot>
        </div> -->
      </div>
    </div>
  </Transition>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { CloseCircleOutlined  } from '@ant-design/icons-vue';

export default defineComponent({
  props:{
    isDcs:{
      type:Boolean,
      default:false,
    }
  },
  components:{
    CloseCircleOutlined
  },
  setup(props,{emit}){
    const show = ref<boolean>(false);
    const close = ()=>{
      show.value = false;
      emit('closeModal')
    }
    return {
      show,
      close
    }
  }
})
</script>

<style scoped>
.modal-mask {
  position: fixed;
  z-index: 9998;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  transition: opacity 0.3s ease;
}

.modal-container {
  width: 1030px;
  height: 650px;
  margin: auto ;
  padding: 12px 20px;
  background: url('../assets/border5.png');
  background-repeat: no-repeat;
  background-position: top center;
  background-size: 100% 100%;
  transition: all 0.3s ease;
}

.modal-containerForDcs {
  width: 1030px;
  height: 800px;
  margin: auto ;
  /* padding: 12px 20px; */
  background-repeat: no-repeat;
  background-position: top center;
  background-size: 100% 100%;
  transition: all 0.3s ease;
}

.modal-header{
  margin-top: 10px;
  margin-left: 15px;
  color: #fff;
  font-weight: 600;
  font-size: 16px;
  /* display: flex; */
}

.closeX{
  height: 30px;
  width: 30px;
  float: right;
  background-color: rgba(0, 0, 0, 0);
  cursor: pointer;
}

.closeXList{
  height: 30px;
  width: 30px;
  float: right;
  margin-right: 45px;
  background-color: rgba(0, 0, 0, 0);
  cursor: pointer;
}
.modal-body {
  margin-top: 35px;
  margin-left: 10px;
}

.modal-default-button {
  float: right;
}


.modal-enter-from {
  opacity: 0;
}

.modal-leave-to {
  opacity: 0;
}

.modal-enter-from .modal-container,
.modal-leave-to .modal-container {
  -webkit-transform: scale(1.1);
  transform: scale(1.1);
}

</style>