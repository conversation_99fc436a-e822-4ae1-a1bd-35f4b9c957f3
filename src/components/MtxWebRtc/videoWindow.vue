<template>
  <!-- <div v-if="currentTaskId"> -->

  <div class="control-box" v-if="props.showControl">
    <videoControl
      ref="videoControlRef"
      :channelId="currentChannelId"
      :presetId="currentPresetId"
      :taskDetail="props.taskInfo"
      @taskPause="taskPause"
      @full-screen="fullScreen"
    ></videoControl>
  </div>

  <div class="canvas-box" :id="`${props.canvasClusterId}Parent`">
    <canvasCluster
      :canvasId="props.canvasClusterId"
      :channelId="currentChannelId"
      ref="canvasClusterRef"
    ></canvasCluster>
  </div>
  <div class="video-box">
    <a-spin tip="视频加载中..." :spinning="spinning">
      <template #indicator>
        <LoadingOutlined></LoadingOutlined>
      </template>
      <video
        ref="videoCluster"
        autoplay
        muted
        class="video-cluster"
        @loadeddata="finishFirstKey"
        style="background-color: black"
        playsinline
      ></video>
    </a-spin>
  </div>

  <!-- </div> -->
  <!-- <a-empty v-else /> -->
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, onBeforeUnmount } from "vue";
import { MediaMTXWebRTCReader } from "@/components/MtxWebRtc/webRtcReader";
import HWebsocket from "@/common/h-websocket";
import canvasCluster from "./videoOsdDrawer.vue";
import videoControl from "./videoContorller.vue";
import { LoadingOutlined } from "@ant-design/icons-vue";
import { getDcsVideoResolution, getChannelVideoLocationDCS } from "@/api/design";
import SetInterval from "@/common/SetInterval";

const videoInstance = ref(null);
const videoCluster = ref(null);

const currentPresetId = ref<string>(""); // 当前的预置点id
const currentInspectItemId = ref<string>(""); // 当前的巡检项id

const currentVideoInfo = ref<any>(); // 当前视频信息
const props = defineProps({
  showControl: {
    type: Boolean,
    default: true,
  },
  canvasClusterId: {
    type: String,
    default: "videoCanvas",
  },
  taskInfo: {
    type: Object,
  },
});
let timer = null;
let lastTime = 0;
let currentTime = 0;

// 视频播放监控检测是否卡顿
function monitorVideoPlayback(videoElement) {
  timer = setInterval(async () => {
    currentTime = videoElement.currentTime !== 0 ? videoElement.currentTime : 0.1;
    if (currentTime === 0) return;
    if (currentTime === lastTime) {
      console.warn("检测到视频播放时间未更新，可能出现卡顿！");
      spinning.value = true;
      if (videoInstance.value) {
        videoInstance.value.close();
        const url = await getChannelVideoLocationDCS(currentChannelId.value);
        if (url.code === 0) {
          setVideoClusterForChannelTree(currentChannelId.value, url.data);
        }

        // videoInstance.value.start();
      }
    } else {
      lastTime = currentTime;
      // spinning.value = false;
    }
  }, 3000);
}

// 视频loading
const spinning = ref<boolean>(false);
const finishFirstKey = () => {
  if (videoCluster.value.srcObject) {
    const tracks = videoCluster.value.srcObject.getTracks();
    console.log(tracks, "tracks");
    tracks.forEach((track) => {
      if (track.kind === "video") {
        const { width, height } = track.getSettings();
        console.log(width, height, "tracks");
        canvasClusterRef.value.changeScale(width, height);
        // 更新ws连接byChannelId
        canvasClusterRef.value.reconnectWsByChannelId(currentChannelId.value);
      }
    });
    spinning.value = false;
  } else {
    emit("videoError", "获取视频参数错误，请稍后或配置后再试");
  }
};

const fullScreen = () => {
  console.log("fullScreen");
  const tracks = videoCluster.value.srcObject.getTracks();
  tracks.forEach((track) => {
    if (track.kind === "video") {
      const { width, height } = track.getSettings();
      console.log("width",width);
      canvasClusterRef.value.changeScale(width, height);
      // 更新ws连接byChannelId
    }
  });
};

/**
 * @param channelIdList 当前任务配置的通道id列表
 * @param taskId 任务id
 * @param taskDetail 第一组数据
 */

// 调度任务视频
const setVideoClusterForTask = async (channelIdList: string[], taskDetail) => {
  nextTick(async () => {
    // 判断当前任务是否运行
    if (taskDetail.length === 0) return;
    const findRunning = taskDetail.findIndex((item) => item.status === "RUNNING");
    if (findRunning !== -1) {
      currentChannelId.value = channelIdList[findRunning];
      currentPresetId.value = taskDetail[findRunning].id;
      currentInspectItemId.value = taskDetail[findRunning].inspectItemId;
    } else {
      currentChannelId.value = channelIdList[0];
      currentPresetId.value = taskDetail[0].id;
      currentInspectItemId.value = taskDetail[0].inspectItemId;
    }

    // 初始化画布
    const VideoResolution = await getDcsVideoResolution({
      id: currentChannelId.value,
      type: "channel",
    });
    const { width, height } = VideoResolution.data;
    canvasClusterRef.value.changeScale(width, height);
    // 更新ws连接byChannelId
    canvasClusterRef.value.reconnectWsByChannelId(currentChannelId.value);

    // 创建video实例
    // videoInstance.value =

    // 监听任务执行进度
    // openWs(WorkId, PointId);

    // 视频loading
    // spinning.value = true;
  });
};

// 通道树视频
const setVideoClusterForChannelTree = (channelId: string, videoInfo: any) => {
  currentVideoInfo.value = videoInfo;
  nextTick(async () => {
    if (videoInstance.value) {
      videoInstance.value.close();
    }

    currentChannelId.value = channelId;
    spinning.value = true;
    // 创建video实例
    videoInstance.value = new MediaMTXWebRTCReader({
      url: `${videoInfo.previewUrl}/whep`,
      onError: (err) => {
        console.error(err);
      },
      onTrack: (evt) => {
        videoCluster.value.srcObject = evt.streams[0];
      },
    }); // 视频实例化
  });
};

// 初始化ws
const currentChannelId = ref("");

const canvasClusterRef = ref(null);
let ws = null;
function openWs(workId: string, pointId: string) {
  ws = new HWebsocket(
    `${(window as any).g.wsApi}/websocket/mes-task-schedule/${workId}/${pointId}`
  );
  ws.onMessage = (evt) => {
    const wsData = JSON.parse(evt.data);
    handleWs(wsData);
  };
}

// 处理ws数据
async function handleWs(wsData) {
  // 调度任务执行进度

  if (isPause.value) return;
  emit("changeStep", wsData[0].inspectItemId);
  // 切换视频通道
  videoInstance.value.changeVideo(wsData[0].channelId);
  // 更新分辨率
  const VideoResolution = await getDcsVideoResolution({
    id: wsData[0].channelId,
    type: "channel",
  });
  const { width, height } = VideoResolution.data;
  canvasClusterRef.value.changeScale(width, height);

  // 更新ws连接byChannelId
  currentChannelId.value = wsData[0].channelId;
  currentPresetId.value = wsData[0].presetId;

  canvasClusterRef.value.reconnectWsByChannelId(currentChannelId.value);
}

const isPause = ref(false);

const taskPause = (val) => {
  console.log(val, "val");
  isPause.value = val;
};

const closeVideo = () => {
  if (videoInstance.value) {
    videoInstance.value.close();
  }
};

const clearCanvas = () => {
  if (canvasClusterRef.value) {
    canvasClusterRef.value.clearCanvas();
  }
};

defineExpose({
  // setVideoClusterForTask,
  setVideoClusterForChannelTree,
  closeVideo,
  clearCanvas,
});

const emit = defineEmits(["changeStep", "videoError"]);

onMounted(() => {
  monitorVideoPlayback(videoCluster.value); // 监听视频播放时间
  SetInterval.close("loginTimer");
});

onBeforeUnmount(() => {
  if (ws) {
    ws.close();
    ws = null;
  }
  timer && clearInterval(timer);
  videoInstance.value && videoInstance.value.close();
});
</script>

<style lang="less" scoped>
/** 控制区域 */
.control-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1001;
}
/** 画布区域 */
.canvas-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

/** 视频播放区域 */
.video-box {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.video-cluster {
  width: 100%;
  height: 100%;
}
:deep(.ant-spin-nested-loading) {
  height: 100%;
}
:deep(.ant-spin-container) {
  height: 100%;
}
</style>
