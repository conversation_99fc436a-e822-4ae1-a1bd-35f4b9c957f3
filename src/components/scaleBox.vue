<template>
  <div
    class="ScaleBox"
    ref="ScaleBox"
    :style="{
      width: width + 'px',
      height: height + 'px',
    }"
  >
    <slot name="content"></slot>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, onUnmounted, ref } from 'vue';
export default defineComponent({
name:'scaleBox',
setup(){
  const scale = ref<number>(0);
  const width = ref<number>(1920);
  const height = ref<number>(1080);
  const ScaleBox = ref(null);

  const getScale = ()=>{
    const wh = window.innerHeight / height.value;
    const ww = window.innerWidth / width.value;
    console.log(wh, ww);

    return ww < wh ? ww : wh;
  }

  // const setScale = ()=>{
  //   scale.value = getScale();
  //   if(ScaleBox.value){
  //     ScaleBox.value.style.setProperty("--scale", scale.value);
  //   }
  // }
  function setScale(){
    scale.value = getScale();
    if(ScaleBox.value){
      ScaleBox.value.style.setProperty("--scale", scale.value);
    }
  }

  const debounce = (fn)=>{
    const delays = 500;
    let timer;
    return function () {
      const th = this;
      const args = arguments;
      if (timer) {
        clearTimeout(timer);
      }
      timer = setTimeout(function () {
        timer = null;
        fn.apply(th, args);
      }, delays);
    };
  }
  onMounted(()=>{
    setScale();
    window.addEventListener("resize",debounce(setScale))
  })

  onUnmounted(() => {
    window.removeEventListener('resize', debounce(setScale))
  })
  return{
    ScaleBox,
    width,
    height

  }
}
})
</script>

<style lang="scss">
#ScaleBox {
  --scale: 1;
}
.ScaleBox {
  position: absolute;
  // transform-style: preserve-3d;
  transform: scale(var(--scale)) translate(-0%, -0%);
  display: flex;
  flex-direction: column;
  transform-origin: 0 0;
  left: 0px;
  top: 0px;
  transition: 0.1s;
  z-index: 200;
  // background: rgba(255, 0, 0, 0.3);
}

</style>
