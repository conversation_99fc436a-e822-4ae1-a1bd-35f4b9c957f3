export const getType = (type) => {
  switch (type) {
    case "SQUARE":
      return "icon-xingzhuang-juxing";
    case "CIRCLE":
      return "icon-yuanxingweixuanzhong";
    case "POLYGON":
      return "icon-duobianxing";
    case "ELLIPSE":
      return "icon-tuoyuanxing";
    case "LINE":
      return "icon-zhixian";
    case "LINE_ARROW":
      return "icon-jiantou_youshang_o";
    case "POINT":
      return "icon-dian";
    default:
      break;
  }
};

export const IconList = {
  directory: "icon-a-Property1changquguanli1",
  channel: "icon-a-Property1qiangji",
  preset: "icon-a-Property1yuzhidian",
  project: "icon-shishigongkuang",
  ops: "icon-yonghuguanli-1",
  ens: "icon-yonghuguanli-1",
  "control-flow": "icon-sheji",
  "web-config": "icon-13",
  "business-root": "icon-a-Property1shipinfenxi",
  'algorithm-instance':'icon-suanfaku',
  'output-param':'icon-shuchu'
};
