var JSWebrtc = {
  Player: null,
  VideoElement: null,
  CreateVideoElements() {
    const elements = document.querySelectorAll('.jswebrtc');
    for (let i = 0; i < elements.length; i++) {
      new JSWebrtc.VideoElement(elements[i]);
    }
  },
  FillQuery(query_string, obj) {
    obj.user_query = {};
    if (query_string.length == 0) return;
    if (query_string.indexOf('?') >= 0) query_string = query_string.split('?')[1];
    const queries = query_string.split('&');
    for (let i = 0; i < queries.length; i++) {
      const query = queries[i].split('=');
      obj[query[0]] = query[1];
      obj.user_query[query[0]] = query[1];
    }
    if (obj.domain) obj.vhost = obj.domain;
  },
  ParseUrl(rtmp_url) {
    const a = document.createElement('a');
    a.href = rtmp_url
      .replace('rtmp://', 'http://')
      .replace('webrtc://', 'http://')
      .replace('rtc://', 'http://');
    let vhost = a.hostname;
    let app = a.pathname.substr(1, a.pathname.lastIndexOf('/') - 1);
    const stream = a.pathname.substr(a.pathname.lastIndexOf('/') + 1);
    app = app.replace('...vhost...', '?vhost=');
    if (app.indexOf('?') >= 0) {
      const params = app.substr(app.indexOf('?'));
      app = app.substr(0, app.indexOf('?'));
      if (params.indexOf('vhost=') > 0) {
        vhost = params.substr(params.indexOf('vhost=') + 'vhost='.length);
        if (vhost.indexOf('&') > 0) {
          vhost = vhost.substr(0, vhost.indexOf('&'));
        }
      }
    }
    if (a.hostname == vhost) {
      const re = /^(\d+)\.(\d+)\.(\d+)\.(\d+)$/;
      if (re.test(a.hostname)) vhost = '__defaultVhost__';
    }
    let schema = 'rtmp';
    if (rtmp_url.indexOf('://') > 0) schema = rtmp_url.substr(0, rtmp_url.indexOf('://'));
    let { port } = a;
    if (!port) {
      if (schema === 'http') {
        port = 80;
      } else if (schema === 'https') {
        port = 443;
      } else if (schema === 'rtmp') {
        port = 1935;
      } else if (schema === 'webrtc' || schema === 'rtc') {
        port = 1985;
      }
    }
    const ret = {
      url: rtmp_url,
      schema,
      server: a.hostname,
      port,
      vhost,
      app,
      stream,
    };
    JSWebrtc.FillQuery(a.search, ret);
    return ret;
  },
  HttpPost(url, data) {
    return new Promise((resolve, reject) => {
      let xhr = new XMLHttpRequest();
      xhr.onreadystatechange = function () {
        if (xhr.readyState === 4 && xhr.status >= 200 && xhr.status < 300) {
          const respone = JSON.parse(xhr.responseText);
          xhr.onreadystatechange = new Function();
          xhr = null;
          resolve(respone);
        }
      };
      xhr.open('POST', url, true);
      xhr.timeout = 5e3;
      xhr.responseType = 'text';
      xhr.setRequestHeader('Content-Type', 'application/json');
      xhr.send(data);
    });
  },
};
if (document.readyState === 'complete') {
  JSWebrtc.CreateVideoElements();
} else {
  document.addEventListener('DOMContentLoaded', JSWebrtc.CreateVideoElements);
}
JSWebrtc.Player = (function () {
  const Player = function (url, options) {
    // if(!url){
    //   throw 'url is null';
    // }
    this.options = options || {};
    // if (!url.match(/^webrtc?:\/\//)) {
    //   throw 'JSWebrtc just work with webrtc';
    // }
    if (!this.options.video) {
      throw 'VideoElement is null';
    }
    this.urlParams = JSWebrtc.ParseUrl(url);
    this.pc = null;
    this.autoplay = !!options.autoplay || false;
    this.paused = true;
    if (this.autoplay) this.options.video.muted = true;
    this.startLoading();
    this.close = ()=>{
      
      this.pause();
      this.destroy();
      console.log(this.pc);
    }
  };
  Player.prototype.startLoading = function () {
    const _self = this;
    if (_self.pc) {
      _self.pc.close();
    }
    _self.pc = new RTCPeerConnection(null);
    _self.pc.ontrack = function (event) {
      _self.options.video.srcObject = event.streams[0];
    };
    _self.pc.addTransceiver('audio', { direction: 'recvonly' });
    _self.pc.addTransceiver('video', { direction: 'recvonly' });
    _self.pc
      .createOffer()
      .then((offer) => _self.pc.setLocalDescription(offer).then(() => offer))
      .then(
        (offer) => new Promise((resolve, reject) => {
          const port = _self.urlParams.port || 1985;
          let api = _self.urlParams.user_query.play || '/rtc/v1/play/';
          if (api.lastIndexOf('/') != api.length - 1) {
            api += '/';
          }
          // let url = `https://${_self.urlParams.server}:${port}${api}`;
          // let url = `https://${_self.urlParams.server}/srs-api${api}`;
          let url = window.g.videoUrl;
          for (const key in _self.urlParams.user_query) {
            if (key != 'api' && key != 'play') {
              url += `&${key}=${_self.urlParams.user_query[key]}`;
            }
          }
          const data = {
            api: url,
            streamurl: _self.urlParams.url,
            clientip: null,
            sdp: offer.sdp,
          };
          // console.log(`offer: ${JSON.stringify(data)}`);
          JSWebrtc.HttpPost(url, JSON.stringify(data)).then(
            (res) => {
              // console.log(`answer: ${JSON.stringify(res)}`);
              resolve(res.sdp);
            },
            (rej) => {
              reject(rej);
            },
          );
        }),
      )
      .then((answer) => _self.pc.setRemoteDescription(new RTCSessionDescription({ type: 'answer', sdp: answer })))
      .catch((reason) => {
        throw reason;
      });
    if (this.autoplay) {
      this.play();
    }
  };
  Player.prototype.play = function (ev) {
    if (this.animationId) {
      return;
    }
    this.animationId = requestAnimationFrame(this.update.bind(this));
    this.paused = false;
  };
  Player.prototype.pause = function (ev) {
    if (this.paused) {
      return;
    }
    cancelAnimationFrame(this.animationId);
    this.animationId = null;
    this.isPlaying = false;
    this.paused = true;
    this.options.video.pause();
    if (this.options.onPause) {
      this.options.onPause(this);
    }
  };
  Player.prototype.stop = function (ev) {
    this.pause();
  };
  Player.prototype.destroy = function () {
    this.pause();
    this.pc.close();
    this.pc && this.pc.close() && this.pc.destroy();
    this.pc = null;
    console.log('destroyed PC');
  };
  Player.prototype.update = function () {
    this.animationId = requestAnimationFrame(this.update.bind(this));
    if (this.options.video.readyState < 4) {
      return;
    }
    if (!this.isPlaying) {
      this.isPlaying = true;
      this.options.video.play();
      if (this.options.onPlay) {
        this.options.onPlay(this);
      }
    }
  };
  return Player;
}());

export default JSWebrtc;
