<template>
  <div class="ErrBox">
    <div class="Err">
      配置信息错误，请重新配置
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  name: 'CError404',
  setup() {
    return {};
  },
});
</script>
<style>
  .ErrBox{
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: row;
    align-items: center;
    justify-content: center;
    font-size: 64px;

  }
</style>