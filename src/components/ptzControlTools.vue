<template>
  <div class="ptz-container">
    <div class="controller">
      <div
        class="sector"
        @mousedown="move('UP')"
        @mouseup="stopMove('UP')"
        @mouseleave="stopMove('UP')"
      />
      <div
        class="sector"
        @mousedown="move('RIGHT')"
        @mouseup="stopMove('RIGHT')"
        @mouseleave="stopMove('RIGHT')"
      />
      <div
        class="sector"
        @mousedown="move('DOWN')"
        @mouseup="stopMove('DOWN')"
        @mouseleave="stopMove('DOWN')"
      />
      <div
        class="sector"
        @mousedown="move('LEFT')"
        @mouseup="stopMove('LEFT')"
        @mouseleave="stopMove('LEFT')"
      />
      <div class="center-circle"></div>
      <!-- 如果用 Antd Icon，换成 <UpOutlined /> 等 -->
      <i class="arrow up"><UpOutlined></UpOutlined></i>
      <i class="arrow down"><DownOutlined></DownOutlined></i>
      <i class="arrow left"><LeftOutlined></LeftOutlined></i>
      <i class="arrow right"><RightOutlined></RightOutlined></i>
    </div>
    <div class="ptz-btns">
      <div class="ptz-btn">
        <div class="ptz-expand">
          <span class="icon-title">
            <CIconFont
              type="icon-fangda-copy"
              @mousedown="move('ZOOM_ADD')"
              @mouseup="stopMove('ZOOM_ADD')"
              @mouseleave="stopMove('ZOOM_ADD')"
              title="调焦+"
            ></CIconFont>
          </span>
        </div>
        <div class="ptz-title">缩放</div>
        <div class="ptz-expand">
          <span class="icon-title">
            <CIconFont
              type="icon-suoxiao1-copy"
              @mousedown="move('ZOOM_DEC')"
              @mouseup="stopMove('ZOOM_DEC')"
              @mouseleave="stopMove('ZOOM_DEC')"
              title="调焦-"
            ></CIconFont>
          </span>
        </div>
      </div>
      <div class="ptz-btn">
        <div class="ptz-expand">
          <span class="icon-title">
            <CIconFont
              type="icon-a-guangquan"
              @mousedown="move('APERTURE_ADD')"
              @mouseup="stopMove('APERTURE_ADD')"
              @mouseleave="stopMove('APERTURE_ADD')"
              title="光圈+"
            ></CIconFont>
          </span>
        </div>
        <div class="ptz-title">光圈</div>
        <div class="ptz-expand">
          <span class="icon-title">
            <CIconFont
              type="icon-guangquan-"
              @mousedown="move('APERTURE_DEC')"
              @mouseup="stopMove('APERTURE_DEC')"
              @mouseleave="stopMove('APERTURE_DEC')"
              title="光圈-"
            ></CIconFont>
          </span>
        </div>
      </div>
      <div class="ptz-btn">
        <div class="ptz-expand">
          <span class="icon-title">
            <CIconFont
              type="icon-jujiao"
              @mousedown="move('FOCUS_ADD')"
              @mouseup="stopMove('FOCUS_ADD')"
              @mouseleave="stopMove('FOCUS_ADD')"
              title="聚焦+"
            ></CIconFont>
          </span>
        </div>
        <div class="ptz-title">变焦</div>
        <div class="ptz-expand">
          <span class="icon-title">
            <CIconFont
              type="icon-jujiao-"
              @mousedown="move('FOCUS_DEC')"
              @mouseup="stopMove('FOCUS_DEC')"
              @mouseleave="stopMove('FOCUS_DEC')"
              title="聚焦-"
            ></CIconFont>
          </span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref } from "vue";
import { PTZContorls, PTZStopContorls } from "@/api/design";
import { UpOutlined, DownOutlined, LeftOutlined, RightOutlined } from "@ant-design/icons-vue";

const props = defineProps({
  channelId: {
    type: String,
  },
});

// 摄像头是否在移动
const isMove = ref(false);
const move = (direction: string) => {
  switch (direction) {
    case "UP":
      PTZContorlsHandeler("UP");
      break;
    case "RIGHT":
      PTZContorlsHandeler("RIGHT");
      break;
    case "LEFT":
      PTZContorlsHandeler("LEFT");
      break;
    case "DOWN":
      PTZContorlsHandeler("DOWN");
      break;
    case "FOCUS_ADD":
      PTZContorlsHandeler("FOCUS_ADD");
      break;
    case "FOCUS_DEC":
      PTZContorlsHandeler("FOCUS_DEC");
      break;
    case "ZOOM_ADD":
      PTZContorlsHandeler("ZOOM_ADD");
      break;
    case "ZOOM_DEC":
      PTZContorlsHandeler("ZOOM_DEC");
      break;
    case "APERTURE_ADD":
      PTZContorlsHandeler("APERTURE_ADD");
      break;
    case "APERTURE_DEC":
      PTZContorlsHandeler("APERTURE_DEC");
      break;
    default:
      break;
  }
};

const PTZContorlsHandeler = (action) => {
  const data = {
    channelId: props.channelId,
    action,
  };

  PTZContorls(data).then((res) => {
    isMove.value = true;
    console.log(res);
  });
};

const PTZStopContorlsHandeler = (action, channelId) => {
  PTZStopContorls({ action: action, channelId: channelId }).then((res) => {
    if (res.code === 0) {
      isMove.value = false;
      emit("moveOver", res.data);
    }
  });
};

const emit = defineEmits(["moveOver"]);

const stopMove = (direction: string) => {
  if (isMove.value) {
    PTZStopContorlsHandeler(direction, props.channelId);
  }
};

const step = ref(1);
</script>

<style lang="less" scoped>
.ptz-container {
  display: flex;
  justify-content: center;
  position: relative;
  width: 100%;
}

.ptz-btns {
  display: flex;
  flex-direction: column;
  gap: 10px;
  left: 0;
  top: 150px;
  width: calc(100% - 150px);
  box-sizing: border-box;
  padding: 15px 10px;
}
.ptz-btn {
  display: flex;
  justify-content: space-between;
  border: 1px solid #eee;
  border-radius: 20px 20px;
  padding: 0px 10px;
  background-color: #f9f9f9;
}
.ptz-title {
  margin-top: 6px;
  font-size: 12px;
  color: #494949;
}
.ptz-expand {
  display: inline-block;
  cursor: pointer;
  transition: color 0.3s;
  font-size: 20px;
  color: #494949;
}
.ptz-expand:hover {
  color: #07397e;
}

/* 按钮方向 */
.controller {
  position: relative;
  width: 150px !important;
  height: 150px !important;
  border: 2px solid #85bef0;
  border-radius: 50%;
  background: #e6f7ff;
  overflow: hidden;
}

/* 用 clip‑path 做扇形 */
.sector {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: #b2daff;
  clip-path: polygon(50% 50%, 97% 0, 0 0);
  transform-origin: 50% 50%;
  transition: background 0.2s;
  cursor: pointer;
}
.sector:nth-of-type(1) {
  transform: rotate(0deg);
} /* 上 */
.sector:nth-of-type(2) {
  transform: rotate(90deg);
} /* 右 */
.sector:nth-of-type(3) {
  transform: rotate(180deg);
} /* 下 */
.sector:nth-of-type(4) {
  transform: rotate(270deg);
} /* 左 */

.sector:hover {
  background: rgba(0, 136, 255, 0.3);
}

/* 中间小圆 */
.center-circle {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 60px;
  height: 60px;
  margin: -30px 0 0 -30px;
  background: white;
  border: 2px solid #85bef0;
  border-radius: 50%;
}

/* 箭头图标 (示例用纯 CSS 画三角) */
.arrow {
  color: #006bc3;
  pointer-events: none;
}
.up {
  position: absolute;
  left: calc(50% - 10px);
  top: 5px;
  font-size: 24px;
}
.down {
  position: absolute;
  left: calc(50% - 10px);
  bottom: 5px;
  font-size: 24px;
}
.left {
  position: absolute;
  left: 10px;
  top: calc(50% - 17px);
  font-size: 24px;
}
.right {
  position: absolute;
  right: 10px;
  top: calc(50% - 17px);
  font-size: 24px;
}
</style>
