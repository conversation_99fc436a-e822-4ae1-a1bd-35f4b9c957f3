<template>
  <div class="control-box" v-if="props.showControl">
    <videoControl
      ref="videoControlRef"
      :channelId="currentChannelId"
      :taskDetail="props.taskInfo"
    ></videoControl>
  </div>
  <div class="canvas-box" id="canvasParent" v-if="props.showCanvas">
    <canvasCluster
      :canvasId="props.canvasClusterId"
      :channelId="currentChannelId"
      ref="canvasClusterRef"
    ></canvasCluster>
  </div>
  <div :id="props.id" class="videoBox"></div>
</template>

<script setup lang="ts">
import { onMounted, ref, onBeforeUnmount, nextTick } from "vue";
import { getChannelVideoLocationDCS } from "@/api/design";
import canvasCluster from "./videoOsdDrawer.vue";
import videoControl from "./videoContorller.vue";
import uEmitter from "@/common/u-event-bus";
import { isEmpty } from "@/common/utils";

const props = defineProps({
  id: {
    type: String,
    default: "videoContainer",
  },
  canvasClusterId: {
    type: String,
    default: "videoCanvas",
  },
  showCanvas: {
    type: Boolean,
    default: true,
  },
  showControl: {
    type: Boolean,
    default: false,
  },
  taskInfo: {
    type: Object,
  },
});

let jessibuca = null; // 视频播放器实例
const showOperateBtns = false; // 是否显示操作按钮

const currentChannelId = ref(""); // 当前视频的channelId

const currentVideoInfo = ref<any>(null); // 当前视频信息

const videoRatio = ref({
  width: 0,
  height: 0,
}); // 视频分辨率

let timer = null;
let lastTime = 0;
// let

function monitorVideoPlayback() {
  let preTime = lastTime;

  timer = setInterval(async () => {
    if (lastTime === 0) return;
    if (lastTime === preTime) {
      console.warn("检测到视频播放时间未更新，可能出现卡顿！");
      const loadingDoc = document.getElementsByClassName("jessibuca-loading")[0] as HTMLDivElement;
      if (loadingDoc) {
        loadingDoc.style.display = "flex";
      }

      playVideo(currentChannelId.value, currentVideoInfo.value);
    } else {
      preTime = lastTime;
    }
  }, 3000);
}
// 创建视频播放器
const createVideo = () => {
  jessibuca = new (window as any).Jessibuca({
    container: document.getElementById(props.id),
    decoder: "/jessibuca/decoder.js",
    videoBuffer: 0.2,
    isResize: true,
    loadingText: "正在加载视频...",
    useMSE: true,
    debug: false,
    isFlv: true,
    showBandwidth: showOperateBtns, // 显示网速
    timeout: 7200,
    loadingTimeoutReplay: 0,
    // heartTimeoutReplayTimes:0,
    operateBtns: {
      fullscreen: showOperateBtns,
      screenshot: showOperateBtns,
      play: showOperateBtns,
      audio: false,
      recorder: false,
    },
    isNotMute: false,
  });
  // 获取视频分辨率
  jessibuca.on("videoInfo", function (audioInfo) {
    console.log("videoInfo", audioInfo);
    videoRatio.value.width = audioInfo.width;
    videoRatio.value.height = audioInfo.height;
    if (props.showCanvas && canvasClusterRef.value) {
      //画布分辨率
      canvasClusterRef.value.changeScale(videoRatio.value.width, videoRatio.value.height);
      // 更新ws连接byChannelId
      canvasClusterRef.value.reconnectWsByChannelId(currentChannelId.value);
    }
  });
  // 断流的时候触发
  // jessibuca.on("error", function (error) {
  //   console.log("error", error);
  //   setTimeout(() => {
  //     if (error === "fetchError") {
  //       playVideo(currentChannelId.value);
  //     }
  //   }, 1000);
  // });

  jessibuca.on("timeout", function (error) {
    console.log("timeout:", error);
    if (error === "loadingTimeout") {
      emit("videoError", "视频加载超时，请检查通道配置或请稍后再试");
    }
  });

  jessibuca.on("timeUpdate", function (ts) {
    // console.log("stats", ts);

    // 视频播放监控检测是否卡顿

    lastTime = ts;
    const loadingDoc = document.getElementsByClassName("jessibuca-loading")[0] as HTMLDivElement;
    if (loadingDoc) {
      loadingDoc.style.display = "none";
    }
  });
};

// 播放视频
const emit = defineEmits(["videoError", "destroyVideoFinish"]); // 获取流失败上报事件
const playVideo = async (id: string, urlData: any) => {
  currentVideoInfo.value = urlData;
  if (!jessibuca) return;
  if (canvasClusterRef.value) {
    canvasClusterRef.value.clearCanvas();
  }
  if (jessibuca.isPlaying()) {
    await jessibuca.pause();
    console.log("暂停播放");
  }
  // 先断开ws
  if (props.showCanvas) {
    canvasClusterRef.value.closeWs();
  }

  // 赋值当前视频的channelId
  currentChannelId.value = id; // 赋值当前视频的channelId
  const loadingDoc = document.getElementsByClassName("jessibuca-loading")[0] as HTMLDivElement;
  if (loadingDoc) {
    loadingDoc.style.display = "flex";
  }

  // 开始播放视频
  await jessibuca.play(window.location.origin + urlData.previewUrl);
};

// 切换视频
// 销毁视频播放器
const destroyVideo = async () => {
  await jessibuca.destroy();
  jessibuca = null;
};

// 导入通道树时触发
uEmitter.on("refreshProjectTree", () => {
  if (isEmpty(currentChannelId.value)) {
    return;
  }
  playVideo(currentChannelId.value, currentVideoInfo.value);
});

const clearCanvas = () => {
  if (canvasClusterRef.value) {
    canvasClusterRef.value.clearCanvas();
  }
};

defineExpose({
  createVideo,
  playVideo,
  destroyVideo,
  clearCanvas,
});

onMounted(() => {
  createVideo();
  monitorVideoPlayback();
});

onBeforeUnmount(() => {
  if (jessibuca) {
    timer && clearInterval(timer);
  }
});

// ---------------------------------------------------osd/canvas------------------------------------

const canvasClusterRef = ref(null);
</script>

<style lang="less" scoped>
.control-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1001;
}
.videoBox {
  position: relative;
  width: 100%;
  height: 100%;
  background-color: #000;
}

.canvas-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}
</style>
