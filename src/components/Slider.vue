<template>
  <div>
    <div class="VueTimeSlot">
      <div class="VueTimeSlotList">
        <div class="VueTimeSlotItems" v-for="(item, index) in times" :key="index">
          <div
            class="VueTimeSlotBox"
            :class="{
              VueTimeSlotBoxSelect: allTimeList.flat().includes(index) || TimeList.includes(index),
              VueTimeSlotBoxItems: getItemsSection(index),
            }"
            @mouseover="ItmeListOnMouseover(index)"
            @click="ItmeListOnclick(index)"
            ref="Refsdom"
            :style="{ width: scaleWidth }"
          ></div>
          <!-- <span style="margin-left: -7px"> {{ index % 2 == 0 ? item : '' }}</span> -->
          <!-- <span v-show="item == 22.5" style="margin-left: 50px"> 23 </span> -->
        </div>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import { JsonParseFun, JsonStrFun } from '@/common/utils';
import {
  defineComponent, ref, reactive, defineExpose, onMounted, onUpdated, watch,
} from 'vue';

export default defineComponent({
  props: {
    scaleWidth: {
      type: String,
      default: '13.72px',
    },
    day: {
      type: Number,
      default: 1,
    },
    getPeriods: {
      type: Array,
      default: () => [],
    },
    activeIndex:{
      type: Number,
      default: 1
    },
  },
  setup(props, { emit }) {
    const times = ref<number[]>([
      0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 5.5, 6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11,
      11.5, 12, 12.5, 13, 13.5, 14, 14.5, 15, 15.5, 16, 16.5, 17, 17.5, 18, 18.5, 19, 19.5, 20,
      20.5, 21, 21.5, 22, 22.5, 23, 23.5,
    ]);
    const Refsdom = ref<HTMLElement[] | any>();
    const TimeList = ref<number[]>([]); // 所选区间
    const TimeItems = ref<number>();
    const TimeItemsx = ref<number>();
    const sections = ref<number[]>([]); // 起始点
    const allTimeList = ref<any[]>([]);

    // 置空
    const recoveryFuc = (id: number) => {
      TimeList.value = [];
      sections.value = [];
      TimeItems.value = undefined;
      TimeItemsx.value = undefined;
      // sections.value.push(times.value[id]);
      // sections.value.push(times.value[id + 1] || 24);
      // TimeList.value.push(id);
      return false;
    };

    // 点击
    const ItmeListOnclick = (id: number) => {
      console.log(TimeList.value, 'aaaa');
      TimeList.value.push(id);
      if (sections.value.length === 0) {
        sections.value.push(times.value[id]);
        sections.value.push(times.value[id + 1] || 24);
      }
      if (TimeList.value.length === 2) {
        // if (TimeList.value[0] > TimeList.value[1]) {
        //   TimeList.value[0] = (TimeList.value[0] as number) + 1;
        //   TimeList.value[1] = (TimeList.value[0] as number) - 1;
        // }
        console.log('TimeList', TimeList.value);
        // allTimeList.value.push(TimeList.value);
        addTimeList(TimeList.value);
        allTimeList.value.forEach((item) => {
          sortArray(item);
        });
        recoveryFuc(id);
        console.log('allTimeList', allTimeList.value);
        let emitTimeList = [];
        emitTimeList = allTimeList.value.map((item) => [
          times.value[item[0]],
          times.value[item[1] + 1] || 24,
        ]);

        emit('getTime', {
          timeQuantum: emitTimeList,
          weekDay: props.day,
        });
      }
    };
    // 添加时间段
    const addTimeList = (timeList: number[]) => {
      // 检查点是否在已有的区间内
      function checkPoint(point) {
        const pointInfo = {
          currentTimeList: null,
          isInPoint: false,
          currentTimeListIndex: null,
        };
        allTimeList.value.forEach((element, i) => {
          if (point > element[0] && point < element[1]) {
            pointInfo.isInPoint = true;
            pointInfo.currentTimeList = element;
            pointInfo.currentTimeListIndex = i;
          }
        });
        return pointInfo;
      }
      // 处理没有点在区间内的情况
      function handleList(arr:number[],currentList:number[]){
         let list =sortArray(arr.concat(currentList));
         const startIndex = list.findIndex((item)=>item === currentList[0]);
         const endIndex = list.findIndex((item)=>item === currentList[1]);
         list = list.filter((item,index)=> !(index>startIndex && index<endIndex));
         let resultArray = [];

         for (let i = 0; i < list.length; i += 2) {
          const rowArray = list.slice(i, i + 2);
          resultArray.push(rowArray);
         }
         return resultArray.filter((element)=> element.length === 2)
        
      }
      const sortTimeList = sortArray(timeList); // 排序
      const flatAllTimeList = JsonParseFun(JsonStrFun(allTimeList.value.flat())); // 扁平化
      if (allTimeList.value.length === 0) {
        allTimeList.value.push(timeList);
        return;
      }
      // 一个点在区间内
      if (checkPoint(sortTimeList[0]).isInPoint && !checkPoint(sortTimeList[1]).isInPoint) {
        console.log('%c single', 'color:blue');
        allTimeList.value[checkPoint(sortTimeList[0]).currentTimeListIndex][1] = sortTimeList[1];
        return;
      }
      // 两个点在区间内
      if (checkPoint(sortTimeList[0]).isInPoint && checkPoint(sortTimeList[1]).isInPoint) {
        console.log('%c double', 'color:red');
        allTimeList.value[checkPoint(sortTimeList[0]).currentTimeListIndex][1] = allTimeList.value[checkPoint(sortTimeList[1]).currentTimeListIndex][1];
        allTimeList.value = allTimeList.value.filter(
          (item, index) => index !== checkPoint(sortTimeList[1]).currentTimeListIndex,
        );
        return;
      }
      // 没有点在区间内
      allTimeList.value = handleList(flatAllTimeList,sortTimeList)
      
    };
    // 排序
    const sortArray = (arr) => {
      arr.sort((a, b) => a - b);
      return arr;
    };

    // 拖动
    const ItmeListOnMouseover = (id: number) => {
      if (TimeList.value.length === 1) {
        TimeItems.value = id;
        sections.value[1] = times.value[id + 1] || 24;
        // console.log(sections.value);
      }
    };

    // 获取时间
    // const getTimeConversion = (time: number) => {
    //   const date = new Date();
    //   const sp = String(time).split('.');
    //   // console.log(sp)
    //   date.setHours(Number(sp[0]));
    //   date.setMinutes(sp[1] === '5' ? 30 : 0);
    //   return date;
    // };

    // 获取样式
    const getItemsSection = (id: number) => {
      let flag = false;
      if (id > TimeList.value[0]) {
        flag = id > TimeList.value[0] && id < (TimeItems.value as any) + 1;
      } else {
        flag = id < TimeList.value[0] && id > (TimeItems.value as any) - 1;
      }

      allTimeList.value.forEach((item, index) => {
        // [[12,24],[11,13]]
        if (item[0] < id && id < item[1]) {
          flag = true;
        }
      });
      return flag;
    };
    const getnumber = () => {
      const temp = allTimeList.value.map((item) => [
        times.value[item[0]],
        times.value[item[1] + 1] || 24,
      ]);
      console.log(temp, 'temp');
    };

    const clearTime = () => {
      TimeList.value = [];
      sections.value = [];
      TimeItems.value = undefined;
      TimeItemsx.value = undefined;
      allTimeList.value = [];
    };

    const changeScale = (currentIndex) => {
      console.log('clear',currentIndex)
      if(currentIndex === props.activeIndex){
        clearTime();
      }
      
    };
    watch(
      () => props.getPeriods,
      (nv) => {
        if (nv) {
          allTimeList.value = [];
          for (let index = 0; index < (props.getPeriods as any[]).length; index++) {
            if ((props.getPeriods[index] as any).week === props.day) {
              const temp = (props.getPeriods[index] as any).timeSlot.map((item) => {
                const temp1 = [];
                times.value.forEach((element, i) => {
                  if (item[0] === element) {
                    temp1[0] = i;
                  }
                  if (item[1] === element) {
                    temp1[1] = i - 1;
                  }
                  if (item[1] === 24) {
                    temp1[1] = 47;
                  }
                });
                return temp1;
              });
              allTimeList.value = temp;
            }
          }
        }
        
      },
      { deep: true },
    );

    return {
      times,
      Refsdom,
      TimeList,
      TimeItems,
      TimeItemsx,
      sections,
      allTimeList,
      ItmeListOnclick,
      ItmeListOnMouseover,
      getItemsSection,
      recoveryFuc,
      getnumber,
      changeScale,
    };
  },
});
</script>

<style lang="scss" scoped>
.VueTimeSlot {
  // width: 1650px;
  margin: 0 auto;
  .VueTimeSlotList {
    display: flex;
    .VueTimeSlotItems {
      display: inline-block;
      padding: 10px 0px;
      color: #507387;
      box-sizing: border-box;
      &:last-child {
        .VueTimeSlotBox {
          border-right: 1px #dfd8e0 solid;
          background-color: #f7f7f7;
        }
      }
      &:first-child {
        .VueTimeSlotBox {
          border-left: 1px #dfd8e0 solid;
          background-color: #f7f7f7;
        }
      }
      .VueTimeSlotBox {
        width: 20px;
        height: 20px;
        border: 1px #dfd8e0 solid;
        border-right: none;
        border-left: none;
        background-color: #f7f7f7;
        cursor: pointer;
      }
      .VueTimeSlotBoxSelect {
        background-color: #4f9bfa !important;
      }
      .VueTimeSlotBoxItems {
        background-color: rgba(50, 150, 250, 0.3);
      }
    }
  }
}
</style>
