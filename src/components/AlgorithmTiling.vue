<template>
  <a-modal
    v-model:open="alTilingModal"
    title="选择算法"
    @ok="alTilingOk"
    @cancel="alTilingCancel"
    :width="800"
    :maskClosable="false"
  >
    <div class="category-container">
      <div v-for="(category, index) in alTilingData" :key="index" class="category-block">
        <h3 class="category-title">{{ category.classification }}</h3>
        <div class="algorithm-list">
          <div
            v-for="(algo, idx) in category.algorithmList"
            :key="idx"
            :class="[
              'algorithm-item',
              selectedAlId === algo.id ? 'selected' : '',
              authAlgorithmCodes.includes(algo.code) ? '' : 'disabled',
            ]"
            @click="selectAlgorithm(algo.id)"
          >
            <!-- <img :src="algo.icon" alt="" class="algorithm-icon" /> -->
            <div v-html="algo.icon" class="algorithm-icon" v-if="algo.icon"></div>
            <div class="algorithm-icon" v-else>
              <img src="../assets/Property 1=通用目标.svg" />
            </div>

            <span class="algorithm-name">{{ algo.name }}</span>
          </div>
        </div>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { message } from "ant-design-vue";
const alTilingModal = ref<boolean>(false);

const alTilingOk = () => {
  if (!selectedAlId.value) {
    message.warning("请选择一个算法");
    return;
  }
  if (addOrEditMode.value === "add") {
    emit("chooseAlgorithm", selectedAlId.value);
  } else {
    emit("editAlgorithm", selectedAlId.value, alItem.value);
  }

  resetTiling();
};

const alTilingCancel = () => {
  resetTiling();
};

const resetTiling = () => {
  selectedAlId.value = "";
  addOrEditMode.value = "";
  alItem.value = null;
  alTilingModal.value = false;
};

// ----------------------------------------------------弹窗算法------------------------------------------
const authAlgorithmCodes = ref<string[]>([]); // 已授权的算法
const alTilingData = ref<any[]>([]);
const addOrEditMode = ref<string>("");
const alItem = ref<any>();

const openAlTiling = (res, currentId = "", mode = "add", currentItem = null) => {
  alTilingModal.value = true;
  alTilingData.value = res;
  authAlgorithmCodes.value = res[0]?.authList;
  selectedAlId.value = currentId;
  addOrEditMode.value = mode;
  alItem.value = currentItem;
};

// 每个分类选中的算法（使用数组，index 与 categories 对应）
const selectedAlId = ref<string>(); // 选中的ID

function selectAlgorithm(id) {
  selectedAlId.value = id;
}

const emit = defineEmits(["chooseAlgorithm", "editAlgorithm"]);

defineExpose({ openAlTiling });
</script>

<style lang="less">
.category-container {
  display: flex;
  flex-wrap: wrap;
  gap: 14px;
}

.category-block {
  width: 700px;
}

.category-title {
  font-size: 18px;
  font-weight: bold;
  margin-bottom: 5px;
}

.algorithm-list {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(80px, 1fr));
  gap: 12px;
}

.algorithm-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  text-align: center;
  cursor: pointer;
  padding: 8px 5px 0px 5px;
  border-radius: 4px;
  transition: all 0.2s;
  border: 1px solid #ccc;
}

.algorithm-item:hover {
  border-color: #409eff;
}

.algorithm-item.selected {
  background-color: #e6f7ff;
  border-color: #1890ff;
}

.algorithm-item.disabled {
  background-color: #f0f0f0;
  color: #999;
  cursor: not-allowed;
  pointer-events: none;
}

.algorithm-icon {
  width: 32px;
  height: 32px;
  object-fit: contain;
  margin-bottom: 10px;
  svg {
    width: 32px;
    height: 32px;
  }
  img {
    width: 32px;
    height: 32px;
  }
}

.algorithm-name {
  font-size: 14px;
}
</style>
