<template>
  <!-- <div v-if="currentTaskId"> -->

  <div class="control-box" v-if="props.showControl">
    <videoControl
      ref="videoControlRef"
      :channelId="currentChannelId"
      :taskDetail="props.taskInfo"
      @full-screen="fullScreen"
      :showBtn="props.showBtn"
      :channel-name="channelName"
      :channelMode="channelMode"
    ></videoControl>
  </div>

  <div class="canvas-box" :id="`${props.canvasClusterId}Parent`">
    <canvasCluster
      :canvasId="props.canvasClusterId"
      :channelId="currentChannelId"
      ref="canvasClusterRef"
    ></canvasCluster>
  </div>
  <div class="video-box">
    <a-spin tip="视频加载中..." :spinning="spinning">
      <template #indicator>
        <LoadingOutlined></LoadingOutlined>
      </template>
      <video
        ref="videoCluster"
        autoplay
        muted
        width="100%"
        height="100%"
        @loadeddata="finishFirstKey"
        style="background-color: black"
      ></video>
    </a-spin>
  </div>

  <!-- </div> -->
  <!-- <a-empty v-else /> -->
</template>

<script setup lang="ts">
import { ref, nextTick, onMounted, onBeforeUnmount } from "vue";
import WebRTCPlayer from "./webRtcPlayer";
import HWebsocket from "@/common/h-websocket";
import canvasCluster from "./videoOsdDrawer.vue";
import videoControl from "./videoContorller.vue";
import { LoadingOutlined } from "@ant-design/icons-vue";

const videoInstance = ref(null);
const videoCluster = ref<any>(null);

const currentTaskDetail = ref<any>({}); // 当前的通道id

// 初始化ws
const currentChannelId = ref("");

const canvasClusterRef = ref(null);

const channelName = ref("");

const channelMode = ref("AUTOMATIC");

const props = defineProps({
  showControl: {
    type: Boolean,
    default: false,
  },
  showBtn: {
    type: Boolean,
    default: false,
  },
  canvasClusterId: {
    type: String,
    default: "videoCanvas",
  },
  taskInfo: {
    type: Object,
    default: {},
  },
});
let timer = null;
let lastTime = 0;
let currentTime = 0;

// 视频播放监控检测是否卡顿
function monitorVideoPlayback(videoElement) {
  if (timer) {
    timer && clearInterval(timer);
  }
  timer = setInterval(async () => {
    // console.log("monitorVideoPlayback", videoElement.currentTime, lastTime);
    currentTime = videoElement.currentTime !== 0 ? videoElement.currentTime : 0.1;
    if (currentTime === 0) return;
    if (currentTime === lastTime) {
      console.warn("检测到视频播放时间未更新，可能出现卡顿！");
      spinning.value = true;
      if (videoInstance.value) {
        videoInstance.value.reStream(currentChannelId.value);
        // videoInstance.value.start();
      }
    } else {
      lastTime = currentTime;
      // spinning.value = false;
    }
  }, 5000);
}

// 视频loading
const spinning = ref<boolean>(false);
const finishFirstKey = () => {
  if (videoCluster.value.srcObject) {
    const tracks = videoCluster.value.srcObject.getTracks();
    console.log(tracks, "tracks================>");
    tracks.forEach((track) => {
      if (track.kind === "video") {
        const { width, height } = track.getSettings();
        console.log(width, height, "tracks");
        canvasClusterRef.value.changeScale(width, height);
        // 更新ws连接byChannelId
        canvasClusterRef.value.reconnectWsByChannelId(currentChannelId.value);
      }
    });
    spinning.value = false;
  }
};

/**
 * @param channelIdList 当前任务配置的通道id列表
 * @param taskId 任务id
 * @param taskDetail 第一组数据
 */

// 调度任务视频
const setVideoClusterForTask = async (videoInfoList, channelIdList: string[], ChannelId) => {
  nextTick(async () => {
    // 初始化画布
    spinning.value = true; // 开启视频loading
    currentChannelId.value = ChannelId;
    if (videoInfoList.length !== 0) {
      const url = videoInfoList[0].previewUrl;
      const parsedUrl = new URL(url);
      const ipFromUrl = parsedUrl.hostname; // ***************
      const portFromUrl = parsedUrl.port;
      const fullUrl = `http://${ipFromUrl}:${portFromUrl}/`;
      // 创建video实例
      videoInstance.value = new WebRTCPlayer(
        fullUrl,
        videoInfoList,
        channelIdList, // 通道id列表
        videoCluster.value, // videoRef
        currentChannelId.value // 当前通道id
      );
    }

    // 视频loading
  });
};

// 通道树视频
const setVideoClusterForChannelTree = (videoInfoList, channelId: string) => {
  nextTick(async () => {
    currentChannelId.value = channelId;
    // 更新画布
    spinning.value = true;

    if (videoInfoList.length !== 0) {
      const url = videoInfoList[0].previewUrl;
      channelName.value = videoInfoList[0].channelName;
      channelMode.value = videoInfoList[0].channelMode;
      // const parsedUrl = new URL(url);
      // const ipFromUrl = parsedUrl.hostname; // ***************
      // const portFromUrl = parsedUrl.port;
      // const fullUrl = `http://${ipFromUrl}:${portFromUrl}/`;
      const fullUrl = `${window.origin}${url}`;

      // 创建video实例
      videoInstance.value = new WebRTCPlayer(
        fullUrl,
        videoInfoList,
        [channelId],
        videoCluster.value,
        currentChannelId.value
      );
    }
  });
};

// 手动切换视频通道
const handleSwitchChannel = async (channelId) => {
  console.log("handleSwitchChannel", videoInstance.value);
  if (!videoInstance.value) return;
  currentTime = 0;
  lastTime = 0;
  videoCluster.value.currentTime = 0;
  currentChannelId.value = channelId;
  videoInstance.value.changeVideo(channelId);
};

// 关闭播放器
const closePlayer = () => {
  if (videoInstance.value) {
    videoInstance.value.closePlayer();
    videoInstance.value = null;
    currentChannelId.value = "";
    currentTaskDetail.value = {};
    canvasClusterRef.value.closeWs();
  }
};

const clearCanvas = () => {
  canvasClusterRef.value.clearCanvas();
};

const fullScreen = () => {
  console.log("fullScreen");
  const tracks = videoCluster.value.srcObject.getTracks();
  tracks.forEach((track) => {
    if (track.kind === "video") {
      const { width, height } = track.getSettings();
      console.log("width", width);
      canvasClusterRef.value.changeScale(width, height);
    }
  });
};

// 更新画布比例
window.addEventListener("resize", () => {
  if (videoInstance.value) {
    const tracks = videoCluster.value.srcObject.getTracks();
    tracks.forEach((track) => {
      if (track.kind === "video") {
        const { width, height } = track.getSettings();
        canvasClusterRef.value.changeScale(width, height);
      }
    });
  }
});
defineExpose({
  setVideoClusterForTask,
  setVideoClusterForChannelTree,
  handleSwitchChannel,
  closePlayer,
  clearCanvas,
  fullScreen,
});

const emit = defineEmits(["changeStep", "channnelError"]);

onMounted(() => {
  monitorVideoPlayback(videoCluster.value); // 监听视频播放时间
});

onBeforeUnmount(() => {
  timer && clearInterval(timer);
  videoInstance.value && videoInstance.value.closePlayer();
});
</script>

<style lang="less" scoped>
/** 控制区域 */
.control-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1001;
}
/** 画布区域 */
.canvas-box {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  z-index: 1000;
}

/** 视频播放区域 */
.video-box {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
  border-radius: 2px;
}
:deep(.ant-spin-nested-loading) {
  height: 100%;
}
:deep(.ant-spin-container) {
  height: 100%;
}
</style>
