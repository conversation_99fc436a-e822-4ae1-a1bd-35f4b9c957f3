import { getChannelVideoLocationDCS } from "@/api/design";

// 发送http请求
async function HttpPost(url, data) {
  const answer = await fetch(url, {
    method: "POST",
    headers: { "Content-Type": "application/sdp" },
    body: data.sdp,
  }).then((res) => {
    switch (res.status) {
      case 201:
        break;
      case 404:
        return res.json().then((e) => {
          throw new Error("404");
        });
      // HttpPost(url, data);
      // throw new Error("stream not found");

      case 400:
        return res.json().then((e) => {
          throw new Error(e.error);
        });
      default:
        throw new Error(`bad status code ${res.status}`);
    }

    return res.text();
  });
  return answer;
}

// 重复请求流地址
async function postWithRetry(
  url: string,
  data: any,
  maxRetries = 5,
  retryDelay = 200
): Promise<any> {
  let attempt = 0;

  while (attempt < maxRetries) {
    try {
      const response = await HttpPost(url, data);
    
      // 如果返回非404，表示成功
      // if (typeof response === "string" && response.includes("404")) {
      //   throw { status: 404 }; // 模拟统一错误处理
      // }

      return response; // 成功返回
    } catch (err: any) {
      attempt++;

      if (err?.status === 404 || err?.message.includes("404")) {
        console.log(
          `%c第 ${attempt} 次请求 404，准备重试...`,
          "background: blue; color: white; padding: 2px 4px; border-radius: 4px"
        );
      } else {
        console.error("非 404 错误，中止重试：", err);
        throw err; // 其他错误直接抛出
      }

      if (attempt >= maxRetries) {
        throw new Error(`请求失败：超过最大重试次数 ${maxRetries}`);
      }

      await new Promise((resolve) => setTimeout(resolve, retryDelay));
    }
  }

  throw new Error("未知错误");
}

class WebRTCPlayer {
  channelIdList: string[]; // 当前任务的通道列表
  channelUrlList: any[]; // 当前任务的播放地址
  channelInfoList: any[]; // 通道信息
  pcList: any[]; // 连接列表
  video: HTMLVideoElement; // 视频播放器
  MediaStreamList: any[]; // 媒体流列表
  streamUrl: any[]; // 流地址
  url: string; // 视频地址
  constructor(
    url: string,
    videoInfoList: any[],
    idList: string[],
    video: HTMLVideoElement,
    currentChannelId: string = ""
  ) {
    if (typeof video === "undefined") {
      console.error("video is null");
      return;
    }
    // if(this.pcList.length > 0){
    //   this.closePlayer();
    // }
    this.url = url;
    this.channelIdList = idList;
    this.video = video;

    this.pcList = [];
    this.MediaStreamList = [];

    // this.pcList = [];
    // 先关闭之前的播放器
    // 获取流地址
    // this.getStreamUrl(idList).then((res) => {
    //   this.channelInfoList = res;
    //   this.channelUrlList = res.map((item) => {
    //     return {
    //       previewUrl: item.previewUrl,
    //       channelId: item.channelId,
    //     };
    //   });
    //   this.initPlayer(currentChannelId);
    // });
    this.channelUrlList = videoInfoList.map((item) => {
      return {
        previewUrl: item.previewUrl,
        channelId: item.channelId,
      };
    });
    this.initPlayer(currentChannelId);
  }
  // ===========================================初始化播放器===========================================

  async initPlayer(currentChannelId: string) {
    const { channelUrlList, video, MediaStreamList } = this;
    // const handledStreams = new Set();
    channelUrlList.forEach(async (url) => {
      const handledStreams = new Set();
      const pc = new RTCPeerConnection();
      pc.addTransceiver("video", { direction: "recvonly" });
      pc.addTransceiver("audio", { direction: "recvonly" });
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);
      // const data = {
      //   // api: `${replacePortInUrl((window as any).g.api, port)}${api}`,
      //   api: `${(window as any).g.videoUrl.replace("https", "http")}`,
      //   clientip: null,
      //   sdp: offer.sdp,
      //   streamurl: window.location.origin + url.previewUrl,
      // };
      
      const answerData = await postWithRetry(`${this.url}/whep`, offer);  
      pc.setRemoteDescription(new RTCSessionDescription({ type: "answer", sdp: answerData }));
     
      pc.ontrack = (event) => {
        const stream = event.streams[0];
        console.log("获取流", stream);
        if (!handledStreams.has(stream.id)) {
          // 添加唯一性检查

          MediaStreamList.push({ stream: stream, channelId: url.channelId });
          console.log("添加流", MediaStreamList);

          const currentStream = MediaStreamList.find((item) => item.channelId === currentChannelId);
          if (currentStream) {
            video.srcObject = currentStream.stream;
          }

          this.pcList.push({ pc: pc, channelId: currentChannelId });

          handledStreams.add(stream.id); // 记录已处理流
          console.log("已处理流", handledStreams);
        }
      };
    });
  }

  // ===========================================重新推流===========================================
  public async reStream(channelId: string) {
    try {
      const response = await getChannelVideoLocationDCS(channelId);
      console.log("重新推流", this.MediaStreamList);
      const nowPc = this.pcList.find((item) => item.channelId === channelId);
      if (nowPc) {
        nowPc.pc.close();
        this.pcList = this.pcList.filter((item) => item.channelId !== channelId);
        this.MediaStreamList = this.MediaStreamList.filter((item) => item.channelId !== channelId);
      }
      const pc = new RTCPeerConnection();
      pc.addTransceiver("video", { direction: "recvonly" });
      pc.addTransceiver("audio", { direction: "recvonly" });
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);
      const answerData = await postWithRetry(`${response.data.previewUrl}/whep`, offer);
      pc.setRemoteDescription(new RTCSessionDescription({ type: "answer", sdp: answerData }));
      pc.ontrack = (event) => {
        const stream = event.streams[0];
        console.log("获取流", stream);
        this.MediaStreamList.push({ stream: stream, channelId: channelId });
        const currentStream = this.MediaStreamList.find((item) => item.channelId === channelId);
        if (currentStream) {
          currentStream.stream = stream;
          this.video.srcObject = currentStream.stream;
        }
        this.pcList.push({ pc: pc, channelId: channelId });
      };
      // const previewUrl = response.data.previewUrl;
    } catch (e) {
      console.error(e);
    }
  }
  // 视频分组切换
  public changeGroup(videoInfoList, currentChannelId: string) {
    if (this.pcList.length) {
      this.closePlayer();
    }
    this.channelUrlList = videoInfoList.map((item) => {
      return {
        previewUrl: item.previewUrl,
        channelId: item.channelId,
      };
    });
    this.initPlayer(currentChannelId);
  }
  // ============================================切换视频==========================================
  public changeVideo(currentChannelId: string) {
    if (this.MediaStreamList.length === 0) {
      return;
    }
    console.log("切换视频2", this.MediaStreamList, currentChannelId);
    const currentStream = this.MediaStreamList.find(
      (stream) => stream.channelId === currentChannelId
    );
    if (!currentStream) {
      console.error("当前流不存在");
      this.video.srcObject = null;
      this.reStream(currentChannelId);
      return;
    }
    this.video.srcObject = currentStream.stream;
  }

  // ===========================================获取流地址==========================================

  // ==========================================关闭播放器=============================================
  public closePlayer() {
    console.log(
      "[Webrtc] 断开视频流成功—————————+++++++++++++++++++++++++++++++++++———————————，",
      this.pcList
    );
    if (!this.pcList) return;
    this.pcList.forEach((pc) => {
      if (pc) {
        if (pc.pc) {
          pc.pc.close();
          console.log(
            "[Webrtc] 断开视频流成功————————————————————————————————————————————————————"
          );
        }
      }
    });
    this.pcList = [];
    this.MediaStreamList = [];
    this.video.srcObject = null;
  }
}

export default WebRTCPlayer;
