<template>
  <canvas :id="canvasId" style="width: 100%; height: 100%"></canvas>
</template>

<script lang="ts">
import { defineComponent, nextTick, onMounted, onUnmounted, watch, onBeforeUnmount } from "vue";
import { fabric } from "fabric";
import HWebsocket from "@/common/h-websocket";
import drawGraph from "@/hooks/darwElement";
import uEmitter from "@/common/u-event-bus";

export default defineComponent({
  props: {
    channelId: {
      type: String,
      default: "",
    },
    canvasId: {
      type: String,
      default: "videoCanvas",
    },
  },
  setup(props) {
    let canvas = null;

    // =============================================初始化画布==========================================
    function initCanvas() {
      // 创建画布
      console.log("初始化画布+++++++++++++++++");
      canvas = new fabric.Canvas(props.canvasId, {
        selection: false,
      });
      // changeScale(originalWidth, originalHeight);
    }

    const changeScale = (originalWidth, originalHeight) => {
      const container = document.getElementById(`${props.canvasId}Parent`);
      // 容器实际尺寸
      const containerWidth = container.clientWidth;
      const containerHeight = container.clientHeight;
      console.log(containerWidth, containerHeight, "containerWidth, containerHeight");

      // 计算缩放比例
      const scaleX = containerWidth / originalWidth;
      const scaleY = containerHeight / originalHeight;
      const scale = Math.min(scaleX, scaleY);

      // 设置画布像素尺寸
      canvas.setDimensions({
        width: containerWidth,
        height: containerHeight,
      });

      // // 设置缩放与视口
      canvas.setZoom(scale);
      canvas.setViewportTransform([
        scale,
        0,
        0,
        scale,
        (containerWidth - originalWidth * scale) / 2, // 水平居中
        (containerHeight - originalHeight * scale) / 2, // 垂直居中
      ]);

      canvas.renderAll();
    };

    const reconnectWsByChannelId = (channelId) => {
      console.log(channelId, "channelId");
      if (channelId) {
        reconnectWs(channelId);
      }
    };
    // 绘制图形
    const { draw } = drawGraph();
    // 初始化ws
    let ws = null;
    function openWs(id) {
      const container = document.getElementById(props.canvasId);
      console.log(
        container.clientWidth,
        container.clientHeight,
        "container.clientWidth, container.clientHeight"
      );
      // drawGraph(fakedata);
      ws = new HWebsocket(`${(window as any).g.wsApi}/websocket/osd/${id}`);
      ws.onMessage = (evt) => {
        const wsData = JSON.parse(evt.data);
        if (wsData.eventType === "draw") {
          draw(canvas, 1, 1, container.clientWidth, wsData.osdItemList);
        }
        if (wsData.eventType === "clear") {
          if (canvas) {
            canvas.clear();
          }
        }
        if (wsData.eventType === "switchChannelMode") {
          uEmitter.emit("MesChannelMode", "AUTOMATIC");
        }
        if (wsData.eventType === "switch_mode") {
          uEmitter.emit("MesChannelMode", wsData.channelMode);
        }
      };
    }

    // 重新连接osd的websocket
    function reconnectWs(channelId) {
      let timer = null;
      if (ws === null) {
        openWs(channelId);
      } else {
        closeWs();
        if (canvas) {
          canvas.clear();
          canvas.renderAll();
        }

        timer = setTimeout(() => {
          openWs(channelId);
        }, 0);
      }
    }
    function closeWs() {
      if (ws) {
        console.log("关闭ws");
        ws.close();
        ws = null;
      }
    }
    function disposeCanvas() {
      if (canvas) {
        canvas.clear();
        canvas.dispose();
        canvas = null;
      }
    }
    function clearCanvas() {
      if (canvas) {
        canvas.clear();
        canvas.renderAll();
      }
    }
    onMounted(() => {
      initCanvas();
      // openWs();
    });
    onBeforeUnmount(() => {
      if (canvas) {
        closeWs();
        disposeCanvas();
        uEmitter.off("MesChannelMode");
        console.log(`%cid为videoCanvas的canvas已销毁`, "color:blue");
      }
    });

    return {
      reconnectWsByChannelId,
      initCanvas,
      changeScale,
      clearCanvas,
      closeWs,
    };
  },
});
</script>

<style scoped>
#videoCanvas {
  width: 100% !important;
  height: 100% !important;
}
</style>
