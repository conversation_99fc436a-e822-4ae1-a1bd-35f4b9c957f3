<template>
  <div class="videoUI">
    <div class="videoTitle">
      <div class="videoTitleText">
        {{ props.taskDetail.label ? props.taskDetail.label : props.channelName }}
      </div>
      <div class="videoTitleTime">{{ time }}</div>
    </div>
    <!-- <div class="videoPosition">
      <img src="../../assets/positionIcon.png" alt="" />
      <div class="videoPositionText">{{ props.taskDetail.label }}</div>
    </div> -->

    <div class="carousel-controls" v-if="!props.showBtn">
      <button
        class="control-btn prev-btn"
        aria-label="上一张"
        @click="changeVideo(props.taskDetail.id, -1)"
      >
        <span class="arrow"></span>
      </button>
      <button
        class="control-btn next-btn"
        aria-label="下一张"
        @click="changeVideo(props.taskDetail.id, 1)"
      >
        <span class="arrow"></span>
      </button>
    </div>

    <div class="videoControls">
      <a-tooltip placement="top" title="PTZ控制">
        <CIconFont
          type="icon-shexiangtoukongzhi"
          class="controlcon hoverStyle"
          :class="{ 'ptz-control-active': openPTZControl }"
          @click="ptzControl"
          v-if="(autoOrmulti === 'MANUAL' || props.showBtn) && channelAutoOrmulti === 'MANUAL'"
        ></CIconFont>
      </a-tooltip>
      <a-tooltip placement="top" title="全屏">
        <CIconFont
          type="icon-quanping"
          class="controlcon hoverStyle"
          @click="fullScreen"
          v-if="autoOrmulti === 'MANUAL' || props.showBtn"
        ></CIconFont>
      </a-tooltip>

      <a-radio-group
        v-model:value="channelAutoOrmulti"
        name="radioGroup"
        @change="changeAutoOrmulti"
      >
        <a-radio value="AUTOMATIC">自动</a-radio>
        <a-radio value="MANUAL">手动</a-radio>
      </a-radio-group>
    </div>
    <!-- ---------------------------------------ptz控件-------------------------------- -->
    <div class="ptz-container">
      <ptzControlTools v-show="openPTZControl" :channel-id="channelId"></ptzControlTools>
    </div>
  </div>
</template>

<script setup lang="ts">
// import { pauseMesTask, resumeMesTask } from "@/api/operatorStation";
import uEmitter from "@/common/u-event-bus";
import { inject, nextTick, onBeforeUnmount, ref, watch } from "vue";
import ptzControlTools from "@/components/ptzControlTools.vue";
import { changeAutoOrManual } from "@/api/operatorStation";
import { message } from "ant-design-vue";

const props = defineProps({
  channelId: {
    type: String,
  },
  presetId: {
    type: String,
  },
  showBtn: {
    type: Boolean,
    default: false,
  },

  taskDetail: {
    type: Object,
    default: () => ({
      label: "任务名称",
    }),
  },

  channelName: {
    type: String,
    default: "",
  },

  channelMode: {
    type: String,
    default: "AUTOMATIC",
  },
});

const emit = defineEmits(["fullScreen"]);

const autoOrmulti = inject("autoOrmulti");

// ----------------------------------------------------------------------视频标题--------------------------------------------------------

const time = ref("dsaasd");

function updateClock() {
  const now = new Date();

  // 格式化时间
  const hours = now.getHours().toString().padStart(2, "0");
  const minutes = now.getMinutes().toString().padStart(2, "0");
  const seconds = now.getSeconds().toString().padStart(2, "0");

  // 更新日期
  const year = now.getFullYear();
  const month = (now.getMonth() + 1).toString().padStart(2, "0");
  const date = now.getDate().toString().padStart(2, "0");

  time.value = `${year}-${month}-${date}` + ` ${hours}:${minutes}:${seconds}`;
}

// 立即执行一次初始化
updateClock();

setInterval(updateClock, 1000);

// ----------------------------------------------------------摄像头控制------------------------------------------------------

const openPTZControl = ref(false);
const ptzControl = () => {
  if (!props.channelId) return;
  openPTZControl.value = !openPTZControl.value;
};

// ----------------------------------------------------------左右控制--------------------------------------------------------

const changeVideo = (id: string, step: number) => {
  uEmitter.emit("changeVideoByNext", id, step);
};

// ----------------------------------------------------------全屏控制-----------------------------------------------

const fullScreen = () => {
  // uEmitter.emit("fullScreen",props.channelId);
  const video = document.getElementById("showVideoBoxBox");
  if (!document.fullscreenElement) {
    video
      .requestFullscreen()
      .then(() => {
        setTimeout(() => {
          emit("fullScreen");
        }, 200);
      })

      .catch((err) => {
        alert(`尝试启用全屏模式时出错：${err.message}（${err.name}）`);
      });
  } else {
    document.exitFullscreen();
  }
};

// --------------------------------------------------------通道的手动/自动-------------------------------------------------
const channelAutoOrmulti = ref<string>(props.channelMode);

const changeAutoOrmulti = async (val) => {
  console.log(channelAutoOrmulti.value);
  const data = {
    mode: channelAutoOrmulti.value,
    channelId: props.channelId,
  };
  if(channelAutoOrmulti.value === 'AUTOMATIC'){
    openPTZControl.value = false;
  }
  const res = await changeAutoOrManual(data);
  if (res.code === 0) {
    message.success("操作成功");
  }
};

uEmitter.on("MesChannelMode", (value: string) => {
  channelAutoOrmulti.value = value;
});
</script>

<style scoped lang="less">
.videoUI {
  width: 100%;
  height: 100%;
  position: relative;
}
.videoUI:hover .videoControls {
  display: flex;
}
.videoUI:hover .videoTitle {
  display: flex;
}

.videoUI:hover .videoPosition {
  display: flex;
}
.videoUI:hover .prev-btn {
  display: flex;
}

.videoUI:hover .next-btn {
  display: flex;
}

.videoTitle {
  position: absolute;
  background-color: #001a5d99;
  box-sizing: border-box;
  left: 0;
  right: 0;
  height: 28px;
  line-height: 28px;
  font-size: 16px;
  color: #fff;
  padding: 0px 17px;

  display: none;
  .videoTitleTime {
    margin-left: auto;
  }
}

.videoPosition {
  position: absolute;
  background-color: #001a5d7f;
  width: 280px;
  height: 48px;
  border-radius: 50px;
  border: 1px solid #fff;
  left: 50%; /* 父容器宽度50%位置 */
  top: 50%; /* 父容器高度50%位置 */
  transform: translate(-50%, -50%); /* 自身宽高反向偏移50% */
  display: none;
  padding: 3px 25px;
  cursor: pointer;
  .videoPositionText {
    font-size: 16px;
    color: #fff;
    padding-left: 5px; /* 留出图标空间 */
    padding-top: 7px;
    text-align: left;
  }
}
.control-btn {
  pointer-events: all; /* 恢复按钮点击 */
  width: 48px;
  height: 48px;
  border: none;
  border-radius: 50%;
  background: #001a5d7f;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  cursor: pointer;
  transition: all 0.3s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.control-btn:hover {
  background: #07397f;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
  transform: scale(1.05);
}

.arrow {
  display: block;
  width: 16px;
  height: 16px;
  border: 2px solid #fff;
  position: relative;
}

/* 左侧箭头 */
.prev-btn .arrow {
  border-right: 0;
  border-bottom: 0;
  transform: rotate(-45deg);
  margin-left: 4px;
}

/* 右侧箭头 */
.next-btn .arrow {
  border-left: 0;
  border-top: 0;
  transform: rotate(-45deg);
  margin-right: 4px;
}

/* 按钮位置调整 */
.prev-btn {
  top: 50%; /* 父容器宽度50%位置 */
  left: 20px;
  position: absolute;
  display: none;
}

.next-btn {
  top: 50%; /* 父容器宽度50%位置 */
  right: 20px;
  position: absolute;
  display: none;
}

.videoControls {
  background-color: #001a5d99;
  box-sizing: border-box;
  display: none;
  flex-direction: row-reverse;
  justify-items: center;
  gap: 10px;
  position: absolute;
  left: 0;
  right: 0;
  bottom: 0;
  height: 28px;
  line-height: 28px;
  width: 100%;
  padding-left: 13px;
  padding-right: 13px;
  color: #a8aca2;
  visibility: visible;
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
}
.controlcon {
  font-size: 26px;
  color: #a8aca2;
  align-self: center;
  cursor: pointer;
}

.autoControl {
  align-self: center;
}
.auto-menu {
  display: flex;
  position: relative;
}
.auto-menu-text {
  font-size: 14px;
  min-width: 30px;
  height: 20px;
  line-height: 20px;
  cursor: pointer;
  text-align: center;
}
#auto-menu-list {
  position: absolute;
  left: 50%;
  bottom: 100%;
  visibility: hidden;
  opacity: 0;
  transform: translateX(-50%);
  transition: visibility 0.3s, opacity 0.3s;
  background-color: #001a5d99;
  border-radius: 4px;
  overflow: hidden;
}

.auto-menu-item {
  display: block;
  height: 25px;
  line-height: 25px;
  margin: 0;
  padding: 0 10px;
  cursor: pointer;
  font-size: 14px;
  text-align: center;
  width: 50px;
  color: hsla(0, 0%, 100%, 0.5);
  transition: color 0.3s, background-color 0.3s;
}
.auto-menu-item-active {
  color: #2298fc;
}

.ptz-control-active {
  color: #2298fc;
}

.ptz-container {
  position: absolute;
  right: 10px;
  bottom: 20px;
  width: 300px;
  height: 155px;
}

:deep(.ant-radio-wrapper) {
  line-height: 28px !important;
  color: #fff !important;
}
</style>
