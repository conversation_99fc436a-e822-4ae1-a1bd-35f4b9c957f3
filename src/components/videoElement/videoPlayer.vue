<template>
  <videoWindow
    ref="videoWindowRef"
    :show-control="props.showControl"
    :showBtn="props.showBtn"
    v-if="currentVideoProtocolType === 'RTSP'"
    :task-info="props.taskInfo"
    :canvas-cluster-id="props.canvasClusterId"
  ></videoWindow>
  <videoJessibuca
    ref="videoJessibucaRef"
    @video-error="videoError"
    :show-control="props.showControl"
    :task-info="props.taskInfo"
    v-else
  ></videoJessibuca>
</template>

<script setup lang="ts">
import videoWindow from "./videoWindow.vue";
import videoJessibuca from "../videoJessibuca/videoJessibuca.vue";
import { onBeforeUnmount, ref, provide } from "vue";
import { nextTick } from "process";

const props = defineProps({
  showControl: {
    type: Boolean,
    default: false,
  },
  taskInfo: {
    type: Object,
  },
  taskMode: {
    type: Boolean,
    default: false,
  },
  canvasClusterId: {
    type: String,
    default: "videoCanvas",
  },
  showBtn: {
    type: Boolean,
    default: false,
  },
  channelName:{
    type:String,
    default:''
  }
});

const videoWindowRef = ref(null);
const videoJessibucaRef = ref(null);

const currentVideoProtocolType = ref("RTSP"); // RTSP or GB28281
const currentChannelId = ref("");

const videoInfoList = ref([]);

const playVideo = async (videoInfo, currentId) => {
  if(!videoInfo) return;
  videoInfoList.value = videoInfo;
  currentChannelId.value = currentId;
  const rtspVideoList = videoInfoList.value.map((item) => {
    if (item.videoProtocolType === "RTSP") {
      return item;
    }
  });
  const channelIdList = rtspVideoList.map((item, index) => {
    return item.channelId;
  });
  currentVideoProtocolType.value = videoInfoList.value.find(
    (item) => item.channelId === currentId
  ).videoProtocolType;

  nextTick(() => {
    if (currentVideoProtocolType.value === "RTSP") {
      if (props.taskMode) {
        videoWindowRef.value.setVideoClusterForTask(videoInfoList.value, channelIdList, currentId);
      } else {
        videoWindowRef.value.setVideoClusterForChannelTree(videoInfoList.value, currentId);
      }
    } else {
      videoJessibucaRef.value.playVideo(videoInfo.channelId, videoInfo);
    }
  });
};

const destroyVideo = async () => {
  if (currentVideoProtocolType.value === "RTSP") {
     console.log("另一个关闭");
    videoWindowRef.value.closePlayer();
  } else {
    videoJessibucaRef.value.destroyVideo();
  }
};

const clearCanvas = () => {
  if (currentVideoProtocolType.value === "RTSP") {
    videoWindowRef.value.clearCanvas();
  } else {
    videoJessibucaRef.value.clearCanvas();
  }
};

const changeVideo = (channelId: string) => {
  if (currentChannelId.value === channelId) return;
  if (videoInfoList.value.length === 0) return;
  const videoInfo = videoInfoList.value.find((item) => item.channelId === channelId);
  currentChannelId.value = channelId;
  currentVideoProtocolType.value = videoInfo.videoProtocolType;
  if (videoInfo.videoProtocolType === "RTSP") {
    videoWindowRef.value.handleSwitchChannel(channelId);
  } else {
    videoJessibucaRef.value.playVideo(channelId, videoInfo);
  }
};
// const restartVideo = () => {
//   if (videoProtocolType.value === "RTSP") {

//   }
// };

const videoError = (error) => {
  emit("videoError", error);
  destroyVideo();
};

onBeforeUnmount(() => {
  destroyVideo();
});

const emit = defineEmits(["videoError"]);
defineExpose({
  playVideo,
  destroyVideo,
  clearCanvas,
  changeVideo,
});
</script>

<style lang="less"></style>
