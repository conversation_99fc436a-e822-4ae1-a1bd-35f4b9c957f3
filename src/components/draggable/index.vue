<template>
  <teleport :to="to" v-if="showTele">
    <div class="leftTip" v-if="leftShow" :style="{ width: offset + 'px' }"></div>
    <Vue3DraggableResizable
      :initW="w"
      :initH="h"
      :x="x"
      :y="y"
      :w="w"
      :h="h"
      v-show="show"
      v-model:active="active"
      :draggable="draggable"
      :resizable="resizable"
      :parent="false"
      :disabledH="disabledH"
      @activated="print('activated')"
      @deactivated="print('deactivated')"
      @drag-start="dragStrat(dragName)"
      @resize-start="resizeStart"
      @dragging="drag"
      @resizing="resizing"
      @drag-end="dragEnd"
      @resize-end="resizeEnd"
    >
      <div style="width: 100%; height: 100%; overflow: hidden">
        <slot></slot>
      </div>
    </Vue3DraggableResizable>
    <div class="rightTip" v-if="false" :style="{ width: offset + 'px' }"></div>
  </teleport>
</template>

<script>
import {
  defineComponent, setup, ref, onMounted, computed,
} from 'vue';
import { useStore } from 'vuex';
import Vue3DraggableResizable from 'vue3-draggable-resizable';
import 'vue3-draggable-resizable/dist/Vue3DraggableResizable.css';

export default defineComponent({
  components: { Vue3DraggableResizable },
  props: {
    to: {
      default: 'body',
    },
    resizable: {
      default: true,
    },
    draggable: {
      default: true,
    },
    offset: {
      default: 50,
    },
    x: {
      default: 100,
    },
    y: {
      default: 100,
    },
    h: {
      default: 100,
    },
    w: {
      default: 100,
    },
    dragName: {
      default: '',
    },
    show: {
      default: true,
    },
    disabledH: {
      default: false,
    },
  },
  setup(props, ctx) {
    const active = ref(false);
    const showTele = ref(false);
    const leftShow = ref(false);
    const rightShow = ref(false);
    const print = (val) => {
      // console.log(val)
    };

    const resizing = (data) => {
      ctx.emit('resizing', data);
    };
    const resizeStart = () => {
      ctx.emit('resizeStart', props.dragName);
    };
    const resizeEnd = (data) => {
      ctx.emit('resizeEnd', data);
    };
    const dragStrat = (data) => {
      ctx.emit('dragStrat', data);
    };
    const dragEnd = (data) => {
      ctx.emit('dragEnd', data);
    };
    const drag = (data) => {
      ctx.emit('drag', data);
    };
    onMounted(() => {
      showTele.value = true;
    });
    return {
      active,
      showTele,
      leftShow,
      rightShow,
      print,
      drag,
      dragStrat,
      dragEnd,
      resizing,
      resizeStart,
      resizeEnd,
    };
  },
});
</script>
<style scoped>
.leftTip {
  height: 100%;
  background-image: linear-gradient(to right, rgba(255, 255, 0, 0.1), rgba(255, 255, 0, 0.4));
}
.rightTip {
  height: 100%;
  background-image: linear-gradient(to right, rgba(255, 255, 0, 0.4), rgba(255, 255, 0, 0.1));
  position: absolute;
  right: 0;
}
</style>
