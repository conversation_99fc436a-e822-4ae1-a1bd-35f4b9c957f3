/* 登录页面背景 */
.loginBack {
  position: relative;
  width: 100%;
  height: 100%;

  min-width: 600px;
  min-height: 650px;
  background: url("../../assets/loginbg.png");
  background-size: cover;
  background-repeat: no-repeat;

  .right-container-bg {
    height: 100%;
    width: 668px;
    position: absolute;
    flex-shrink: 0;
    top: 0;
    right: 0;
    background: rgba(255, 255, 255, 0.6);
    backdrop-filter: blur(28.2px);
    .right-container {
      padding: 80px 70px;
      display: flex;
      flex-direction: column;
      .sloganBox {
        .slogan {
          font-size: @font-size-base;
          margin-top: 27px;
          line-height: 1.6;
          color: var(--login-text-color);
        }
      }
      .loginFormBox {
        margin-top: 100px;
        display: flex;
        flex-direction: column;
        width: 100%;
        gap: 20px;
        .title {
          font-size: @font-size-base;
          color: var(--login-text-color);
          margin-bottom: 8px;
        }
        .userInput {
          width: 100%;
          height: 40px;
          border: none;
          font-size: 18px;
        }
        .fit-picture {
          width: 130px;
          height: 40px;
        }
      }
      .loginSubmitBox {
        margin-top: 90px;
        .login-form-button {
          width: 100%;
          height: 40px;
          font-size: 18px;
        }
      }
    }
  }
}
