<template>
  <div class="loginBack">
    <div class="right-container-bg">
      <div class="right-container">
        <div class="sloganBox">
          <img src="@/assets/loginlogo.png" alt="暂无图片" />
          <p class="slogan">
            欢迎登录智能视觉分析与监控系统。我们采用创新的‘平台+应用’架构，为您带来全新的智能化视觉分析与监控体验。
          </p>
        </div>
        <div class="loginFormBox">
          <div>
            <p class="title">用户名</p>
            <a-input
              v-model:value="formState.username"
              class="userInput"
              autocomplete="hidden"
              :maxlength="16"
            />
          </div>
          <div>
            <p class="title">密码</p>
            <a-input-password
              v-model:value="formState.password"
              class="userInput"
              autocomplete="hidden"
              :maxlength="20"
              @pressEnter="getLogin"
            >
            </a-input-password>
          </div>
          <div v-if="errorOut3Times">
            <p class="title">验证码</p>
            <a-input
              v-model:value="formState.captchaValue"
              class="userInput"
              autocomplete="hidden"
              :maxlength="16"
              @pressEnter="getLogin"
            >
              <template #suffix>
                <img
                  class="fit-picture"
                  :src="captchaPic"
                  @click="getCaptchaPic"
                  title="点击刷新验证码"
                  alt="暂无图片"
                />
              </template>
            </a-input>
          </div>
        </div>
        <div class="loginSubmitBox">
          <a-button type="primary" html-type="submit" class="login-form-button" @click="getLogin">
            登录
          </a-button>
        </div>
      </div>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted } from "vue";
import { UserOutlined, LockOutlined, QrcodeOutlined } from "@ant-design/icons-vue";
import { useRouter } from "vue-router";
import { message } from "ant-design-vue";
import { userLogin, getCaptcha, checkFirstConfig } from "@/api/project";
import { JsonStrFun } from "@/common/utils";
import md5 from "md5";
import { useStore } from "vuex";
import SetInterval from "@/common/SetInterval";

interface FormState {
  username: string;
  password: string;
  captchaValue: string | number;
  captchaKey: string;
}
export default defineComponent({
  components: {
    UserOutlined,
    LockOutlined,
    QrcodeOutlined,
  },
  setup() {
    const router = useRouter();
    const store = useStore();
    const formState = ref<FormState>({
      username: "",
      password: "",
      captchaValue: "",
      captchaKey: "",
    });
    // 验证码图片
    const errorOut3Times = ref<boolean>(false);

    const captchaPic = ref<string>("");
    const getCaptchaPic = () => {
      getCaptcha().then((res) => {
        if (res.code === 0) {
          captchaPic.value = res.data.imgBase64;
          formState.value.captchaKey = res.data.captchaKey;
        }
      });
    };

    const getLogin = () => {
      const loginData = {
        username: formState.value.username,
        password: md5(formState.value.password),
        // password: formState.value.password,

        captchaValue: formState.value.captchaValue,
        captchaKey: formState.value.captchaKey,
      };
      userLogin(loginData).then((res) => {
        if (res.code === 0) {
          message.success("登录成功");
          const data = {
            id: res.data.loginId,
            username: loginData.username,
            password: loginData.password,
            token: res.data.tokenValue,
          };
          window.localStorage.setItem("hollysysIntelligent", JsonStrFun(data));
          checkFirstConfig().then((res) => {
            if (res.code === 0) {
              if (res.data) {
                router.push({ name: "systemPreset" });
              } else {
                router.push({ name: "project_design" });
              }
            }
          });
          SetInterval.run("loginTimer");
        }
        if (res.code === 168) {
          errorOut3Times.value = true;
        }
      });
    };

    onMounted(() => {
      getCaptchaPic();
      SetInterval.closeTimeout("loginTimer"); // 关闭全局定时器
    });

    return {
      formState,
      getLogin,
      captchaPic,
      getCaptchaPic,
      errorOut3Times,
    };
  },
});
</script>
<style scoped lang="less">
@import url("./loginStyle.less");
</style>
