import { fabric } from "fabric";
import { JsonParseFun, JsonStrFun } from "@/common/utils";
import { graphicTypeList } from "@/models/design";

class EchoGraphics {
  canvas: any;
  objectBorderWidth: number; // 图形的边框粗细
  objectBorderColor: string; // 图形的边框颜色
  constructor(canvas) {
    this.canvas = canvas;
    this.objectBorderWidth = 2;
    this.objectBorderColor = "#FF0000";
  }

  public echoDraw(paramData: any[]) {
    const pData = JsonParseFun(JsonStrFun(paramData));
    pData.forEach((item, index) => {
      if (graphicTypeList.includes(item.dataType) && item.value !== null) {
        this.echoByType(item);
      }
      if (item.dataType === "ARRAY" && item.value) {
        if (typeof item.value === "string") {
          item.value = JsonParseFun(item.value);
        }
        item.value.forEach((items) => {
          items.params.forEach((element) => {
            if (graphicTypeList.includes(element.type) && element.value !== null) {
              this.echoByType(element, true);
            }
          });
        });
      }
    });
    // const workspace = this.canvas.getObjects().filter((item) => item.id === "workspace")[0];
    // console.log(this.canvas.viewportTransform, "viewportTransform", paramData, workspace);
    // const Objects = this.canvas.getObjects();
    // Objects.forEach((item) => {
    //   if (item.id !== "workspace") {
    //     // 透视变换后，坐标系发生变化，需要重新计算位置及大小
    //     // 调整大小
    //     if (item.width > workspace.width || item.height > workspace.height) {
    //       const minSize = Math.min(workspace.width, workspace.height);
    //       item.set({
    //         width: minSize - this.objectBorderWidth,
    //         height: minSize - this.objectBorderWidth,
    //       });
    //       if (item.type === "CIRCLE") {
    //         item._objects.forEach((obj) => {
    //           if (obj.type === "circle") {
    //             obj.set({
    //               radius: minSize / 2 - this.objectBorderWidth,
    //             });
    //           }
    //         });
    //       }
    //       if (item.type === "ELLIPSE") {
    //         item.set({
    //           rx: minSize / 2,
    //           ry: minSize / 2,
    //         });
    //       }
    //     }

    //     // 调整位置
    //     if (item.left > workspace.width || item.top > workspace.height) {
    //       item.set({ left: 0, top: 0 });
    //       if (item.type === "CIRCLE") {
    //         item.left += item.getScaledWidth() / 2 - this.objectBorderWidth;
    //         item.top += item.getScaledWidth() / 2 - this.objectBorderWidth;
    //       }
    //       if (item.type === "ELLIPSE") {
    //         item.left += item.getRadiusX() - this.objectBorderWidth;
    //         item.top += item.getRadiusY() - this.objectBorderWidth;
    //       }
    //       if (item.type === "POINT") {
    //         item.set({ left: 5, top: 5 });
    //       }
    //     }
    //   }
    // });

    this.canvas.requestRenderAll();
  }

  private echoByType(item, isArray = false) {
    const { canvas, objectBorderColor, objectBorderWidth } = this;
    if (!item.value) return;
    let coordinates = item.value;
    if (typeof item.value === "string") {
      coordinates = JsonParseFun(item.value);
    }
    switch (item.dataType) {
      case "SQUARE":
        const rect = new fabric.Rect({
          left: coordinates[0].x,
          top: coordinates[0].y,
          width: Math.abs(coordinates[0].x - coordinates[2].x),
          height: Math.abs(coordinates[0].y - coordinates[1].y),
          stroke: objectBorderColor,
          strokeWidth: item.key === "roi_cbb8c" ? 5 : objectBorderWidth,
          id: isArray ? item.id : item.key,
          name: isArray ? `${item.id.slice(0, 17)}_${item.name}` : item.name,
          fill: "rgba(255, 255, 255, 0)",
          lockRotation: true,
          type: "SQUARE",
        });
        canvas.add(rect);
        break;
      case "POINT":
        const POINT = new fabric.Circle({
          fill: objectBorderColor,
          radius: 5,
          type: "POINT",
          originX: "center",
          originY: "center",
          left: coordinates.x,
          top: coordinates.y,
          id: isArray ? item.id : item.key,
          name: isArray ? `${item.id.slice(0, 17)}_${item.name}` : item.name,
          hasControls: false,
          lock: false,
          lockRotation: true,
        });
        canvas.add(POINT);
        break;
      case "CIRCLE":
        const circle = new fabric.Circle({
          stroke: objectBorderColor,
          strokeWidth: objectBorderWidth,
          fill: "rgba(255, 255, 255, 0)",
          originX: "center",
          originY: "center",
          radius: coordinates.radius,
          left: coordinates.center.x,
          top: coordinates.center.y,
          id: isArray ? item.id : item.key,
          name: isArray ? `${item.id.slice(0, 17)}_${item.name}` : item.name,
          lockRotation: true,
        });
        const point = new fabric.Circle({
          fill: "#00FF00",
          strokeWidth: 0,
          radius: 5,
          type: "point",
          originX: "center",
          originY: "center",
          left: coordinates.center.x,
          top: coordinates.center.y,
          id: isArray ? item.id : item.key,
          name: isArray ? `${item.id.slice(0, 17)}_${item.name}` : item.name,
          lockRotation: true,
        });

        const group = new fabric.Group([circle, point], {
          left: coordinates.center.x,
          top: coordinates.center.y,
          lockRotation: true,
          originX: "center",
          originY: "center",
          type: "CIRCLE",
          id: isArray ? item.id : item.key,
          name: isArray ? `${item.id.slice(0, 17)}_${item.name}` : item.name,
          controls: fabric.util.object.clone(fabric.Object.prototype.controls),
        });
        ["ml", "mr", "mt", "mb"].forEach((control) => {
          group.controls[control].visible = false;
        });
        canvas.add(group);
        break;
      case "LINE_ARROW":
        let a_x1 = coordinates.start.x,
          a_x2 = coordinates.end.x,
          a_y1 = coordinates.start.y,
          a_y2 = coordinates.end.y;

        let a_w = a_x2 - a_x1,
          a_h = a_y2 - a_y1,
          a_sh = Math.cos(Math.PI / 4) * 16;
        let a_sin = a_h / Math.sqrt(Math.pow(a_w, 2) + Math.pow(a_h, 2));
        let a_cos = a_w / Math.sqrt(Math.pow(a_w, 2) + Math.pow(a_h, 2));

        let a_w1 = (16 * a_sin) / 4,
          a_h1 = (16 * a_cos) / 4,
          a_centerx = a_sh * a_cos,
          a_centery = a_sh * a_sin;
        let a_path = " M " + a_x1 + " " + a_y1;
        a_path += " L " + a_x2 + " " + a_y2;
        a_path += " L " + (a_x2 - a_centerx + a_w1 * 2) + " " + (a_y2 - a_centery - a_h1 * 2);

        a_path += " L " + (a_x2 - a_centerx - a_w1 * 2) + " " + (a_y2 - a_centery + a_h1 * 2);
        a_path += " L " + a_x2 + " " + a_y2;

        const a_line = new fabric.Path(a_path, {
          stroke: objectBorderColor,
          fill: objectBorderColor,
          strokeWidth: objectBorderWidth,
          id: isArray ? item.id : item.key,
          type: "LINE",
          name: isArray ? `${item.id.slice(0, 17)}_${item.name}` : item.name,
          lockRotation: true,
        });
        canvas.add(a_line);
      case "LINE":
        let x1 = coordinates.start.x,
          x2 = coordinates.end.x,
          y1 = coordinates.start.y,
          y2 = coordinates.end.y;

        const path = "M " + x1 + " " + y1 + " L " + x2 + " " + y2;
        const line = new fabric.Path(path, {
          stroke: objectBorderColor,
          fill: objectBorderColor,
          strokeWidth: objectBorderWidth,
          id: isArray ? item.id : item.key,
          type: "LINE",
          name: isArray ? `${item.id.slice(0, 17)}_${item.name}` : item.name,
          lockRotation: true,
        });
        canvas.add(line);

        break;
      case "POLYGON":
        const points = coordinates.map((element) => {
          return {
            x: element.x,
            y: element.y,
          };
        });
        const polygon = new fabric.Polygon(points, {
          stroke: objectBorderColor,
          strokeWidth: objectBorderWidth,
          fill: "rgba(255, 255, 255, 0)",
          id: isArray ? item.id : item.key,
          name: isArray ? `${item.id.slice(0, 17)}_${item.name}` : item.name,
          lockRotation: true,
          type: "POLYGON",
        });
        canvas.add(polygon);
        break;
      case "ELLIPSE":
        const ellipse = new fabric.Ellipse({
          stroke: objectBorderColor,
          strokeWidth: objectBorderWidth,
          fill: "rgba(255, 255, 255, 0)",
          originX: "center",
          originY: "center",
          rx: coordinates.axes[0],
          ry: coordinates.axes[1],
          left: coordinates.center.x,
          top: coordinates.center.y,
          id: isArray ? item.id : item.key,
          name: isArray ? `${item.id.slice(0, 17)}_${item.name}` : item.name,
          lockRotation: true,
          type: "ELLIPSE",
        });
        canvas.add(ellipse);
        break;
      default:
        break;
    }
  }
}

export default EchoGraphics;
