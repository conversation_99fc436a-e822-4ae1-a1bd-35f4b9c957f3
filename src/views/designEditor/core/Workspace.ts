import { fabric } from 'fabric';

class Workspace {
  public canvas:any; // fabricCanvas
  public imgUrl:string; // 背景图片url
  workspaceEl!:HTMLElement; // 画布容器
  workspace!:any; // 画布
  option:any; // 画布大小
  initScale:number; // 画布缩放比例
  constructor(canvas, instanceData, VHoption, Zoom){
    this.canvas = canvas;
    this.imgUrl = instanceData.currentScene.configBenchPic;
    this.init(VHoption,Zoom);
  }

  init(option:any,scale:number){
    const workspaceEl = document.querySelector('#workspace') as HTMLElement;
    if(!workspaceEl){
      throw new Error('找不到#workspace');
    }
    this.workspaceEl = workspaceEl;
    this.workspace = null;
    this.option = option;
    this.initScale = scale;
    this._initBackground();
    this._initWorkspace();
    this._bindWheel();
  }

  // 初始化背景(最外层)
  _initBackground() {
    this.canvas.backgroundImage = '';
    this.canvas.setWidth(this.workspaceEl.offsetWidth);
    this.canvas.setHeight(this.workspaceEl.offsetHeight);
  }

  // 初始化画布
  _initWorkspace() {
    const { width, height } = this.option;
    const workspace = new fabric.Rect({
      fill: 'rgba(255,255,255,0)',
      width,
      height,
      id: 'workspace',
      objectCaching: true // 启用对象缓存
    });
    workspace.set('selectable', false);
    workspace.set('hasControls', false);
    workspace.set('hoverCursor','default');
    this.canvas.add(workspace);
    this.canvas.renderAll();

    this.workspace = workspace;

    this._setCenterFromObject(workspace);
    this._setWorkspaceBackground(this.imgUrl);
    // this.auto();
  }
  /**
   * 缩放
   */
  _bindWheel(){
    const { canvas } = this;
    canvas.on('mouse:wheel', function (opt) {
      const delta = opt.e.deltaY;
      let zoom = canvas.getZoom();
      zoom *= 0.999 ** delta;
      if (zoom > 20) zoom = 20;
      if (zoom < 0.01) zoom = 0.01;
      // const center = canvas.getCenter();
      canvas.zoomToPoint({ // 关键点
        x: opt.e.offsetX,
        y: opt.e.offsetY
      }, zoom);
      opt.e.preventDefault();
      opt.e.stopPropagation();
    });
  }
  /**
   * 还原缩放
   */
  $backInitZoom(){
    const { canvas,workspace } = this;
    const viewportTransform = canvas.viewportTransform;
    viewportTransform[0] = this.initScale; 
    viewportTransform[3] = this.initScale;
    const objCenter = workspace.getCenterPoint();
    viewportTransform[4] = canvas.width / 2 - objCenter.x * viewportTransform[0];
    viewportTransform[5] = canvas.height / 2 - objCenter.y * viewportTransform[3];
    canvas.setViewportTransform(viewportTransform);
    canvas.renderAll();
  }
  /**
   * 设置画布中心到指定对象中心点上
   * @param obj 画布对象
   * @returns 
   */
  _setCenterFromObject(obj){
    const { canvas } = this;
    const objCenter = obj.getCenterPoint();
    canvas.viewportTransform[0] = this.initScale;
    canvas.viewportTransform[3] = this.initScale;
    const viewportTransform = canvas.viewportTransform;
    if (canvas.width === undefined || canvas.height === undefined || !viewportTransform) return;

    viewportTransform[4] = canvas.width / 2 - objCenter.x * viewportTransform[0];
    viewportTransform[5] = canvas.height / 2 - objCenter.y * viewportTransform[3];
    canvas.setViewportTransform(viewportTransform);
    canvas.renderAll();
    // 不超出画布

    this.workspace.clone((cloned) => {
      // this.canvas.clipPath = cloned;
      this.canvas.requestRenderAll();
    });
  }
  /**
   * 设置画布背景
   * @param url 图片url
   */
  _setWorkspaceBackground(url){
    const {canvas} = this
    fabric.Image.fromURL(url, img => {
      canvas.setBackgroundImage(img)
      canvas.renderAll()
    })
  }
  _delWorkspaceBackground(){
    const { canvas } = this;
    canvas.getObjects().forEach((item)=>{
      if(item.id === 'workspace'){
        canvas.remove(item);
      }
    })
  }
  
}

export default Workspace;