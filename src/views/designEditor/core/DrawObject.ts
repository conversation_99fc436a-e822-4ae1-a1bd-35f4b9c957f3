import { fabric } from "fabric";
import { v4 as uuid } from "uuid";
import { graphicTypeList } from "@/models/design";
import { message } from "ant-design-vue";
import uEmitter from "@/common/u-event-bus";

class DrawObject {
  store: any;
  public canvas: any;
  public workspace: any;
  isDrawMode: boolean; // 是否是绘制模式
  isDrawing: boolean; // 是否在绘制中
  isSelectColor: boolean; // 是否选择颜色
  colorNodeInfo: any; // 选择颜色的节点信息
  drawType: string; // 当前绘制类型
  LineMode: null | boolean; // 是否是直线模式
  drawId: string; // 绘制的ID
  objectToDraw: any; // 绘制的object
  mouseFrom: any; // 图形起点
  mouseTo: any; // 图形终点
  objectBorderWidth: number; // 图形的边框粗细
  objectBorderColor: string; // 图形的边框颜色
  drawedObjectId: string[]; // 已绘制图形的ID
  paramName: string; // 图形所属的参数名称
  moveCount: number;
  paramsData: any;

  // 多边形
  pointArray: any[]; // 多边形点的集合
  lineArray: any[]; // 多边形线的集合
  activeShape: any;
  activeLine: any;
  line: any;

  constructor(canvas, workspace, paramsData, store) {
    this.store = store;
    this.canvas = canvas;
    this.workspace = workspace;
    this.isDrawMode = false;
    this.isDrawing = false;
    this.isSelectColor = false;
    this.colorNodeInfo = null;
    this.drawType = "";
    this.LineMode = null;
    this.drawId = "";
    this.objectToDraw = null;
    this.mouseFrom = {
      x: 0,
      y: 0,
    };
    this.mouseTo = {
      x: 0,
      y: 0,
    };
    this.objectBorderWidth = 2;
    this.objectBorderColor = "#FF0000";
    // this.drawedObjectId = drawedId;
    this.moveCount = 1;
    this.paramsData = paramsData;
    // 初始化多边形
    this.pointArray = [];
    this.lineArray = [];
    this.activeShape = null;
    this.activeLine = null;
    this.line = null;
    this.init();
  }

  init() {
    const { canvas, workspace } = this;
    canvas.on("mouse:down", (e) => {
      if (this.isSelectColor) {
        return;
      }

      if (!this.isDrawMode) return;
      canvas.discardActiveObject();
      canvas.getObjects().forEach((element) => {
        element.selectable = false;
        element.hasControls = false;
      });
      canvas.requestRenderAll();
      this.mouseFrom.x = e.absolutePointer.x;
      this.mouseFrom.y = e.absolutePointer.y;
      if (this.drawType === "POINT") {
        this.objectToDraw = new fabric.Circle({
          fill: this.objectBorderColor,
          radius: 5,
          left: this.mouseFrom.x,
          top: this.mouseFrom.y,
          id: this.drawId,
          type: "POINT",
          originX: "center",
          originY: "center",
          name: this.paramName,
          hasControls: false,
          lockRotation: true,
        });
        canvas.add(this.objectToDraw);
        canvas.renderAll();
        this.endRest();
        return;
      }
      if (this.drawType === "POLYGON") {
        const { pointArray, drawType } = this;
        if (pointArray.length > 1) {
          if (e.target && e.target.id === pointArray[0].id) {
            this.generatePolygon();
            return;
          }
        }
        if (drawType === "POLYGON") {
          this.addPointForPolygon(e);
        }
      }

      this.isDrawing = true;
    });

    canvas.on("mouse:move", (e) => {
      if (this.isSelectColor) return;
      if (!this.isDrawing) return;
      canvas.discardActiveObject();
      const activeObject = canvas.getActiveObject();
      if (activeObject) return;
      if (this.moveCount % 2) {
        // 减少绘制频率
        this.moveCount++;
        return;
      }
      this.mouseTo.x = e.absolutePointer.x;
      this.mouseTo.y = e.absolutePointer.y;
      if (this.drawType !== "POLYGON") {
        if (this.objectToDraw) {
          canvas.remove(this.objectToDraw);
        }
        this.darwObject();
        canvas.add(this.objectToDraw);
        canvas.renderAll();
      }
      if (this.drawType === "POLYGON") {
        if (this.activeLine && this.activeLine.class === "line") {
          const pointer = this.canvas.getPointer(e.e);
          this.activeLine.set({
            x2: pointer.x,
            y2: pointer.y,
          });

          const points = this.activeShape.get("points");
          points[this.pointArray.length] = {
            x: pointer.x,
            y: pointer.y,
            zIndex: 1,
          };
          this.activeShape.set({
            points,
          });
          canvas.renderAll();
        }
        canvas.renderAll();
      }
    });

    canvas.on("mouse:up", (e) => {
      if (this.isSelectColor) {
        const pointer = canvas.getPointer(e, true);
        const x = Math.floor(pointer.x);
        const y = Math.floor(pointer.y);

        const context = canvas.getContext();
        const pixelData = context.getImageData(x, y, 1, 1).data;
        const rgba = [pixelData[0], pixelData[1], pixelData[2]];
        console.log("colorNodeInfo===>", this.colorNodeInfo);
        // const roidata = this.paramsData.getSomeParamsBySubmitData("roi");
        const paramList = this.paramsData.getSomeParamsBySubmitData("paramList");
        paramList.forEach((item) => {
          if (item.key === this.colorNodeInfo.key) {
            item.value = rgba;
          }
        });
        console.log("paramList===>", paramList);
        this.paramsData.setSubmitData("paramList", paramList);

        uEmitter.emit("colorPicker", { item: this.colorNodeInfo, color: rgba });

        this.canvas.defaultCursor = "default";
        this.canvas.perPixelTargetFind = false;
        this.isSelectColor = false;
        return;
      }
      canvas.renderAll();
      let obj: any = null;
      // const obj = e.target;

      if (e.target) {
        obj = e.target;
      } else {
        obj = canvas.getObjects().slice(-1)[0];
      }
      // const
      const minSize = 15;
      const zoom = canvas.viewportTransform[0];
      const marginX = canvas.viewportTransform[4];
      const marginY = canvas.viewportTransform[5];
      const marginXright = workspace.width * zoom + marginX;
      const marginYright = workspace.height * zoom + marginY;
      if (obj) {
        let objBoundingRect = obj.getBoundingRect();
        if (obj.type !== "POINT" && obj.type !== "POLYGON" && obj.type !== "circle") {
          console.log(objBoundingRect.width / zoom ,objBoundingRect.height / zoom);
          if (objBoundingRect.width / zoom < minSize || objBoundingRect.height / zoom < minSize) {
            message.warn(`绘制图形不能小于${minSize}px*${minSize}px`);
            if (obj.type === "CIRCLE" || obj.type === "ELLIPSE") {
              this.limitSize(obj, minSize * 2);
            } else {
              this.limitSize(obj, minSize);
            }
          }
        }

        // 计算对象的原始缩放比例
        const scaleX = (workspace.width * zoom) / objBoundingRect.width;
        const scaleY = (workspace.height * zoom) / objBoundingRect.height;
        // const scaleToFit = Math.min(scaleX, scaleY);
        //超出画布大小了
        if (scaleX < 1) {
          // 应用缩放比例
          obj.set({
            scaleX: obj.scaleX * scaleX,
          });
          canvas.renderAll();
        }

        if (scaleY < 1) {
          // 应用缩放比例
          obj.set({
            scaleY: obj.scaleY * scaleY,
          });
        }

        // 超出左边
        if (objBoundingRect.left < marginX) {
          obj.set({ left: 0 });
          if (obj.type === "CIRCLE") {
            obj.left += obj.getScaledWidth() / 2;
          }
          if (obj.type === "ELLIPSE") {
            obj.left += obj.getRadiusX();
          }
          if (obj.type === "POINT") {
            obj.set({ left: 5 });
          }
        }
        // 超出上边
        if (objBoundingRect.top < marginY) {
          obj.set({ top: 0 });
          if (obj.type === "CIRCLE") {
            obj.top += obj.getScaledWidth() / 2;
          }
          if (obj.type === "ELLIPSE") {
            obj.top += obj.getRadiusY();
          }
          if (obj.type === "POINT") {
            obj.set({ left: 5 });
          }
        }
        // 超出右边
        if (objBoundingRect.left + objBoundingRect.width > marginXright) {
          obj.left = workspace.getScaledWidth() - obj.getScaledWidth() - this.objectBorderWidth;

          if (obj.type === "CIRCLE") {
            obj.left =
              workspace.getScaledWidth() - obj.getScaledWidth() / 2 - this.objectBorderWidth;
            console.log(obj.left);
          }
          if (obj.type === "ELLIPSE") {
            obj.left = workspace.getScaledWidth() - obj.getRadiusX() - this.objectBorderWidth;
          }
        }
        // 超出下边
        if (objBoundingRect.top + objBoundingRect.height > marginYright) {
          obj.top = workspace.getScaledHeight() - obj.getScaledHeight() - this.objectBorderWidth;

          if (obj.type === "CIRCLE") {
            obj.top =
              workspace.getScaledHeight() - obj.getScaledWidth() / 2 - this.objectBorderWidth;
          }
          if (obj.type === "ELLIPSE") {
            obj.top = workspace.getScaledHeight() - obj.getRadiusY() - this.objectBorderWidth;
          }
        }
        obj.strokeUniform = true;
        // obj.set({
        //   zoomX:0.6,
        //   zoomY:0.6
        // })
        // console.log(obj,'obj')
        // canvas.setActiveObject(obj)
        if (Object.keys(obj).includes("_objects") && obj.type !== "CIRCLE") {
          canvas.discardActiveObject();
        }

        obj.setCoords();
      }
      // if(e.target.id !== 'workspace'){

      // }

      this.hasActiveObj();
      this.markData2ParamList();
      if (!this.isDrawing) return;
      // 将图形标记到已保存的数据中
      if (this.drawType !== "POLYGON") {
        this.endRest();
      }
    });
  }
  // 限制图形是否过小
  limitSize(obj, minSize) {
    const actualWidth = obj.width * obj.scaleX;
    const actualHeight = obj.height * obj.scaleY;
    // 修正宽度
    if (actualWidth < minSize) {
      obj.scaleX = minSize / obj.width;
    }
    // 修正高度
    if (actualHeight < minSize) {
      obj.scaleY = minSize / obj.height;
    }

    // 更新控件位置和渲染
    this.canvas.requestRenderAll();
  }

  // 选中图形
  hasActiveObj() {
    if (this.canvas.getActiveObjects().length === 1) {
      this.store.commit("chengeSelectOneByObj", this.canvas.getActiveObjects()[0]);
    } else {
      this.store.commit("chengeSelectOneByObj", null);
    }
  }

  /**
   * 根据type绘制图形
   */
  darwObject() {
    const { mouseFrom, mouseTo, objectBorderColor, paramName, objectBorderWidth, drawType } = this;
    let canvasObject = null;
    // debugger;
    switch (drawType) {
      case "ELLIPSE": // 椭圆
        canvasObject = new fabric.Ellipse({
          left: (mouseTo.x - mouseFrom.x) / 2 + mouseFrom.x,
          top: (mouseTo.y - mouseFrom.y) / 2 + mouseFrom.y,
          stroke: objectBorderColor,
          fill: "rgba(255, 255, 255, 0)",
          originX: "center",
          originY: "center",
          id: this.drawId,
          rx: Math.abs(mouseFrom.x - mouseTo.x) / 2,
          ry: Math.abs(mouseFrom.y - mouseTo.y) / 2,
          strokeWidth: objectBorderWidth,
          name: paramName,
          lockRotation: true,
          type: "ELLIPSE",
        });
        break;
      case "CIRCLE": // 正圆
        const CIRCLE = new fabric.Circle({
          left: mouseFrom.x,
          top: mouseFrom.y,
          stroke: objectBorderColor,
          fill: "rgba(255, 255, 255, 0)",
          originX: "center",
          originY: "center",
          id: this.drawId,
          radius: Math.abs(mouseFrom.x - mouseTo.x) / 2,
          strokeWidth: objectBorderWidth,
          name: paramName,
          lockRotation: true,
          type: "CIRCLE",
        });
        const POINT = new fabric.Circle({
          left: CIRCLE.left,
          top: CIRCLE.top,
          radius: 4,
          fill: "#00FF00",
          originX: "center",
          originY: "center",
          strokeWidth: 0,
          type: "POINT",
        });
        canvasObject = new fabric.Group([CIRCLE, POINT], {
          name: paramName,
          id: this.drawId,
          lockRotation: true,
          originX: "center",
          originY: "center",
          type: "CIRCLE",
          controls: fabric.util.object.clone(fabric.Object.prototype.controls),
        });
        ["ml", "mr", "mt", "mb"].forEach((control) => {
          canvasObject.controls[control].visible = false;
        });
        // canvasObject = new fabric.Circle({
        //   left: mouseFrom.x,
        //   top: mouseFrom.y,
        //   stroke: objectBorderColor,
        //   fill: "rgba(255, 255, 255, 0)",
        //   originX: "center",
        //   originY: "center",
        //   id: this.drawId,
        //   radius: Math.abs(mouseFrom.x - mouseTo.x) / 2,
        //   strokeWidth: objectBorderWidth,
        //   name: paramName,
        //   lockRotation: true,
        //   type: "circle",
        // });
        break;
      case "SQUARE": // 长方形
        canvasObject = new fabric.Rect({
          left: mouseFrom.x - mouseTo.x < 0 ? mouseFrom.x : mouseTo.x,
          top: mouseFrom.x - mouseTo.x < 0 ? mouseFrom.y : mouseTo.y,
          width: Math.abs(mouseFrom.x - mouseTo.x),
          height: Math.abs(mouseFrom.y - mouseTo.y),
          stroke: objectBorderColor,
          strokeWidth: objectBorderWidth,
          name: paramName,
          id: this.drawId,
          fill: "rgba(255, 255, 255, 0)",
          lockRotation: true,
          type: "SQUARE",
        });
        break;

      case "LINE_ARROW": // 直线箭头
        var x1 = mouseFrom.x,
          x2 = mouseTo.x,
          y1 = mouseFrom.y,
          y2 = mouseTo.y;
        var w = x2 - x1,
          h = y2 - y1,
          sh = Math.cos(Math.PI / 4) * 16;
        var sin = h / Math.sqrt(Math.pow(w, 2) + Math.pow(h, 2));
        var cos = w / Math.sqrt(Math.pow(w, 2) + Math.pow(h, 2));
        var w1 = (16 * sin) / 4,
          h1 = (16 * cos) / 4,
          centerx = sh * cos,
          centery = sh * sin;
        /**
         * centerx,centery 表示起始点，终点连线与箭头尖端等边三角形交点相对x，y
         * w1 ，h1用于确定四个点
         */

        var path = " M " + x1 + " " + y1;
        path += " L " + x2 + " " + y2;
        // path += " L " + (x2 - centerx + w1) + " " + (y2 - centery - h1);
        path += " L " + (x2 - centerx + w1 * 2) + " " + (y2 - centery - h1 * 2);

        path += " L " + (x2 - centerx - w1 * 2) + " " + (y2 - centery + h1 * 2);
        path += " L " + x2 + " " + y2;
        // path += " L " + (x2 - centerx - w1) + " " + (y2 - centery + h1);
        // path += " Z";
        canvasObject = new fabric.Path(path, {
          stroke: objectBorderColor,
          fill: objectBorderColor,
          strokeWidth: objectBorderWidth,
          id: this.drawId,
          type: "LINE",
          name: paramName,
          lockRotation: true,
        });
        break;
      case "LINE": // 线
        const path_s = "M " + mouseFrom.x + " " + mouseFrom.y + " L " + mouseTo.x + " " + mouseTo.y;
        canvasObject = new fabric.Path(path_s, {
          stroke: objectBorderColor,
          strokeWidth: objectBorderWidth,
          id: this.drawId,
          name: paramName,
          lockRotation: true,
          type: "LINE",
        });

        break;
      default:
        break;
    }
    this.objectToDraw = canvasObject;
  }

  addPointForPolygon(e) {
    const id = uuid();
    const pointForPolygon = new fabric.Circle({
      radius: 5,
      fill: "#ffffff",
      stroke: "#333333",
      strokeWidth: 0.5,
      left: this.mouseFrom.x,
      top: this.mouseFrom.y,
      hasBorders: false,
      hasControls: false,
      originX: "center",
      originY: "center",
      id,
      objectCaching: false,
      lockMovementX: true,
      lockMovementY: true,
      lockScalingX: true,
      lockScalingY: true,
    });
    if (this.pointArray.length === 0) {
      pointForPolygon.set({
        fill: "red",
      });
    }
    const points = [this.mouseFrom.x, this.mouseFrom.y, this.mouseFrom.x, this.mouseFrom.y];
    this.line = new fabric.Line(points, {
      strokeWidth: 2,
      fill: "#999999",
      stroke: "#999999",
      class: "line",
      originX: "center",
      originY: "center",
      selectable: false,
      hasBorders: false,
      hasControls: false,
      evented: false,
      objectCaching: false,
    });
    if (this.activeShape) {
      const pos = this.canvas.getPointer(e.e);
      const pointsByPoly = this.activeShape.get("points");
      pointsByPoly.push({
        x: pos.x,
        y: pos.y,
      });
      const POLYGON = new fabric.Polygon(pointsByPoly, {
        stroke: "#333333",
        strokeWidth: 1,
        fill: "#cccccc",
        opacity: 0.3,

        selectable: false,
        hasBorders: false,
        hasControls: false,
        evented: false,
        objectCaching: false,
        type: "POLYGON",
      });
      this.canvas.remove(this.activeShape);
      this.canvas.add(POLYGON);
      this.activeShape = POLYGON;
      this.canvas.renderAll();
    } else {
      const polyPoint = [
        {
          x: this.mouseFrom.x,
          y: this.mouseFrom.y,
        },
      ];
      const POLYGON = new fabric.Polygon(polyPoint, {
        stroke: "#333333",
        strokeWidth: 1,
        fill: "#cccccc",
        opacity: 0.3,

        selectable: false,
        hasBorders: false,
        hasControls: true,
        evented: false,
        objectCaching: false,
      });
      this.activeShape = POLYGON;
      this.canvas.add(POLYGON);
      this.canvas.renderAll();
    }
    this.activeLine = this.line;
    this.pointArray.push(pointForPolygon);
    this.lineArray.push(this.line);
    this.canvas.add(this.line);
    this.canvas.add(pointForPolygon);
  }

  /**
   * 生成多边形
   */
  generatePolygon() {
    const { canvas, paramsData, objectBorderColor, objectBorderWidth } = this;
    const points = [];
    this.pointArray.forEach((POINT) => {
      points.push({
        x: POINT.left,
        y: POINT.top,
      });
      canvas.remove(POINT);
    });
    this.lineArray.forEach((line) => {
      canvas.remove(line);
    });
    canvas.remove(this.activeShape).remove(this.activeLine);
    const POLYGON = new fabric.Polygon(points, {
      stroke: objectBorderColor,
      strokeWidth: objectBorderWidth,
      fill: "rgba(255, 255, 255, 0)",
      opacity: 1,
      id: this.drawId,
      name: this.paramName,
      lockRotation: true,
      type: "POLYGON",
    });
    canvas.add(POLYGON);
    canvas.renderAll();
    this.activeLine = null;
    this.activeShape = null;
    this.pointArray = [];
    this.lineArray = [];
    this.line = null;
    this.endRest();
  }

  /** 清空多边形边点 */

  clearPolygonPointAndLine() {
    this.activeLine = null;
    this.activeShape = null;
    this.pointArray = [];
    this.lineArray = [];
    this.line = null;
    this.endRest();
  }

  /**
   * 选择是否进入到绘制模式
   */
  setModeAndType(param: any) {
    const { paramsData, canvas } = this;
    if (param.paramName === "RoI") {
      const workspace = this.canvas.getObjects().find((item) => item.id === "workspace");
      const width = workspace.getScaledWidth();
      const height = workspace.getScaledHeight();
      const rect = new fabric.Rect({
        left: 0,
        top: 0,
        width: width - 8,
        height: height - 8,
        stroke: "#ff0000",
        strokeWidth: 5,
        fill: "rgba(255, 255, 255, 0)",
        objectCaching: false,
        id: param.id,
        type: "SQUARE",
        name: param.paramName,
        lockRotation: true,
        selectable: true, // 保持可选中
        evented: true,
        perPixelTargetFind: true, // 精确点击检测
      });
      this.canvas.add(rect);
      this.canvas.renderAll();

      // 将roi装到SubmitData中
      const roiObj = canvas.getObjects().find((item) => item.id === "roi_cbb8c");
      if (roiObj) {
        const roidata = paramsData.getSomeParamsBySubmitData("roi");
        if (roidata) {
          roidata.coordinates = this.getObjectValue("roi_cbb8c", roiObj.type);
        }
        paramsData.setSubmitData("roi", roidata);
        paramsData.editChangeStatus();
      }
      return;
    }
    this.isDrawMode = param.isDrawMode;
    this.drawType = param.drawType;
    this.paramName = param.paramName;
    this.LineMode = param.LineMode;
    this.drawId = param.id;

    this.canvas.perPixelTargetFind = true;
    this.canvas.defaultCursor = "crosshair";
    this.canvas.selection = false;
    this.canvas.getObjects().forEach((element) => {
      element.selectable = false;
      element.hasControls = false;
    });
  }

  /**
   * 选择颜色模式
   */
  setColorMode(item) {
    this.isSelectColor = true;
    this.colorNodeInfo = item;
    this.canvas.perPixelTargetFind = true;
    this.canvas.defaultCursor = "crosshair";
  }
  /**
   * 绘制结束
   */
  endRest() {
    this.isDrawMode = false;
    this.isDrawing = false;
    this.objectToDraw = null;
    this.paramName = null;
    this.moveCount = 1;
    this.canvas.defaultCursor = "default";
    this.canvas.perPixelTargetFind = false;
    this.canvas.discardActiveObject();
    this.canvas.selection = true;
    this.canvas.getObjects().forEach((obj) => {
      if (obj.id !== "workspace") {
        obj.selectable = true;
      }

      if (obj.id !== "workspace" && !obj.type.includes("POINT")) {
        obj.hasControls = true;
      }
    });
  }

  /**
   * 标记图形同通过paramList
   */
  markData2ParamList() {
    const { paramsData, canvas } = this;
    canvas.renderAll();
    const paramList = paramsData.getSomeParamsBySubmitData("paramList");
    paramList.forEach((item) => {
      if (item.dataType === "array") {
        if (item.value) {
          item.value.forEach((element) => {
            element.params.forEach((param) => {
              if (graphicTypeList.includes(param.dataType)) {
                param.value = this.getObjectValue(param.id, param.dataType);
              }
            });
          });
        }
      }
      if (graphicTypeList.includes(item.dataType)) {
        item.value = this.getObjectValue(item.key, item.dataType);
      }
    });
    // 将roi装到SubmitData中
    const roiObj = canvas.getObjects().find((item) => item.id === "roi_cbb8c");

    if (roiObj) {
      const roidata = paramsData.getSomeParamsBySubmitData("roi");
      console.log("roidata+++++++++++++++++", roidata);
      if (roidata) {
        roidata.coordinates = this.getObjectValue("roi_cbb8c", roiObj.type);
      }
      paramsData.setSubmitData("roi", roidata);
    }
    paramsData.setSubmitData("paramList", paramList);
    paramsData.editChangeStatus();
  }

  /**
   *
   * @param id 图形的id
   * @param type 图形的type
   * @returns 图形参数
   */
  getObjectValue(id, type) {
    const { canvas, objectBorderWidth } = this;
    const canvasObject = canvas.getObjects();
    // if (isMain) {
    if (!canvasObject.find((item) => item.id === id)) return null;
    for (let index = 0; index < canvasObject.length; index++) {
      if (canvasObject[index].id === id) {
        if (type === "SQUARE") {
          const bounds = canvasObject[index].getBoundingRect(true);
          const topLeft = {
            x: bounds.left,
            y: bounds.top,
          };
          const topRight = {
            x: bounds.left + bounds.width - objectBorderWidth,
            y: bounds.top,
          };
          const bottomRight = {
            x: bounds.left + bounds.width - objectBorderWidth,
            y: bounds.top + bounds.height - objectBorderWidth,
          };
          const bottomLeft = {
            x: bounds.left,
            y: bounds.top + bounds.height - objectBorderWidth,
          };
          return [
            { x: Math.round(topLeft.x), y: Math.round(topLeft.y) },
            { x: Math.round(bottomLeft.x), y: Math.round(bottomLeft.y) },
            { x: Math.round(bottomRight.x), y: Math.round(bottomRight.y) },
            { x: Math.round(topRight.x), y: Math.round(topRight.y) },
          ];
        }
        if (type === "POINT") {
          return { x: canvasObject[index].left, y: canvasObject[index].top };
        }
        if (type === "CIRCLE") {
          return {
            center: {
              x: Math.round(canvasObject[index].left),
              y: Math.round(canvasObject[index].top),
            },
            radius:
              canvasObject[index].type === "CIRCLE"
                ? Math.round(canvasObject[index].getScaledWidth() / 2)
                : Math.round(canvasObject[index].rx),
          };
        }
        if (type === "ELLIPSE") {
          return {
            center: {
              x: Math.round(canvasObject[index].left),
              y: Math.round(canvasObject[index].top),
            },
            axes: [Math.round(canvasObject[index].rx), Math.round(canvasObject[index].ry)],
            angle: canvasObject[index].angle,
          };
        }
        if (type === "LINE_ARROW") {
          const line = canvasObject[index];

          var matrix = line.calcTransformMatrix();
          var transformedPoints = line
            .get("path")
            .map((path) => {
              // path.slice(1)
              return {
                x: path.slice(1)[0],
                y: path.slice(1)[1],
              };
            })
            .map(function (p) {
              return new fabric.Point(p.x - line.pathOffset.x, p.y - line.pathOffset.y);
            })
            .map(function (p) {
              return fabric.util.transformPoint(p, matrix);
            });

          const linePoint = transformedPoints.map((item) => {
            return [item.x, item.y];
          });

          return {
            start: { x: Math.round(linePoint[0][0]), y: Math.round(linePoint[0][1]) },
            end: {
              x: Math.round(linePoint[linePoint.length - 1][0]),
              y: Math.round(linePoint[linePoint.length - 1][1]),
            },
          };
        }
        if (type === "LINE") {
          const line = canvasObject[index];

          var matrix = line.calcTransformMatrix();
          var transformedPoints = line
            .get("path")
            .map((path) => {
              // path.slice(1)
              return {
                x: path.slice(1)[0],
                y: path.slice(1)[1],
              };
            })
            .map(function (p) {
              return new fabric.Point(p.x - line.pathOffset.x, p.y - line.pathOffset.y);
            })
            .map(function (p) {
              return fabric.util.transformPoint(p, matrix);
            });

          const linePoint = transformedPoints.map((item) => {
            return [item.x, item.y];
          });

          return {
            start: { x: Math.round(linePoint[0][0]), y: Math.round(linePoint[0][1]) },
            end: {
              x: Math.round(linePoint[linePoint.length - 1][0]),
              y: Math.round(linePoint[linePoint.length - 1][1]),
            },
          };
        }
        if (type === "POLYGON") {
          const POLYGON = canvasObject[index];
          var matrix = POLYGON.calcTransformMatrix();
          var transformedPoints = POLYGON.get("points")
            .map(function (p) {
              return new fabric.Point(p.x - POLYGON.pathOffset.x, p.y - POLYGON.pathOffset.y);
            })
            .map(function (p) {
              return fabric.util.transformPoint(p, matrix);
            });

          const polygonPoint = transformedPoints.map((item) => {
            return { x: Math.round(item.x), y: Math.round(item.y) };
          });
          return polygonPoint;
        }
      }
    }
  }
}

export default DrawObject;
