import { fabric } from "fabric";
import Workspace from "./Workspace";
import DrawObject from "./DrawObject";
import BindData from "./BindData";
import EchoGraphics from "./EchoGraphics";
import { graphicTypeList } from "@/models/design";
import { JsonParseFun, JsonStrFun } from "@/common/utils";
class Editor {
  canvas: any;
  // workspace实例
  workspace: any;
  drawObject: any;
  paramsData: any;
  echoGraphics: any;
  store: any;
  constructor() {
    this.paramsData = new BindData();
    document.onkeydown = (e) => {
      // 键盘 delect删除所选元素
      if (e.code === "Delete") {
        this.deleteObj();
      }
    };
  }
  /**
   * 初始化fabric
   * @param canvas fabric实例
   */
  initFabric(canvas) {
    this.canvas = canvas;
  }

  /**
   * 初始化背景
   * @param canvas fabric实例
   * @param InstanceData 图形数据
   * @param VHoption 纵向横向位移
   * @param Zoom 缩放
   * @param store vuex
   */
  initCanvasBg(canvas, InstanceData, VHoption, Zoom, store?) {
    this.store = store;
    this.canvas = canvas;
    this.workspace = new Workspace(canvas, InstanceData, VHoption, Zoom);
    this.drawObject = new DrawObject(canvas, this.workspace.workspace, this.paramsData, store);
    this.echoGraphics = new EchoGraphics(canvas);
  }
  // 获取当前canvas元素
  checkObj() {
    const { canvas, workspace } = this;
    console.log(canvas.getObjects());
  }
  // 还原缩放
  backZoom() {
    const { workspace } = this;
    workspace.$backInitZoom();
  }
  // 检查画布是否已有参数
  checkObjectById(id) {
    const { canvas, store } = this;
    const findObject = canvas.getObjects().find((item) => item.id === id);
    if (findObject) {
      const top = findObject.get("top");
      findObject.animate("top", top - 100, {
        duration: 500,
        onChange: canvas.renderAll.bind(canvas),
        onComplete: function () {
          // findObject.set({
          //   top:top
          // })
          // canvas.discardActiveObject();
          // canvas.setActiveObject(findObject);
          // // findObject.selectable = true;
          // canvas.renderAll();
          // canvas.requestRenderAll();
          // store.commit('chengeSelectOneByObj',findObject)
          findObject.animate("top", top, {
            duration: 200,
            onChange: canvas.renderAll.bind(canvas),
            onComplete: function () {
              // findObject.set({
              //   top:top
              // })
              canvas.discardActiveObject();
              canvas.setActiveObject(findObject);
              // findObject.selectable = true;
              canvas.renderAll();
              canvas.requestRenderAll();
              store.commit("chengeSelectOneByObj", findObject);
            },
            easing: fabric.util.ease["easeOut"],
          });
        },
        easing: fabric.util.ease["easeOutBounce"],
      });
    }

    return findObject;
  }
  // 切换到编辑模式并指定图形类型
  setModeAndType(param: any) {
    const { drawObject } = this;
    drawObject.setModeAndType(param);
  }

  // 切换到颜色选择模式
  setColorMode(item) {
    const { canvas } = this;
    this.drawObject.setColorMode(item);
  }

  // 获取图形

  // 清空画布
  clearCanvas() {
    const { canvas } = this;
    canvas.getObjects().forEach((item) => {
      if (item.id !== "workspace") {
        canvas.remove(item);
      }
    });
    canvas.renderAll();
    const paramList = this.paramsData.getSomeParamsBySubmitData("paramList");
    const roi = this.paramsData.getSomeParamsBySubmitData("roi");
    paramList.forEach((item) => {
      if (graphicTypeList.includes(item.dataType) && item.value) {
        item.value = null;
      }
      if (item.dataType === "array") {
        if (item.value) {
          item.value.forEach((point) => {
            point.params.forEach((param) => {
              if (param.value && graphicTypeList.includes(param.dataType)) {
                param.value = null;
                param.id = null;
              }
            });
          });
        }
      }
    });
    if (roi) {
      const tempRoi = { ...roi, coordinates: null };
      this.paramsData.setSubmitData("roi", tempRoi);
    }
    // console.log(paramList)
    this.paramsData.setSubmitData("paramList", paramList);

    this.drawObject.clearPolygonPointAndLine();
  }

  // 删除array删除图形
  deleteCanvasByArray(id) {
    const { canvas, paramsData } = this;
    canvas.getObjects().forEach((item) => {
      if (item.id.includes(id)) {
        canvas.remove(item);
      }
    });
  }

  // 删除图形
  deleteObj() {
    const { canvas } = this;
    const deleteObjId = [];
    canvas.getActiveObjects().forEach((item) => {
      deleteObjId.push(item.id);
      canvas.remove(item);
    });
    canvas.renderAll();

    const paramList = this.paramsData.getSomeParamsBySubmitData("paramList");
    paramList.forEach((item) => {
      if (graphicTypeList.includes(item.dataType) && item.value) {
        if (deleteObjId.includes(item.key)) {
          item.value = null;
        }
      }
      if (item.dataType === "array") {
        item.value.forEach((point) => {
          point.params.forEach((param) => {
            if (param.value && graphicTypeList.includes(param.dataType)) {
              if (deleteObjId.includes(param.id)) {
                param.value = null;
                param.id = null;
              }
            }
          });
        });
      }
    });
    if (deleteObjId.includes("roi_cbb8c")) {
      this.paramsData.setSubmitData("roi", null);
    }
    this.paramsData.setSubmitData("paramList", paramList);
  }
}

export default Editor;
