import { JsonParseFun, JsonStrFun } from '@/common/utils';
import EventEmitter from 'events';
import _ from 'lodash'


class BindData extends EventEmitter {
  public paramsData:any; // 后台返回的数据
  public submitData:Map<string,any>; // 需要提交的参数
  public editStatus:boolean;
  constructor(){
    super();
    this.paramsData = null;
    this.submitData = new Map();
    this.editStatus = false;
  }

  public setData(data){
    this.paramsData = data
  }

  public getData(){
    return this.paramsData
  }

  public setSubmitData(key:string,value:any){
    this.submitData.set(key,value)
  }
  public getSomeParamsBySubmitData(key:string){
    const deepClone = _.cloneDeep(this.submitData.get(key))
    return deepClone;
  }
  public getAnyParamsBySubmitData(){
    const {submitData } = this;
    const newMapList = [];
    for (const iterator of submitData.entries()) {
      newMapList.push(iterator);
    }
    return _.cloneDeep(newMapList);
  }

  // 修改任意编辑项启用编辑状态
  public editChangeStatus(){
    this.editStatus = true;
  } 
  // 重置编辑状态
  public resettingEditStatus(){
    this.editStatus = false;
  }
  
}

export default BindData;