<template>
  <div>
    <a-spin :spinning="spinning" tip="识别中....">
      <a-layout class="layoutAlConf">
        <a-layout-header class="layoutHeaderBox">
          <div class="headerBox">
            <div class="leftBtn">算法配置</div>
            <p class="title">
              <span>{{ channelName }} > {{ presetName }} > {{ title }}</span>
            </p>
            <div class="rightBtn">
              <a-space>
                <a-image
                  :width="200"
                  :style="{ display: 'none' }"
                  :preview="{ visible, onVisibleChange: setVisible }"
                  :src="discriminateSrc"
                  :previewMask="false"
                />
                <a-button @click="returnBack">还原比例</a-button>
                <a-button @click="clear">清空画布</a-button>
                <a-button :disabled="!isMainSence" @click="syncSence">参数同步↓</a-button>
                <a-button @click="handleDiscriminate" :disabled="isLoadOver">效果预览</a-button>
                <a-dropdown :disabled="isLoadOver">
                  <template #overlay>
                    <a-menu @click="handleSave">
                      <a-menu-item key="1">
                        <UserOutlined />
                        仅应用
                      </a-menu-item>
                      <a-menu-item key="2">
                        <UserOutlined />
                        应用并关闭
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a-button type="primary">
                    应用
                    <DownOutlined />
                  </a-button>
                </a-dropdown>
              </a-space>
            </div>
          </div>
        </a-layout-header>
        <a-layout>
          <a-layout-sider theme="light" class="pictureSlider">
            <pictureSlider @imgListLoadFinish="imgListLoadFinish"></pictureSlider>
          </a-layout-sider>
          <a-layout-content>
            <div id="workspace">
              <canvas id="canvas"></canvas>
            </div>
            <div class="log-panel-container">
              <div class="logTitle">执行日志</div>
              <div class="log-panel" id="log">
                <div v-for="(item, index) in logList" :key="index" class="log-item">
                  <IconFont
                    :type="getMsgType(item)"
                    :class="getMsgType(item)"
                    class="iconfont"
                  ></IconFont>

                  <span>{{ item.timestamp }}-{{ item.msg }}</span>
                </div>
              </div>
            </div>
          </a-layout-content>
          <a-layout-sider theme="light" width="320" class="paramSlider">
            <paramSlider @isMain="isMain"></paramSlider>
          </a-layout-sider>
        </a-layout>
      </a-layout>
    </a-spin>
  </div>
</template>
<script lang="ts">
import { defineComponent, onMounted, provide, ref, h, onBeforeUnmount, watch, computed } from "vue";
import { useRoute } from "vue-router";
import { DownOutlined } from "@ant-design/icons-vue";
import { fabric } from "fabric";
import { useStore } from "vuex";
import {
  algorithmDiscriminate,
  applyAlgorithm,
  channelScreenshot,
  getAlgorithmConf,
  getAlgorithmPic,
  sync,
} from "@/api/design";
import Editor from "./core/core";
import { isEmpty, uuid2 } from "@/common/utils";
import pictureSlider from "./components/pictureSlider.vue";
import paramSlider from "./components/paramSlider.vue";
import { JsonParseFun, JsonStrFun } from "@/common/utils";
import { message, Modal } from "ant-design-vue";
import HWebsocket from "@/common/h-websocket";
import colorConvert from "color-convert";
import IconFont from "@/components/c-iconfont";
export default defineComponent({
  components: {
    pictureSlider,
    paramSlider,
    DownOutlined,
    IconFont,
  },
  setup(props, ctx) {
    const canvasEditor = new Editor();
    const route = useRoute();
    const store = useStore();
    const title = route.query.title;
    const presetName = route.query.presetName;
    const channelName = route.query.channelName;

    const alDes = ref("");

    provide("canvasEditor", canvasEditor);
    const getInstanceData = () => {
      getAlgorithmConf(route.query.algorithmInstanceId).then((res) => {
        if (res.code === 0) {
          const canvas = new fabric.Canvas("canvas", {
            // perPixelTargetFind:true,
            selectionBorderColor: "#164BFF",
            lockRotation: true,
          });
          canvasEditor.initFabric(canvas);
          // 获取算法描述
          alDes.value = res.data.algorithmDes;
          // 初始化背景
          const image = new Image();
          image.src = res.data.currentScene.configBenchPic;
          const VHoption = {
            width: 0,
            height: 0,
          };
          image.onload = () => {
            VHoption.width = image.width;
            VHoption.height = image.height;
            const Zoom = VHoption.width === 1920 && VHoption.height === 1080 ? 0.6 : 1;
            canvasEditor.initCanvasBg(canvas, res.data, VHoption, Zoom, store);
            // 回显图形

            const roiData = [
              {
                dataType: res.data.roiDrawType,
                value: typeof res.data.roi === "string" ? JsonParseFun(res.data.roi) : res.data.roi,
                key: "roi_cbb8c",
                constraints: {},
                name: "ROI",
              },
            ];
            if (roiData[0].value) {
              canvasEditor.echoGraphics.echoDraw(roiData);
            }
            canvasEditor.echoGraphics.echoDraw(res.data.paramList);
          };
          // 图片错误处理
          image.onerror = () => {
            message.error("该场景截图不可用，请检查场景", 2);
          };

          // 初始化数据
          canvasEditor.paramsData.setSubmitData(
            "algorithmInstanceId",
            route.query.algorithmInstanceId
          );
          canvasEditor.paramsData.setSubmitData("presetId", route.query.presetId);
          canvasEditor.paramsData.setSubmitData("algorithmCode", route.query.algorithmCode);
          canvasEditor.paramsData.setSubmitData("isMainSence", true);
          canvasEditor.paramsData.emit("instanceData", res.data);
          canvasEditor.paramsData.setData(res.data);
        } else {
          // router.push({ name: 'project_design' });
          // window.close();
        }
      });
    };

    // 资源是否加载完成
    const isLoadOver = ref(true);

    const imgListLoadFinish = (val) => {
      isLoadOver.value = val;
    };
    // 是否为主场景
    const isMainSence = ref(true);
    const isMain = (val) => {
      isMainSence.value = val;
    };
    const lookObj = () => {
      canvasEditor.checkObj();
    };
    const returnBack = () => {
      canvasEditor.backZoom();
    };
    // 清空画布
    const clear = () => {
      Modal.confirm({
        title: "提示",
        content: "确定清空画布吗?",
        onOk() {
          canvasEditor.clearCanvas();
        },
      });
    };

    /**
     * 同步场景
     */
    const syncSence = () => {
      console.log(canvasEditor.paramsData.editStatus, "asd");
      if (canvasEditor.paramsData.editStatus) {
        message.warn("同步前必须先应用修改项", 2);
        return;
      }
      const data = {
        algorithmInstanceId:
          canvasEditor.paramsData.getSomeParamsBySubmitData("algorithmInstanceId"),
        presetId: canvasEditor.paramsData.getSomeParamsBySubmitData("presetId"),
        fromSceneId: canvasEditor.paramsData.getSomeParamsBySubmitData("sceneId"),
      };
      sync(data).then((res) => {
        if (res.code === 0) {
          message.success("更新成功");
        }
      });
    };
    // 通知配置页面保存成功
    let bc = null;
    const postMessage = (algorithmInstanceId, presetId) => {
      bc = new BroadcastChannel("finish_configuration");
      const data = {
        algorithmInstanceId,
        presetId,
      };
      bc.postMessage(data);
    };
    // 校验是否所有参数都有值
    const validate = (submitData: Array<any>) => {
      const warn = {
        status: false,
        warnString: "",
      };
      const paramList = submitData.find((item) => item[0] === "paramList")[1];
      const roi = submitData.find((item) => item[0] === "roi")[1];
      if (roi.roiRequired && isEmpty(roi.coordinates)) {
        warn.status = true;
        warn.warnString = "ROI区域";
        return warn;
      }
      paramList.forEach((item) => {
        if (item.dataType === "array") {
          if (isEmpty(item.value)) {
            // if(item.value.length === 0){
            //   warn.status = true;
            //   warn.warnString = `${item.name}`
            //   return;
            // }
            warn.status = true;
            warn.warnString = `${item.name}`;
          } else {
            item.value.forEach((element) => {
              // !param.value 太绝对还是区分一下  param.value === false || param.value === 0     bool || number
              const findNull = element.params.find(
                (param) =>
                  !param.nullable &&
                  (param.value === null || param.value === undefined || param.value === "")
              );
              if (findNull) {
                warn.status = true;
                warn.warnString = `${item.label}【${element.pointName}】${findNull.label}`;
              }
            });
          }
        } else if (item.dataType === "RANGE" && item.value) {
          if (item.value.start > item.value.end || item.value.start === item.value.end) {
            warn.status = true;
            warn.warnString = `${item.label}的起始值不能大于或等于结束值`;
          }
        } else {
          if (isEmpty(item.value) && item.constraints.required) {
            warn.status = true;
            warn.warnString = `${item.label}未配置，请检查并完善`;
          }
        }
      });
      return warn;
    };
    // ---------------------------------------------------------------应用--------------------------------------------------------
    function getDataByKey(key: string) {
      const submitData = canvasEditor.paramsData.getAnyParamsBySubmitData() as Array<any>;
      if (key === "paramList") {
        submitData
          .find((item) => item[0] === key)[1]
          .forEach((item) => {
            if (item.dataType === "array") {
              if (item.value) {
                item.value.forEach((element) => {
                  element.params.forEach((conf) => {
                    if (conf.dataType === "RGB") {
                      if (conf.value) {
                        conf.value = colorConvert.hex.rgb(conf.value);
                      }
                    }
                  });
                });
              }
            }

            if (item.dataType === "RGB" && item.value) {
              if (item.value.includes("#")) {
                item.value = colorConvert.hex.rgb(item.value);
              }
            }

            if (item.dataType.includes("string")) {
              item.value = JsonParseFun(item.value);
            }
            if (item.dataType === "SELECTOR" && typeof item.value === "string" && item.value) {
              item.value = [item.value];
            }
          });
        return submitData.find((item) => item[0] === key)[1];
      }
      if (key === "roi") {
        return submitData.find((item) => item[0] === key)[1].coordinates;
      }
      if (submitData.find((item) => item[0] === key)) {
        return submitData.find((item) => item[0] === key)[1];
      } else {
        return false;
      }
    }
    const handleSave = async (key) => {
      const submitData = canvasEditor.paramsData.getAnyParamsBySubmitData() as Array<any>;

      const validator = validate(submitData);

      if (validator.status) {
        message.warn(`${validator.warnString}`, 3);
        return;
      }
      const applyData = {
        algorithmInstanceId: getDataByKey("algorithmInstanceId"),
        presetId: getDataByKey("presetId"),
        paramList: getDataByKey("paramList"),
        roi: getDataByKey("roi"),
        sceneId: getDataByKey("sceneId"),
        // subAlgorithmList: getDataByKey("SubalgorithmList"),
        algorithmId: route.query.algorithmId,
      };
      if (getDataByKey("isBatchProcessing")) {
        Object.assign(applyData, {
          cutPicInterval: getDataByKey("cutPicInterval"),
          cutPicSize: getDataByKey("cutPicSize"),
        });
      }

      const res = await applyAlgorithm(applyData);

      if (res.code !== 0) return;
      let timer = null;
      message.success("操作成功");
      postMessage(applyData.algorithmInstanceId, applyData.presetId);
      // 重置应用状态
      canvasEditor.paramsData.resettingEditStatus();
      if (key.key === "2") {
        timer = await setTimeout(() => {
          window.close();
        }, 1000);
      }
    };

    const view = () => {
      console.log(canvasEditor.paramsData.getAnyParamsBySubmitData());
    };
    //  -----------------------------------------------------------识别调试----------------------------------------------------
    const spinning = ref<boolean>(false);
    const visible = ref<boolean>(false);
    const setVisible = (value): void => {
      visible.value = value;
    };
    const discriminateSrc = ref<string>("");
    const handleDiscriminate = () => {
      const submitData = canvasEditor.paramsData.getAnyParamsBySubmitData() as Array<any>;
      // const subalgorithmKey =
      //   canvasEditor.paramsData.getSomeParamsBySubmitData("indexBySubalgorithm");
      const validator = validate(submitData);

      if (validator.status) {
        message.warn(`'${validator.warnString}'未配置，请检查并完善。`, 3);
        return;
      }
      // visible.value = true;

      clientKey.value = uuid2(32);
      const discriminateData = {
        algorithmInstanceId: getDataByKey("algorithmInstanceId"),
        sceneId: getDataByKey("sceneId"),
        images: getDataByKey("images"),
        paramList: getDataByKey("paramList"),
        roi: getDataByKey("roi"),
        clientKey: clientKey.value,
      };
      if (getDataByKey("isBatchProcessing")) {
        Object.assign(discriminateData, {
          cutPicInterval: getDataByKey("cutPicInterval"),
          cutPicSize: getDataByKey("cutPicSize"),
        });
      }
      spinning.value = true;
      closeWs().then(() => {
        openws();
      });

      algorithmDiscriminate(discriminateData).then((res) => {
        spinning.value = false;
        if (res.code === 0) {
          discriminateSrc.value = res.data.resultImgUrl;
          visible.value = true;
          spinning.value = false;
        }
      });
    };

    /**
     * 执行日志
     */
    const activeKey = ref("0");
    const logList = ref<any[]>([]);
    let ws = null;
    const clientKey = ref();

    // 获取日志类型
    const getMsgType = (item) => {
      if (item.level === "INFO") {
        return "icon-success-filling";
      }
      if (item.level === "SUCCESS") {
        return "icon-success-filling";
      }
      if (item.level === "WARN") {
        return "icon-warning-filling";
      }
      if (item.level === "ERROR") {
        return "icon-error-fill";
      }
    };
    const openws = () => {
      ws = new HWebsocket(
        `${(window as any).g.wsApi}/websocket/execute/algorithm/log/${clientKey.value}`,
        0
      );
      ws.onMessage = (evt) => {
        activeKey.value = "1";
        logList.value.push(JsonParseFun(evt.data));
        const dom = document.getElementById("log");
        dom.scrollTop += 155;
      };
    };

    const closeWs = () => {
      return new Promise<void>((resolve, reject) => {
        if (ws) {
          ws.close();
          ws = null;
        }
        logList.value = [];
        resolve();
      });
    };

    onMounted(() => {
      getInstanceData();
    });

    onBeforeUnmount(() => {
      if (bc) {
        bc.close();
      }
    });

    // getInstanceData();
    return {
      alDes,
      title,
      presetName,
      channelName,
      lookObj,
      returnBack,
      clear,
      handleSave,
      handleDiscriminate,
      spinning,
      visible,
      setVisible,
      discriminateSrc,
      imgListLoadFinish,
      isLoadOver,
      isMainSence,
      isMain,
      syncSence,
      view,
      logList,
      getMsgType,
      activeKey,
    };
  },
});
</script>
<style scoped lang="less">
@import "./style/index.less";
</style>
