<template>
  <div>
    <div v-if="!selectOneType">
      <div v-if="isBatchProcessing">
        <a-divider orientation="left">截图配置</a-divider>
        <a-form :model="picFrom" style="width: 50%; margin-left: 10px">
          <a-form-item label="截图间隔" name="cutPicInterval">
            <a-input-number
              v-model:value="picFrom.cutPicInterval"
              :min="1"
              :max="100"
              addon-after="秒"
              style="width: 100px"
              @change="setData('cutPicInterval')"
            />
          </a-form-item>
          <a-form-item label="截图张数" name="cutPicSize">
            <a-input-number
              v-model:value="picFrom.cutPicSize"
              :min="1"
              :max="10"
              style="width: 100px"
              @change="setData('cutPicSize')"
            />
          </a-form-item>
        </a-form>
      </div>

      <a-divider orientation="left">图像源</a-divider>
      <a-select
        v-model:value="imgSourceMode"
        style="width: 50%; margin-left: 10px"
        size="small"
        @change="getImageList"
      >
        <a-select-option value="SCHEDULE">周期截图</a-select-option>
        <a-select-option value="MANUAL">本地上传</a-select-option>
        <!-- <a-select-option value="TEMPLATE_FAILED">匹配失败</a-select-option> -->
      </a-select>
      <uploadFile
        :preset-id="activeNodeKey"
        @refreshImgList="getImageList"
        v-if="imgSourceMode === 'MANUAL'"
      ></uploadFile>
      <a-divider orientation="left">待选图像</a-divider>
      <a style="margin-left: 10px" @click="freshHistoryPic">刷新</a>
      <!-- <a style="margin-left: 10px;" v-if="hasActiveObject">aa</a> -->
      <div class="imageList">
        <a-image-preview-group>
          <div v-for="(item, index) in historyImgList" :key="index">
            <a-image
              :src="item"
              class="imageStyle"
              @click="changeSelectPic(item)"
              :preview="false"
              :class="{ selectedImageStyle: selectedPic.includes(item) }"
            ></a-image>
          </div>
        </a-image-preview-group>
      </div>
    </div>
    <div v-else>
      <objectConfig></objectConfig>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, watch, computed, defineEmits } from "vue";
import { useRoute } from "vue-router";
import { useStore } from "vuex";
import uploadFile from "@/components/uploadFile.vue";
import {
  algorithmDiscriminate,
  applyAlgorithm,
  channelScreenshot,
  getAlgorithmConf,
  getAlgorithmPic,
  sync,
} from "@/api/design";
import { message } from "ant-design-vue";
import emitCanvas from "@/hooks/emitCanvas";
import objectConfig from "./objectConfig.vue";

export default defineComponent({
  components: {
    uploadFile,
    objectConfig,
  },
  props: {
    isHasActive: {
      default: null,
    },
  },
  setup(props, ctx) {
    const activeNodeKey = useRoute().query.presetId as string;
    const channelId = useRoute().query.channelId as string;
    const { canvasEditor } = emitCanvas() as any;
    const store = useStore();

    // -----------------------------------截图配置----------------------------------------

    const picFrom = ref({
      cutPicInterval: 1,
      cutPicSize: 1,
    });
    const isBatchProcessing = ref(false); // 是否存在截图配置

    /**
     * 更新上传数据
     * @param key 参数的标识key
     */
    const setData = (key) => {
      canvasEditor.paramsData.setSubmitData(key, picFrom.value[key]);
      canvasEditor.paramsData.editChangeStatus();
    };

    // ----------------------------------------图像源选择-----------------------------------
    const imgSourceMode = ref<string>("SCHEDULE");

    const historyImgList = ref<string[]>([]); // 图像列表
    const selectedPic = ref<string[]>([]);
    // 获取图像列表
    const getImageList = () => {
      getAlgorithmPic(activeNodeKey, imgSourceMode.value).then((res) => {
        if (res.code === 0 && res.data !== null) {
          historyImgList.value = res.data.pics;
          selectedPic.value = [res.data.pics[0]];

          canvasEditor.paramsData.setSubmitData("images", historyImgList.value);
          ctx.emit("imgListLoadFinish", false);
        }
      });
    };
    // 选择图片
    const changeSelectPic = (src) => {
      if (!isBatchProcessing) {
        if (selectedPic.value.includes(src)) {
          selectedPic.value.splice(selectedPic.value.indexOf(src), 1);
          canvasEditor.paramsData.setSubmitData("images", selectedPic.value);
        } else {
          selectedPic.value.push(src);
          canvasEditor.paramsData.setSubmitData("images", selectedPic.value);
        }
      } else {
        if (selectedPic.value.includes(src)) return;
        selectedPic.value = [src];
        canvasEditor.paramsData.setSubmitData("images", selectedPic.value);
      }
    };
    // 刷新图片
    const freshHistoryPic = () => {
      channelScreenshot(channelId, false).then((res) => {
        if (res.code === 0) {
          if (selectedPic.value.includes(historyImgList.value[0])) {
            selectedPic.value[selectedPic.value.indexOf(historyImgList.value[0])] = res.data;
          }
          historyImgList.value[0] = res.data;

          // selectedPic.value.push(res.data);
          canvasEditor.paramsData.setSubmitData("images", selectedPic.value);
          message.success("更新成功");
        }
      });
    };

    // 监听选择的图形
    const selectOneType = ref<any>(null);
    watch(
      () => store.state.design.selectOneByObj,
      (nv) => {
        // console.log(nv,'vuex');
        selectOneType.value = nv;
      }
    );

    onMounted(() => {
      getImageList();
      canvasEditor.paramsData.on("instanceData", (data) => {
        isBatchProcessing.value = data.isBatchProcessing;
        if (data.isBatchProcessing) {
          picFrom.value.cutPicInterval = data.cutPicInterval;
          picFrom.value.cutPicSize = data.cutPicSize;
          canvasEditor.paramsData.setSubmitData("isBatchProcessing", data.isBatchProcessing);
          canvasEditor.paramsData.setSubmitData("cutPicInterval", picFrom.value.cutPicInterval);
          canvasEditor.paramsData.setSubmitData("cutPicSize", picFrom.value.cutPicSize);
        }
      });
    });

    return {
      activeNodeKey,
      imgSourceMode,
      getImageList,
      historyImgList,
      selectedPic,
      changeSelectPic,
      freshHistoryPic,
      selectOneType,
      picFrom,
      isBatchProcessing,
      setData,
    };
  },
});
</script>

<style scoped>
.imageList {
  overflow-y: auto;
  max-height: 735px;
}
.imageList::-webkit-scrollbar {
  width: 5px;
}
.imageList::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.2);
}
.imageList::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}

:deep(.imageStyle) {
  width: 192px !important;
  height: 108px !important;
  cursor: pointer !important;
  border: 1px solid rgba(255, 255, 255, 0) !important;
}

:deep(.selectedImageStyle) {
  border: 3px solid red !important;
}
</style>
