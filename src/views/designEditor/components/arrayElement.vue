<template>
  <div style="border: 1px solid #d9d9d9; padding: 10px 0px">
    <span style="margin-left: 10px; font-weight: 600">{{ arrayAllData.name }}</span>
    <a-button
      @click="addArrayItem"
      style="float: right; margin-right: 10px"
      size="small"
      type="primary"
      :disabled="!isMainSence"
    >
      <template #icon><plus-outlined /></template>
      添加
    </a-button>
    <a-collapse v-model:activeKey="currentArrayItemId" accordion ghost style="margin-top: 5px">
      <a-collapse-panel v-for="(item, index) in arrayItemList" :key="item.id">
        <template #header>
          <a-tooltip placement="topLeft">
            <template #title>
              {{ item.pointName }}
            </template>
            <span class="titleName">{{ item.pointName }}</span>
          </a-tooltip>
        </template>
        <template #extra
          ><a-button
            size="small"
            danger
            type="primary"
            @click.stop="deleteArrayItem(item.id)"
            :disabled="!isMainSence"
            >删除</a-button
          ></template
        >
        <div v-for="(element, i) in item.params" :key="i" style="margin-bottom: 10px">
          <!-- type为绘制图形 -->
          <a-button
            v-if="graphicTypeList.includes(element.type)"
            style="width: 100%"
            @click="drawParamItem(element, item.id, item.pointName)"
          >
            <template #icon>
              <IconFont :type="getType(element.type)"></IconFont>
            </template>
            {{ element.name }}
          </a-button>
          <!-- type为取色器 -->
          <div
            v-if="element.type === 'color_picker'"
            style="border: 1px solid #d9d9d9; padding: 5px 10px"
          >
            <span style="margin-right: 10px">{{ element.name }}</span>
            <input
              type="color"
              v-model="element.value"
              style="width: 50%"
              @change="setData(element.type, item.id, element.key)"
            />
          </div>
          <!-- type 为量程 -->
          <div v-if="element.type === 'range'" style="border: 1px solid #d9d9d9; padding: 5px 10px">
            <span style="margin-right: 10px">{{ element.name }}</span>
            <a-input-number
              v-model:value="element.value[0]"
              :min="-99999999"
              :max="element.value[1]"
              :controls="false"
              style="width: 35%"
              size="small"
              @change="setData(element.type, item.id, element.key)"
            ></a-input-number
            >~<a-input-number
              v-model:value="element.value[1]"
              :min="element.value[0]"
              :max="999999999"
              :controls="false"
              style="width: 35%"
              size="small"
              @change="setData(element.type, item.id, element.key)"
            ></a-input-number>
          </div>
          <!-- type 为选择器 -->
          <div
            v-if="element.type === 'select'"
            style="border: 1px solid #d9d9d9; padding: 5px 10px"
          >
            <span style="margin-right: 10px">{{ element.name }}</span>
            <a-select
              v-model:value="element.value"
              :placeholder="element.name"
              size="small"
              style="width: 50%"
              :options="element.options"
              @change="setData(element.type, item.id, element.key)"
            ></a-select>
          </div>
          <!-- type 为字符串 -->
          <div
            v-if="element.type === 'string'"
            style="border: 1px solid #d9d9d9; padding: 5px 10px"
          >
            <span style="margin-right: 10px">{{ element.name }}</span>
            <a-input
              v-model:value="element.value"
              @change="setData(element.type, item.id, element.key)"
            ></a-input>
          </div>
          <!-- type 为数字 -->
          <div
            v-if="element.type === 'number'"
            style="border: 1px solid #d9d9d9; padding: 5px 10px; display: flex; align-items: center"
          >
            <span style="margin-right: 10px">{{ element.name }}</span>
            <div style="width: 45%">
              <a-slider
                v-model:value="element.value"
                :min="element.valRange[0]"
                :max="element.valRange[1]"
                :step="element.valRange[2]"
                :defaultValue="element.valRange[3]"
                @change="setData(element.type, item.id, element.key)"
              />
            </div>
            <a-input-number
              v-model:value="element.value"
              :min="element.valRange[0]"
              :max="element.valRange[1]"
              :step="element.valRange[2]"
              :controls="false"
              size="small"
              style="width: 20%"
              @change="setData(element.type, item.id, element.key)"
            />
          </div>
        </div>
      </a-collapse-panel>
    </a-collapse>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, onMounted, nextTick, watch, computed, onUpdated } from "vue";
import { useRoute } from "vue-router";
import IconFont from "@/components/c-iconfont";
import { PlusOutlined } from "@ant-design/icons-vue";
import { uuid2 } from "@/common/utils";
import { JsonStrFun, JsonParseFun } from "@/common/utils";
import { graphicTypeList, getType } from "@/models/design";
import emitCanvas from "@/hooks/emitCanvas";
import { Modal } from "ant-design-vue";

export default defineComponent({
  props: {
    // array类型的数据
    arrayAllData: {
      default: null,
    },
    isMainSence: {
      default: true,
    },
  },
  components: {
    IconFont,
    PlusOutlined,
  },
  setup(props, { emit }) {
    const { canvasEditor } = emitCanvas() as any;
    const arrayAllData = ref(props.arrayAllData) as any;

    const route = useRoute();

    const presetName = route.query.presetName;
    const algorithmName = route.query.title;

    const currentArrayItemId = ref<string>(""); // 当前选中折叠ID
    // 初始化列表
    const initArrayItemList = (value) => {
      if (value === null || !value.length) return [];
      currentArrayItemId.value = value[0].id;
      return value.map((item) => {
        return {
          id: item.id,
          name: arrayAllData.value.name,
          params: item.params,
          pointName: item.pointName,
        };
      });
    };
    const arrayItemList = ref<any[]>([]); // array实例的列表
    // 定义一个函数，用于生成指定范围内的随机整数
    function generateSecureRandomNumber(digits) {
      const array = new Uint32Array(1);
      window.crypto.getRandomValues(array);
      const max = Math.pow(10, digits) - 1; // 最大的5位数是99999
      const min = Math.pow(10, digits - 1); // 最小的5位数是10000
      const randomNum = Math.floor((array[0] / (0xffffffff + 1)) * (max - min + 1)) + min;
      return randomNum;
    }

    // 添加一个array实例
    const addArrayItem = () => {
      const ArrayItemId: string = uuid2(32);
      currentArrayItemId.value = ArrayItemId;
      const arrayTemplate: any = JsonParseFun(JsonStrFun(arrayAllData.value.valRange));

      const pointName = `${generateSecureRandomNumber(5)}-${presetName}-${algorithmName}`;
      arrayItemList.value.push({
        id: ArrayItemId,
        params: arrayTemplate,
        name: arrayAllData.value.name,
        pointName: pointName,
      });
      // 将数据写入submitData
      const submitDataParamList = canvasEditor.paramsData.getSomeParamsBySubmitData(
        "paramList"
      ) as any[];
      submitDataParamList.forEach((item, index) => {
        if (item.key === arrayAllData.value.key) {
          if (item.value === null) {
            item.value = [
              { id: ArrayItemId, params: arrayTemplate, name: arrayAllData.value.name, pointName },
            ];
          } else {
            item.value.push({
              id: ArrayItemId,
              params: arrayTemplate,
              name: arrayAllData.value.name,
              pointName,
            });
          }
        }
      });
      canvasEditor.paramsData.setSubmitData("paramList", submitDataParamList);
      canvasEditor.paramsData.editChangeStatus();
    };
    // 删除实例
    const deleteArrayItem = (id) => {
      Modal.confirm({
        title: "删除",
        content: "确定删除该点吗？",
        onOk() {
          arrayItemList.value = arrayItemList.value.filter((item) => item.id !== id);
          canvasEditor.deleteCanvasByArray(id);

          const submitDataParamList = canvasEditor.paramsData.getSomeParamsBySubmitData(
            "paramList"
          ) as any[];

          submitDataParamList.forEach((item, index) => {
            if (item.key === arrayAllData.value.key) {
              const filterDel = item.value.filter((item) => item.id !== id);
              item.value = filterDel.length < 1 ? null : filterDel;
            }
          });
          canvasEditor.paramsData.setSubmitData("paramList", submitDataParamList);
          canvasEditor.paramsData.editChangeStatus();
        },
      });
    };
    /**
     * 绘制图形
     * @param param 绘制参数
     */
    const drawParamItem = (element, id, pointName) => {
      if (!canvasEditor.checkObjectById(`${id}_${element.key}`)) {
        const params = {
          isDrawMode: true,
          drawType: element.type,
          id: `${id}_${element.key}`,
          paramName: `${id.slice(0, 17)}_${element.name}`,
        };
        // paramsData注入相应的id
        const submitDataParamList = canvasEditor.paramsData.getSomeParamsBySubmitData(
          "paramList"
        ) as any[];
        submitDataParamList.forEach((item) => {
          if (item.key === arrayAllData.value.key) {
            item.value.forEach((val) => {
              if (val.id === id) {
                val.params.forEach((param) => {
                  if (`${val.id}_${param.key}` === params.id) {
                    param.id = params.id;
                  }
                });
              }
            });
          }
        });
        canvasEditor.paramsData.setSubmitData("paramList", submitDataParamList);
        canvasEditor.setModeAndType(params);
        canvasEditor.paramsData.editChangeStatus();
      }
    };

    // setData
    const setData = (type = null, id = null, key) => {
      const submitDataParamList = canvasEditor.paramsData.getSomeParamsBySubmitData(
        "paramList"
      ) as any[];
      let configValue = null;
      if (id) {
        const findArrayItem = arrayItemList.value.find((item) => item.id === id);
        findArrayItem.params.forEach((item) => {
          if (item.type === type && item.key === key) {
            configValue = item.value;
          }
        });
      }
      if (type) {
        submitDataParamList.forEach((item, index) => {
          if (item.key === arrayAllData.value.key) {
            if (item.value) {
              item.value.forEach((element) => {
                if (element.params && element.id === id) {
                  element.params.forEach((conf) => {
                    if (conf.type === type && conf.key === key) {
                      conf.value = configValue;
                    }
                  });
                }
              });
            }
          }
        });
        canvasEditor.paramsData.setSubmitData("paramList", submitDataParamList);
        canvasEditor.paramsData.editChangeStatus();
      }
    };

    // watch(() => props.arrayAllData, () => {
    //   console.log(props.arrayAllData,'wwwww')
    //   arrayItemList.value = initArrayItemList((props.arrayAllData as any).value);
    // }, { deep: true })

    onMounted(() => {
      console.log(props.arrayAllData);
      arrayItemList.value = initArrayItemList((props.arrayAllData as any).value);
      // console.log(arrayItemList.value,'onmunted')
    });

    return {
      currentArrayItemId,
      arrayItemList,
      addArrayItem,
      deleteArrayItem,
      arrayAllData,
      graphicTypeList,
      getType,
      drawParamItem,
      setData,
    };
  },
});
</script>

<style scoped>
.titleName {
  display: inline-block;
  width: 130px;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
  vertical-align: top;
}
</style>
