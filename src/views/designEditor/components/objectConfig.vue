<template>
  <div>
    <a-divider orientation="left">位置信息</a-divider>
    <div style="padding: 0px 10px">
      <div>
        <span style="margin-right: 10px;">X:</span>
        <a-input-number style="width: 80%;" v-model:value="leftObj" :precision="2" @change="changeInfoByCanvasObj('left')"></a-input-number>
      </div>
      <div style="margin-top: 10px;">
        <span style="margin-right: 10px;">Y:</span>
        <a-input-number style="width: 80%;" v-model:value="topObj" :precision="2" @change="changeInfoByCanvasObj('top')"></a-input-number>
      </div>
      
    </div>

    <a-divider orientation="left">标识符</a-divider>
    <div style="padding: 0px 10px">
      <a-textarea style="width: 100%;" readonly v-model:value="IdentifierByObj" :autoSize="true"></a-textarea>
    </div>
      
    <a-divider orientation="left">颜色</a-divider>
    <div style="padding: 0px 10px">
      <a-select v-model:value="colorByObj" style="width: 90%;" @change="changeInfoByCanvasObj('color')">
        <a-select-option value="#FF0000">红色</a-select-option>
        <a-select-option value="#0000FF">蓝色</a-select-option>
        <a-select-option value="#00FF00">绿色</a-select-option>
        <a-select-option value="#FFFFFF">白色</a-select-option>
        <a-select-option value="#000000">黑色</a-select-option>
      </a-select>
    </div>
      

  </div>
  
</template>

<script lang="ts">
import { defineComponent, onMounted, ref, watch, computed } from 'vue';
import { useStore } from 'vuex';
import emitCanvas from '@/hooks/emitCanvas';

export default defineComponent({
  setup(props, ctx) {
    const store = useStore();
    const { canvasEditor } = emitCanvas();
    // const objInfo = computed(()=>store.state.design.selectOneByObj).value
    const IdentifierByObj = ref<string>();
    const colorByObj = ref<string>();
    const leftObj = ref<string>();
    const topObj = ref<string>();

    const getInfoByCanvasObj = ()=>{
      const obj = canvasEditor.canvas.getActiveObjects()[0];
      if(!obj) return;
      leftObj.value = obj.get('left');
      topObj.value = obj.get('top');
      if(obj.type === 'point'){
        colorByObj.value = obj.get('fill')
      }else{
        colorByObj.value = obj.get('stroke')
      }
      IdentifierByObj.value = obj.get('name')
    }


    const changeInfoByCanvasObj = (type)=>{
      const obj = canvasEditor.canvas.getActiveObjects()[0];
      if(!obj) return;
      if(type === 'left'){
        obj.set('left', leftObj.value);
      }
      if(type === 'top'){
        obj.set('top', topObj.value);
      }
      if(type === 'color'){
        if(obj.type === 'point'){
          obj.set('fill',colorByObj.value)
        }else if(obj.type === 'line'){
          obj.set('fill',colorByObj.value);
          obj.set('stroke',colorByObj.value)
        }else{
          obj.set('stroke',colorByObj.value)
        }
      }
      canvasEditor.canvas.renderAll();
    }

    watch(()=>store.state.design.selectOneByObj,(nv)=>{
      // console.log(nv,'vuex');
      console.log(nv,'nvnvn')
      if(nv){
        getInfoByCanvasObj();
      }
      
    },{deep:true})

    

    onMounted(()=>{
      
      getInfoByCanvasObj();
    })

    return{
      IdentifierByObj,
      colorByObj,
      leftObj,
      topObj,
      changeInfoByCanvasObj
    }
  },
})
</script>

<style scoped>


</style>