<template>
  <div class="confTitle">
    算法配置
    <IconFont type="icon-bangzhu" class="bangzhu"></IconFont>
  </div>
  <a-tabs
    v-model:activeKey="currentSceneId"
    size="small"
    style="height: calc(100vh - 64px)"
    @change="changeSence"
  >
    <a-tab-pane v-for="pane in sceneList" :key="pane.id">
      <template #tab>
        <a-tooltip :title="pane.name" placement="left">
          {{ pane.name.length > 4 ? pane.name.slice(0, 4) + "..." : pane.name }}
        </a-tooltip>
      </template>
      <div class="tabBox">
        <a-divider orientation="left">基准图</a-divider>
        <a-image :src="currentScenePic" class="referenceImage" :preview="false"></a-image>
        <a-divider orientation="left" v-if="roi.roiRequired">ROI区域</a-divider>
        <div
          v-if="graphicTypeList.includes(roi.dataType) && roi.roiRequired"
          :class="{ 'paramItem-required': roi.roiRequired }"
        >
          <a-button style="width: 90%" @click="drawParamItem(roi)">
            <template #icon><IconFont :type="getType(roi.dataType)"></IconFont></template>
            绘制区域
          </a-button>
        </div>

        <a-divider orientation="left">参数配置</a-divider>
        <div v-for="(item, index) in paramList" :key="index" style="margin-bottom: 10px">
          <!-- array类型 -->
          <arrayElement
            v-if="item.dataType === 'array'"
            :array-all-data="item"
            :is-main-sence="isMainSence"
            ref="arrayRef"
          ></arrayElement>
          <!-- STRING类型 -->
          <div
            v-if="item.dataType === 'STRING'"
            class="paramItem"
            :class="{ 'paramItem-required': item.constraints.required }"
          >
            <a-tooltip>
              <template #title>{{ item.label }}</template>
              <span style="margin-right: 8px">{{
                item.label.length > 10 ? item.label.slice(0, 10) + "..." : item.label
              }}</span>
            </a-tooltip>
            <a-input
              v-model:value="item.value"
              @change="setData(item.key)"
              style="width: 80%"
              :maxLength="stringLength(item.key)"
            ></a-input>
          </div>

          <!-- 图形类型  -->
          <div
            v-if="graphicTypeList.includes(item.dataType)"
            :class="{ 'paramItem-required': item.constraints.required }"
          >
            <a-button style="width: 90%" @click="drawParamItem(item)">
              <template #icon
                ><IconFont
                  :type="
                    getType(item.dataType, item.constraints.direct ? item.constraints.direct : null)
                  "
                ></IconFont
              ></template>
              {{ item.label }}
            </a-button>
          </div>

          <!-- Boolean类型 -->
          <div
            v-if="item.dataType === 'BOOLEAN'"
            class="paramItem"
            :class="{ 'paramItem-required': item.constraints.required }"
          >
            <a-tooltip>
              <template #title>{{ item.label }}</template>
              <span style="margin-right: 8px">{{
                item.label.length > 10 ? item.label.slice(0, 10) + "..." : item.label
              }}</span>
            </a-tooltip>
            <a-switch v-model:checked="item.value" @change="setData(item.key)"></a-switch>
          </div>
          <!-- INTEGER/FLOAT类型 -->
          <div
            v-if="item.dataType === 'INTEGER' || item.dataType === 'FLOAT'"
            style="display: flex; align-items: center"
            class="paramItem"
            :class="{ 'paramItem-required': item.constraints.required }"
          >
            <a-tooltip>
              <template #title>{{ item.label }}</template>
              <span>{{ item.label.length > 5 ? item.label.slice(0, 5) + "..." : item.label }}</span>
            </a-tooltip>
            <div style="width: 100px; margin-left: 5px">
              <a-slider
                v-model:value="item.value"
                :min="item.constraints.min"
                :max="item.constraints.max"
                :step="item.constraints.precision"
                @change="setData(item.key)"
              />
            </div>
            <a-input-number
              v-model:value="item.value"
              :min="item.constraints.min"
              :max="item.constraints.max"
              :precision="getPrecision(item.constraints.precision)"
              :controls="false"
              size="small"
              style="width: 50px"
              @change="setData(item.key)"
            />
          </div>
          <!-- 多选select类型 -->
          <div
            v-if="item.dataType === 'SELECTOR'"
            class="paramItem"
            :class="{ 'paramItem-required': item.constraints.required }"
          >
            <a-tooltip>
              <template #title>{{ item.label }}</template>
              <span style="margin-right: 8px">{{
                item.label.length > 5 ? item.label.slice(0, 5) + "..." : item.label
              }}</span>
            </a-tooltip>
            <a-select
              v-model:value="item.value"
              :mode="item.constraints.maxLength > 1 ? 'multiple' : 'default'"
              :max-tag-count="3"
              :options="item.constraints.options"
              size="small"
              style="width: 60%"
              @change="setData(item.key)"
              :fieldNames="{ label: 'label', value: 'key' }"
            ></a-select>
          </div>
          <!-- range类型 -->
          <div
            v-if="item.dataType === 'RANGE'"
            class="paramItem"
            :class="{ 'paramItem-required': item.constraints.required }"
          >
            <a-tooltip>
              <template #title>{{ item.label }}</template>
              <span style="margin-right: 8px">{{
                item.label.length > 5 ? item.label.slice(0, 5) + "..." : item.label
              }}</span>
            </a-tooltip>
            <a-input-number
              v-model:value="item.value.start"
              :min="item.constraints.min"
              :max="item.constraints.max"
              :precision="getPrecision(item.constraints.precision)"
              :controls="false"
              style="width: 35%"
              size="small"
              @change="setData(item.key)"
            ></a-input-number
            >~<a-input-number
              v-model:value="item.value.end"
              :min="item.constraints.min"
              :max="item.constraints.max"
              :precision="getPrecision(item.constraints.precision)"
              :controls="false"
              style="width: 35%"
              size="small"
              @change="setData(item.key)"
            ></a-input-number>
          </div>
          <!-- 取色笔类型 -->
          <div
            v-if="item.dataType === 'RGB'"
            class="paramItem"
            :class="{ 'paramItem-required': item.constraints.required }"
          >
            <a-tooltip>
              <template #title>{{ item.label }}</template>
              <span style="margin-right: 8px">{{
                item.label.length > 5 ? item.label.slice(0, 5) + "..." : item.label
              }}</span>
              <!-- <span style="margin-right: 8px">oakjsdas</span> -->
            </a-tooltip>
            <!-- <a-input v-model:value="item.value" style="width: 50%" /> -->

            <ElColorPicker
              v-model="item.value"
              color-format="hex"
              @change="setData(item.key)"
            ></ElColorPicker>

            <a-button @click="colorPicker(item)" style="margin-left: 10px">
              <IconFont type="icon-quseqi" class="colorPicker" />
              取色器</a-button
            >
          </div>
        </div>
      </div>
    </a-tab-pane>
  </a-tabs>

  <!-- <a-button @click="test">矩形</a-button> -->
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, onBeforeUnmount } from "vue";
import { graphicTypeList, getType } from "@/models/design";
import IconFont from "@/components/c-iconfont";
import emitCanvas from "@/hooks/emitCanvas";
import arrayElement from "./arrayElement.vue";
import { JsonParseFun, JsonStrFun, getPrecision, isEmpty } from "@/common/utils";
import colorConvert from "color-convert";
import { getAlgorithmConf } from "@/api/design";
import { ElInputNumber } from "element-plus";
import { message } from "ant-design-vue";
import { useStore } from "vuex";
import uEmitter from "@/common/u-event-bus";
import { ElColorPicker } from "element-plus";

interface RoiType {
  dataType: string;
  key: string;
  label: string;
  coordinates: any;
  roiRequired: boolean;
  constraints: any;
}
export default defineComponent({
  components: {
    arrayElement,
    IconFont,
    ElInputNumber,
    ElColorPicker,
  },
  setup(props, { emit }) {
    const { canvasEditor } = emitCanvas() as any;
    const currentSceneId = ref<string>(""); // 当前场景Id
    const currentScenePic = ref<string>(""); // 当前场景基准图
    const sceneList = ref<any[]>([]); // 场景列表
    const paramList = ref<any[]>([]); // 参数列表
    const indexBySubalgorithm = ref<number | string>(0);
    // const subalgorithmList = ref<any[]>([]); // 子算法集合

    // const strategies = ref<any[]>([]); // 子算法中的策略选项集合
    const roi = ref<RoiType | null>(null);
    const isMainSence = ref<boolean>(true);
    const arrayRef = ref(null);
    const store = useStore();

    /**
     * 初始化参数数据
     * @param data bus传上来的数据
     */
    // string类型长度限制
    const stringLength = (key) => {
      if (key === "resultDes") {
        return 20;
      } else if (key === "unit") {
        return 10;
      } else {
        return "";
      }
    };
    const initParams = (data) => {
      // instanceData.value = data;
      sceneList.value = data.sceneList;
      currentSceneId.value = data.currentScene.id;
      currentScenePic.value = data.currentScene.benchPic;
      paramList.value = JsonParseFun(JsonStrFun(data.paramList));
      paramList.value.forEach((item) => {
        if (item.dataType === "RANGE") {
          if (item.value === null) {
            item.value = {
              start: null,
              end: null,
            };
            item.constraints.max = !isEmpty(item.constraints.max)
              ? Number(item.constraints.max)
              : 99999999;
            item.constraints.min = !isEmpty(item.constraints.min)
              ? Number(item.constraints.min)
              : -99999999;
            item.constraints.precision = isEmpty(item.constraints.precision)
              ? item.dataType === "FLOAT"
                ? 0.01
                : 0
              : Number(item.constraints.precision);
          }
          if (item.value && typeof item.value === "string") {
            item.value = JsonParseFun(item.value);
          }
          item.constraints.max = Number(item.constraints.max);
          item.constraints.min = Number(item.constraints.min);
          item.constraints.precision = Number(item.constraints.precision);
        }
        if (item.dataType === "SELECTOR") {
          if (typeof item.constraints.options === "string") {
            item.constraints.options = JsonParseFun(item.constraints.options);
          }

          if (item.value === null) {
            item.value = [];
          }
        }
        if (item.dataType === "FLOAT" || item.dataType === "INTEGER") {
          if (item.value && typeof item.value === "string") {
            item.value = Number(item.value);
          }
          item.constraints.max = !isEmpty(item.constraints.max)
            ? Number(item.constraints.max)
            : 99999999;
          item.constraints.min = !isEmpty(item.constraints.min)
            ? Number(item.constraints.min)
            : -99999999;
          item.constraints.precision = isEmpty(item.constraints.precision)
            ? item.dataType === "FLOAT"
              ? 0.01
              : 0
            : Number(item.constraints.precision);
        }
        if (item.dataType === "RGB") {
          if (item.value) {
            item.value = `#${colorConvert.rgb.hex(item.value)}`;
          }
        }
        if (item.dataType === "BOOLEAN") {
          if (item.value && typeof item.value === "string") {
            item.value = item.value.toLowerCase() === "true";
          }
        }

        if (item.type === "array") {
          if (typeof item.value === "string") {
            // console.log('item======>');
            item.value = JsonParseFun(item.value);
            // console.log('asdasdas======>');
            item.value.forEach((element) => {
              element.params.forEach((conf) => {
                if (conf.type === "color_picker") {
                  if (conf.value) {
                    conf.value = `#${colorConvert.rgb.hex(conf.value)}`;
                  }
                }
              });
            });
          }
          item.valRange.forEach((element) => {
            if (element.type === "select") {
              element.options = Object.keys(element.valRange).map((items, indexs) => ({
                value: items,
                label: element.valRange[items],
              }));
            }
            if (element.type === "range") {
              if (element.value === null && element.valRange !== null) {
                element.value = [element.valRange[0], element.valRange[1]];
                // item.valRange[2] = String(item.valRange[2]).split('.')[1].length;
              }
              // if(element.value && typeof element.value === 'string'){
              //   element.value = JsonParseFun(element.value)
              // }
            }
            if (element.type === "number") {
              if (!Object.keys(element).includes("value")) {
                element.value = element.valRange[3];
              }
              if (element.value === null && element.valRange !== null) {
                element.value = element.valRange[3];
              }
              if (element.value && typeof element.value) {
                element.value = JsonParseFun(element.value);
              }
              // if(element.value && typeof element.value === 'string'){
              //   element.value = JsonParseFun(element.value)
              // }
            }
            // if(element.type === 'color_picker'){
            //   if(element.value){
            //     if(typeof element.value === 'string'){
            //       element.value = `#${colorConvert.rgb.hex(JsonParseFun(element.value))}`
            //     }else{
            //       element.value = `#${colorConvert.rgb.hex(element.value)}`
            //     }

            //   }

            // }
          });
        }
        if (item.value && graphicTypeList.includes(item.type)) {
          item.value = JsonParseFun(item.value);
        }
      });
      roi.value = {
        dataType: data.roiDrawType,
        key: "roi_cbb8c",
        label: "RoI",
        constraints: {},
        coordinates: data.roi,
        roiRequired: data.roiRequired,
      };
      canvasEditor.paramsData.setSubmitData("paramList", paramList.value);
      canvasEditor.paramsData.setSubmitData("roi", roi.value);
      canvasEditor.paramsData.setSubmitData("sceneId", currentSceneId.value);
      canvasEditor.paramsData.setSubmitData("indexBySubalgorithm", indexBySubalgorithm.value);
      // canvasEditor.paramsData.setSubmitData("SubalgorithmList", subalgorithmList.value);
      canvasEditor.paramsData.setSubmitData(
        "isMainSence",
        sceneList.value.findIndex((item) => item.id === currentSceneId.value) === 0
      );
      isMainSence.value =
        sceneList.value.findIndex((item) => item.id === currentSceneId.value) === 0;
      emit("isMain", sceneList.value.findIndex((item) => item.id === currentSceneId.value) === 0);
    };
    /**
     * 更新上传数据
     * @param key 参数的标识key
     */
    const setData = (key) => {
      const submitDataParamList = canvasEditor.paramsData.getSomeParamsBySubmitData(
        "paramList"
      ) as any[];
      submitDataParamList.forEach((item, index) => {
        if (item.key === key) {
          item.value = paramList.value.find((element) => element.key === key).value;
        }
      });
      canvasEditor.paramsData.setSubmitData("paramList", submitDataParamList);
      canvasEditor.paramsData.editChangeStatus();
    };

    /**
     * 绘制图形
     * @param item 绘制图形参数
     */
    const drawParamItem = (item) => {
      console.log(item);
      if (!canvasEditor.checkObjectById(item.key)) {
        const params = {
          isDrawMode: true,
          drawType: item.dataType,
          id: item.key,
          paramName: item.label,
          LineMode: item.constraints.direct ? item.constraints.direct : null,
        };
        console.log(params, "params");
        canvasEditor.setModeAndType(params);
      }
    };

    /**
     * 切换场景
     */
    const changeSence = (val) => {
      const algorithmInstanceId =
        canvasEditor.paramsData.getSomeParamsBySubmitData("algorithmInstanceId");
      getAlgorithmConf(algorithmInstanceId, val).then((res) => {
        if (res.code == 0) {
          // canvasEditor.canvas.clear();
          canvasEditor.canvas.getObjects().forEach((item) => {
            if (item.id !== "workspace") {
              canvasEditor.canvas.remove(item);
            }
          });
          // 更新背景
          const image = new Image();
          image.src = res.data.currentScene.configBenchPic;
          const VHoption = {
            width: 0,
            height: 0,
          };
          image.onload = () => {
            VHoption.width = image.width;
            VHoption.height = image.height;
            const Zoom = VHoption.width === 1920 && VHoption.height === 1080 ? 0.6 : 1;
            canvasEditor.initCanvasBg(canvasEditor.canvas, res.data, VHoption, Zoom, store);
            // 回显图形

            if (res.data.roi && JsonParseFun(res.data.roi).length !== 0) {
              const roiData = [
                {
                  dataType: res.data.roiDrawType,
                  constraints: {},
                  value: res.data.roi,
                  key: "roi_cbb8c",
                  label: "RoI",
                },
              ];
              canvasEditor.echoGraphics.echoDraw(roiData);
            }
            // 参数回显
            canvasEditor.echoGraphics.echoDraw(res.data.paramList);
          };

          // 图片错误处理

          image.onerror = () => {
            message.error("该场景截图不可用，请检查场景", 2);
            canvasEditor.workspace._delWorkspaceBackground();
          };

          // canvasEditor.workspace._setWorkspaceBackground(res.data.currentScene.configBenchPic);
          // roi回显

          initParams(res.data);
        }
      });
    };

    /**
     * 颜色选择器
     * @param item 颜色选择器参数
     */
    const colorPicker = (item) => {
      canvasEditor.setColorMode(item);
    };

    onMounted(() => {
      canvasEditor.paramsData.on("instanceData", (data) => {
        initParams(data);
      });
      uEmitter.on("colorPicker", ({ item, color }) => {
        paramList.value.forEach((element) => {
          if (element.key === item.key) {
            element.value = `#${colorConvert.rgb.hex(color)}`;
          }
        });
      });
    });
    onBeforeUnmount(() => {
      uEmitter.off("colorPicker");
    });

    return {
      drawParamItem,
      currentSceneId,
      currentScenePic,
      sceneList,
      paramList,
      indexBySubalgorithm,
      stringLength,
      // subalgorithmList,
      graphicTypeList,
      getType,
      setData,
      // setSubData,
      roi,
      arrayRef,
      changeSence,
      isMainSence,
      colorConvert,
      getPrecision,
      colorPicker,
    };
  },
});
</script>
<style scoped lang="less">
.confTitle{
  padding-inline: 17px;
  height: 35px;
  line-height: 35px;
  font-size: 16px;
  background-color: #fafafa;
  border-bottom: 1px solid #efefef;
  .bangzhu{
    color: var(--primary-color);
    font-size: 20px;
    float: right;
    
  }
}
.descBox {
  width: 100%;
  height: 500px;
  overflow-x: auto;
  overflow-y: auto;
}
.descBox::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

.paramItem {
  border: 1px solid #d9d9d9;
  padding: 5px 10px;
  width: 100%;
}
.paramItem-required::before {
  display: inline-block;
  margin-right: 4px;
  color: #ff4d4f;
  font-size: 14px;
  font-family: SimSun, sans-serif;
  line-height: 1;
  content: "*";
}

.ant-tabs-left > .ant-tabs-content-holder > .ant-tabs-content > .ant-tabs-tabpane {
  padding-left: 5px;
  padding-right: 5px;
}
.ul {
  width: 100%;
}
.ul > li {
  margin-bottom: 10px;
}

.tabBox {
  width: 100%;
  max-height: 900px;
  overflow-y: auto;
  padding: 0px 10px 50px 10px;
}
.tabBox::-webkit-scrollbar {
  width: 5px;
}
.tabBox::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.2);
}
.tabBox::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
:deep(.ant-tabs-nav .ant-tabs-tab) {
  width: 60px;
  padding: 8px 10px;
}

.colorPicker {
  color: #000;
}
</style>
