.layoutAlConf {
  height: 100vh;
}
.layoutHeaderBox {
  background-color: #fff;
  border-bottom: 0.5px solid #ccc;
  height: 60px;
  line-height: 60px;
  padding-inline: 17px;
  margin-bottom: 16px;
  .headerBox {
    display: flex;
    height: 100%;

    .leftBtn {
      font-size: 16px;
      font-weight: 600;
    }
    .title {
      font-size: 14px;
      font-weight: 400;
      flex: 1;
      margin-left: 181px;
    }
  }
}

#workspace {
  box-shadow: inset 0 0 9px 2px #0000001f;
  border: 1px solid #ccc;
  width: 100%;
  height: calc(100vh - 250px);
}

.pictureSlider {
  margin-left: 16px;
  margin-right: 16px;
  border: 1px solid #ccc;
  height: calc(100vh - 90px);
}
.paramSlider {
  margin-left: 16px;
  margin-right: 16px;
  border: 1px solid #ccc;
  height: calc(100vh - 90px);
}
.log-panel-container {
  background-color: #fff;
  border: 1px solid #ddd;
  height: calc(100vh - 760px);
  position: relative;

  .logTitle {
    height: 42px;
    line-height: 42px;
    background-color: #fafafa;
    border-bottom: 1px solid #efefef;
    text-align: center;
  }
  .help-panel {
    height: 100px;
    width: 100%;
    overflow: auto;
    position: relative;
  }
  .log-panel {
    white-space: pre-wrap;
    word-break: break-word;
    line-height: 2;
    font-family: "Fira Code", "Consolas", monospace;
    font-size: 16px;
    height: 100px;
    padding: 0px 10px;
    align-items: center;
    .log-item {
      display: flex;
      align-items: center; /* 垂直居中对齐 */
      width: 100%;
    }
    .iconfont {
      font-size: 18px;
      margin-right: 8px; /* 根据需要调整图标和文字之间的间距 */
    }
  }
}

.log-panel::-webkit-scrollbar {
  width: 0px !important;
}

.icon-success-filling {
  color: #00a870;
}
.icon-error-fill {
  color: #e34d59;
}
.icon-warning-filling {
  color: #faad14;
}
