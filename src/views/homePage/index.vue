<template>
  <div class="aa">
    <a-row class="row1">
      <a-col :span="8" class="col1-1">
        <h2 class="tith2" style="background-color: linear-gradient(to top,#0276D6, #00B6F7);">数据统计</h2>
        <!-- <div class="lefttoday_tit" style="height: 5%">
          <p class="fl">{{ currentDate() }}</p>
          <p class="fr"></p>
        </div> -->
        <div class="lefttoday_number">
          <div class="widget-inline-box fl">
            <h3>{{ allExecuteRecords }}</h3>
            <p class="leftTop">累计巡航记录</p>
          </div>
          <div class="widget-inline-box fl">
            <h3>{{ accumulateAlarmCount }}</h3>
            <p class="leftTop">累计报警数</p>
          </div>
          <div class="widget-inline-box fl">
            <h3>{{ todayAlarmCount }}</h3>
            <p class="leftTop">今日报警数</p>
          </div>
          <div class="widget-inline-box fl">
            <h3>{{ allInspPoint }}</h3>
            <p class="leftTop">总巡航点数</p>
          </div>
          <div class="widget-inline-box fl" id="gauge1" style="height: 145px"></div>
          <div class="widget-inline-box fl" id="gauge2" style="height: 145px"></div>
        </div>
      </a-col>
      <a-col :span="8" class="col1-1">
        <h2 class="tith2">近7日报警趋势</h2>
        <div class="lefttoday_number" style="height: 83%">
          <div id="bar1" style="height: 100%"></div>
        </div>
      </a-col>
      <a-col :span="8" class="col1-1">
        <h2 class="tith2">仪表盘</h2>
        <div class="lefttoday_number" style="height: 83%;display: flex;flex-wrap: wrap;">
          <div style="width: 50%;height: 100%;">
            <div id="allVariableCount" style="width: 100%;height: 70%;"></div>
            <a-tooltip>
              <template #title>{{ topRight01Title }}</template>
              <p style="color: #2e7cff;text-align: center;">{{ topRight01Title.length > 12 ? topRight01Title.slice(0, 12)+'...':topRight01Title }}</p>
            </a-tooltip>

            <a-select
              size="small"
              style="width: 160px;margin-left: 25px;position: absolute;"
              v-model:value="topRight01Id"
              :options="algorithmOptions"
              @change="handletopRight01"
              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
              dropdownClassName="style-select-dropdown"
            ></a-select>

          </div>
          <div style="width: 50%;height: 100%">
            <div id="todayInsPointCount" style="width: 100%;height: 70%;"></div>
            <a-tooltip>
              <template #title>{{ topRight02Title }}</template>
              <p style="color: #2e7cff;text-align: center;">{{ topRight02Title.length > 14 ? topRight02Title.slice(0, 14)+'...':topRight02Title }}</p>
            </a-tooltip>
            <a-select
              size="small"
              style="width: 160px;margin-left: 25px;position: absolute;"
              v-model:value="topRight02Id"
              :options="algorithmOptions"
              @change="handletopRight02"
              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
              dropdownClassName="style-select-dropdown"
            ></a-select>

          </div>


          <!-- <div id="allChannelCount" style="width: 50%;height: 50%;"></div>
          <div id="allPresetCount" style="width: 50%;height: 50%;"></div> -->
        </div>
      </a-col>
    </a-row>
    <a-row class="row2">
      <a-col :span="8" class="col2-1"
        ><h2 class="tith2">历史</h2>
        <div class="lefttoday_number" style="height: 81%">
          <div>
            <a-space>
              <span>时间间隔:</span>
              <a-select
                size="small"
                style="width: 100px;"
                v-model:value="historytime"
                :options="timeOptions"
                @change="handleTimeChange"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                dropdownClassName="style-select-dropdown"
              ></a-select>
              <span>算法实例:</span>
              <a-select
                size="small"
                style="width: 160px;"
                v-model:value="algorithm"
                :options="algorithmOptions"
                @change="handleAlgorithmChange"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
                dropdownClassName="style-select-dropdown"
              ></a-select>
            </a-space>
          </div>
          <div id="historyBar" style="height: 100%;"></div>
        </div
      ></a-col>
      <a-col :span="8" class="col2-1"
        ><h2 class="tith2">实时巡航点</h2>
        <div class="lefttoday_number" style="height: 81%">
          <!-- <div v-if="dataSource.length === 0" style="margin-top: 30%;margin-left: 42%;font-size: 20px;color: #eef1fa;">暂无数据</div> -->
          <!-- <a-table
            :columns="columns"
            :dataSource="dataSource"
            :pagination="false"
            style="margin: 10px"
            :scorll="{y:300}"
          ></a-table> -->
          <!-- <a-button @click="test">ttt</a-button>
          <a-button @click="add">add</a-button> -->
          <table class="outputTable" aria-describedby="实时运行表">
          <thead >
              <tr >
                <th colspan="1" style="width: 50%;">通道名</th>
                <th colspan="2" style="width: 50%;">实例名</th>
              </tr>

          </thead>
            <tbody v-if="dataSource.length">
              <tr v-for="(item,index) in dataSource">
                <td>{{item.channelName}}</td>
                <td>{{item.presetName}}</td>
              </tr>
            </tbody>
            <tbody v-else>
              <tr>
                <td colspan="2">暂无数据</td>
              </tr>
            </tbody>
          </table>
          </div
      ></a-col>
      <a-col :span="8" class="col2-1"
        ><h2 class="tith2" style="margin-left: 5%;width: 25%;">近7日算法报警</h2>
        <div class="lefttoday_number" style="height: 81%">
          <div style="height: 100%" id="reportTop10"></div></div
      ></a-col>
    </a-row>
  </div>
</template>

<script lang="ts">
import type { SelectProps } from 'ant-design-vue';
import { defineComponent, onMounted, ref, onBeforeUnmount } from 'vue';
import {
  getleftTop,
  getCenterTop,
  getCenterBottom,
  getRightBottom ,
  getRightTop,
  getleftBottomList,
  getleftBottom,
  get7DayAlarm
} from '@/api/operatorStation';
import SetInterval from '@/common/SetInterval';
import HWebsocket from '@/common/h-websocket';
import * as echarts from 'echarts';

type EChartsOption = echarts.EChartsOption;
type ECharts = echarts.ECharts

export default defineComponent({
  setup() {
    const echart = echarts;
    const currentDate = () => {
      const d = new Date();
      const year = d.getFullYear();
      let month: any = d.getMonth();
      month = month + 1 > 12 ? 1 : month + 1;
      month = month > 9 ? month : `0${month.toString()}`;
      const day = d.getDate();
      let hour: any = d.getHours();
      hour = hour > 9 ? hour : `0${hour.toString()}`;
      let minute: any = d.getMinutes();
      minute = minute > 9 ? minute : `0${minute.toString()}`;
      const second = d.getSeconds();
      return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    };

    // 左上数据统计
    const allExecuteRecords = ref<number>(0) // 累计巡航记录
    const accumulateAlarmCount = ref<number>(0) // 累计报警数
    const todayAlarmCount = ref<number>(0) // 今日报警数
    const allInspPoint = ref<number>(0) // 总巡航点数
    const successRate = ref([
      { value: 33, name: '识别成功' },
      { value: 67, name: '识别失败' },
    ]);
    const channelOnlineRate = ref([
      { value: 33, name: '在线' },
      { value: 67, name: '离线' },
    ])
    let LeftChart1 = null;
    let LeftChart2 = null;
    const gauge = (chartDom, data, name) => {
      // const LeftChart1 = echart.init(document.getElementById('gauge1'), 'dark');
      const option:EChartsOption = {
        backgroundColor: 'rgba(1,202,217,.0)',
        tooltip: {
          trigger: 'item',
          formatter: '{a} <br/>{b} : {c}%',
          position:'inside',
          confine:true,
        },

        series: [
          {
            name: name,
            type: 'pie',
            top: -20,
            radius: [30, 55],
            data: data,
            labelLine:{
              show:false
            },
            label:{
              show:false,
            }
          },
        ],
      }
      chartDom.setOption(option);
    };
    // 中上报警数量
    // 获取日期
    const getweek = ()=>{
      let weekX = [];
      const weekDays = ['周日', '周一', '周二', '周三', '周四', '周五', '周六'];
      const today = new Date();
      weekX = weekDays.slice(today.getDay() + 1).concat(weekDays.slice(0, today.getDay() + 1));
      return weekX;

    }
    let topCenterChart = null;
    const topCenter = (data) => {
      let options:EChartsOption = {
        tooltip: {
          trigger: 'axis',

        },
        grid: {
          left: 40,
          right: 20,
          top: 20,
          bottom: 40,
        },
        xAxis:{
          type:'category',
          axisLine:{
            lineStyle:{
              color:'rgba(255,255,255,.3)'
            }
          },
          splitLine:{
            lineStyle:{
              color:'rgba(255,255,255,.3)'
            }
          },
          axisLabel:{
            color:'rgba(255,255,255,.3)'
          },
          data:getweek(),
          axisPointer:{
            type:'shadow'
          }
        },
        yAxis:{
          type: 'value',
          axisLine: {
            lineStyle: {
              color: 'rgba(255,255,255,.3)',
            },
          },
          splitLine: {
            lineStyle: {
              color: 'rgba(255,255,255,.3)',
            },
          },

          axisLabel: {
            formatter: '{value}',
          },
        },
        series:[
          {
            type: 'line',
            name: '报警数量',
            itemStyle: {
              color:'#7cffb2',
            },
            data:data,
          },
          {
            type:'bar',
            data:data,
            tooltip:{
              show:false
            },
            itemStyle:{
              color:'#2e7cff',
            }
          }
        ]

      };
      topCenterChart.setOption(options);
    };

    // 仪表板
    let topRight01:ECharts = null;
    let topRight02:ECharts = null;
    const topRight01Id = ref<string>(); // 左边选择的ID
    const topRight02Id = ref<string>(); // 右边选择的ID
    const topRight01Title = ref<string>(''); // 左边对应的title
    const topRight02Title = ref<string>(''); // 右边对应的title
    const handletopRight01 = ()=>{
      const dataTopRight01 = {
          algorithmInstanceId:getAlgorithmInstanceId(topRight01Id.value),
          algorithmDetailId: topRight01Id.value,
      }
      getRightTop(dataTopRight01).then(res=>{
          if(res.code === 0){
            topRight01Title.value = (algorithmOptions.value as any).find((item)=> item.value === topRight01Id.value).label
            const data = {
              title: (algorithmOptions.value as any).find((item)=> item.value === topRight01Id.value).label,
              value: res.data,
              range:res.data.range
            }
            topRight(topRight01, data.title, data.value,data.range)
          }
        })
    }
    const handletopRight02 = ()=>{
      const dataTopRight02 = {
          algorithmInstanceId:getAlgorithmInstanceId(topRight02Id.value),
          algorithmDetailId: topRight02Id.value,
      }
      getRightTop(dataTopRight02).then(res=>{
          if(res.code === 0){
            topRight02Title.value = (algorithmOptions.value as any).find((item)=> item.value === topRight02Id.value).label
            const data = {
              title: (algorithmOptions.value as any).find((item)=> item.value === topRight02Id.value).label,
              value: res.data.number,
              range:res.data.range
            }
            topRight(topRight02, data.title, data.value,data.range)
          }
        })
    }
    const topRight = (elementDom:ECharts,title:string,data:number = 0,range:number[] = [0,100]) => {
      // const yibiaopan1init = echart.init(document.getElementById(elementId), 'dark');
      let options:EChartsOption = {
        backgroundColor:'rgba(1,202,217,.0)',
        tooltip:{
          formatter: `${title.slice(0,15)}<br />${title.slice(15)}<br />{c}`,
          confine:true,
        },
        grid:{
          top:50,
          left:0,
        },
        series:[
          {
            name:title,
            type:'gauge',
            detail:{
              formatter:'{value}'
            },
            // 仪表盘指针
            pointer:{
              show:true,
            },
            // 仪表盘轴线样式
            axisLine:{
              lineStyle:{
                width:5,
              }
            },
            // 展示当前进度
            progress:{
              show:true,
              width:3
            },
            // 分隔线样式
            splitLine:{
              show:false,
            },
            // 刻度样式
            axisTick:{
              distance:1,
              length:3,
            },
            // 刻度标签
            axisLabel:{
              distance: 0,
            },
            min:range[0],
            max:range[1],
            splitNumber:10,
            radius:'100%',
            data:[
              {
                detail:{
                  fontSize:14,
                  color:'#2e7cff',
                },
                title:{
                  color:'#2e7cff',
                  fontSize:12
                },
                value:data,
                // name:title.length > 10?title.substring(0, 10) + "...":title
              }
            ]
          }
        ]
      };
      elementDom.setOption(options);
    };
    // 历史
    let bottomLeft:ECharts = null;
    const historyBar = (xAxis,data) => {
      // const historyBarinit = echart.init(document.getElementById('historyBar'), 'dark');
      const options:EChartsOption = {
        color: ['#FADB71'],
        backgroundColor: 'rgba(1,202,217,.0)',
        tooltip: {
          show:true,
          trigger: 'axis',
          axisPointer: {
            // 坐标轴指示器，坐标轴触发有效
            type: 'shadow', // 默认为直线，可选为：'line' | 'shadow'
          },
          confine:true,
        },
        title:{
          show:false,
        },
        grid: {
          top: 20,
          bottom:100,
          left:40,
          right:10
        },
        xAxis: [
          {
            show:true,
            type: 'category',
            data: xAxis,
            // axisTick: {
            //   alignWithLabel: true,
            // },
            // nameRotate:90,
            axisLabel:{
              show:true,
              interval: 0,
              rotate:45,
              formatter:function(value){
                if (value.length > 5) {
                    return value.substring(11);
                  } else {
                    return value;
                  }
              }
            }
          },
        ],
        yAxis: [
          {
            type: 'value',
            show:true,
          },
        ],
        series: [
          {
            name: '当前时段的值',
            type: 'bar',
            barWidth: '0%',
            color:'#2e7cff',
            data: data,
          },
        ],
      }

      const EmptyOption:EChartsOption = {
          backgroundColor:'rgba(1,202,217,.0)',
          title:{
            show:true,
            text:'暂无数据',
            top:105,
            left:'center',
            textStyle:{
              fontSize:20,
              fontWeight:'normal',
            }
          },
          tooltip:{
            show:false
          },
          xAxis:{
            show: false
          },
          yAxis:{
            show: false
          },
          series:[
            {
              data:[],
            }
          ],
      }
      if(data.length === 0 && xAxis.length === 0){

        // historyBarinit.clear();
        // bottomLeft.clear();
        bottomLeft.setOption(EmptyOption);
      }else{

        // bottomLeft.clear();
        bottomLeft.setOption(options);
      }


    };
    const historytime = ref<string>('24');
    const timeOptions = ref<SelectProps['options']>([
      {
        value: '24',
        label: '24小时',
      },
      {
        value: '12',
        label: '12小时',
      },
      {
        value: '3',
        label: '3小时',
      },
    ]);
     // 通过algorithmDetailId获取algorithmInstanceId
     const getAlgorithmInstanceId = (algorithmDetailId:string)=>{
        let id:string = null;
        algorithmOptions.value.forEach((item)=>{
          if(item.value === algorithmDetailId){
            id = item.algorithmInstanceId
          }
        })
        return id;
    }
    const handleTimeChange = (val)=>{
      const data = {
          algorithmInstanceId: getAlgorithmInstanceId(algorithm.value),
          algorithmDetailId: algorithm.value,
          hour:val,
        }
      getleftBottom(data).then((ress)=>{
        if(ress.code === 0){
          const xAxis = ress.data.map((item)=>{
            return Object.keys(item)[0];
          })
          const barData =  ress.data.map((item)=>{
            return Object.values(item)[0];
          })
          historyBar(xAxis,barData);
        }
      })
    }
    const algorithm = ref<string>('');
    const algorithmOptions = ref([]);
    const handleAlgorithmChange = (val)=>{
      // const data = {
      //     algorithmInstanceId:val,
      //     hour:historytime.value,
      //   }
        const data = {
          algorithmInstanceId: getAlgorithmInstanceId(val),
          algorithmDetailId: val,
          hour:historytime.value,
        }
        getleftBottom(data).then((ress)=>{
          if(ress.code === 0){
            const xAxis = ress.data.map((item)=>{
              return Object.keys(item)[0];
            })
            const barData =  ress.data.map((item)=>{
              return Object.values(item)[0];
            })
            historyBar(xAxis,barData);
          }
        })
    }

    // 历史报警
    const columns = [
      {
        title: '通道名',
        dataIndex: 'channelName',
      },
      {
        title: '实例名',
        dataIndex: 'presetName',
      },
      // {
      //   title: '状态',
      //   dataIndex: 'action',
      // },
    ];
    const dataSource = ref([

    ]);

    let wsTable = null;
    const add = ()=>{
      dataSource.value.push({
        channelName:String(Math.random()).slice(0,5),
        presetName:String(Math.random()).slice(0,5),
        presetId:'222'
      });
    }
    // const test = ()=>{
    //   dataSource.value = dataSource.value.filter((item)=> item.presetId !== '222' )
    // }
    function openWs() {
      // drawGraph(fakedata);
      wsTable = new HWebsocket(`${(window as any).g.wsApi}/websocket/real-time-point`);
      wsTable.onMessage = (evt) => {
        const wsData = JSON.parse(evt.data);
        if(wsData.action === 'start'){
          dataSource.value.push(wsData);
        }
        if(wsData.action === 'end'){
          dataSource.value = dataSource.value.filter((item)=>{ return item.presetId !== wsData.presetId })
          // console.log(dataSource.value);
        }

      };
    };
    function closeWs() {
      if (wsTable) {
        console.log('关闭ws');
        wsTable.close();
        wsTable = null;
      }
    }

    // 近7日算法报警
    let bottomRight = null;
    const reportTop10 = (axisData,seriesData) => {
      // const reportTop10init = echart.init(document.getElementById('reportTop10'), 'dark');
      let options:EChartsOption = null;
      if(seriesData.length === 0){
        options = {
          backgroundColor:'rgba(1,202,217,.0)',
          title:{
            show:true,
            text:'暂无数据',
            top:105,
            left:'center',
            textStyle:{
              fontSize:20,
              fontWeight:'normal',
            }
          },
          tooltip:{
            show:false
          },
          xAxis:{
            show: false
          },
          yAxis:{
            show: false
          },
          series:[
            {
              data:[],
            }
          ],
        }
      } else {
        options = {
          backgroundColor:'rgba(1,202,217,.0)',
          tooltip: {
            show:true,
            trigger: 'item',
            axisPointer: {
              type: 'shadow',
            },
            confine:true,
          },
          title:{
            show:false,
          },
          grid: {
            left: 0,
            right: 25,
            bottom: 30,
            top:0,
            containLabel: true,
          },
          xAxis: {
            show:true,
            type: 'value',
            boundaryGap: [0, 0.01],
            // axisLine:{
            //   lineStyle:{
            //     color:'#ccc'
            //   }
            // },
          },
          yAxis: {
            show:true,
            type: 'category',
            data: axisData,
            inverse:true,
            // nameTextStyle:{
            //   color:'#fff'
            // },
            axisLabel:{
              show:true,
              interval: 0,
              formatter:function(value){
                if (value.length > 8) {
                    return value.substring(0, 8) + "...";
                  } else {
                    return value;
                  }
              }
            }

          },
          series: [
            {
              type: 'bar',
              data: seriesData,
            },
          ],
        }
      }
      bottomRight.setOption(options);
    };

    // 请求数据
    const getAllData = (isRefresh = false)=>{
      // 数据统计
      getleftTop().then(res=>{
        if(res.code === 0){
          allExecuteRecords.value = res.data.allExecuteRecords;
          accumulateAlarmCount.value = res.data.accumulateAlarmCount;
          todayAlarmCount.value = res.data.todayAlarmCount;
          allInspPoint.value = res.data.allInspPoint;
          successRate.value = [
            { value: Number(res.data.successRate.slice(0,-1)), name: '识别成功' },
            { value: 100-Number(res.data.successRate.slice(0,-1)), name: '识别失败' },
          ]
          channelOnlineRate.value = [
            { value: Number(res.data.channelOnlineRate.slice(0,-1)), name: '在线' },
            { value: 100-Number(res.data.channelOnlineRate.slice(0,-1)), name: '离线' },
          ]
        }
        gauge(LeftChart1,successRate.value, '识别成功率');
        gauge(LeftChart2,channelOnlineRate.value, '通道在线率')
      })
      // 近7日报警趋势
      get7DayAlarm().then(res=>{
        if(res.code === 0){
          topCenter(res.data);
        }
      })
      // 实时巡航点
      // getCenterBottom().then(res=>{
      //   if(res.code === 0){
      //     dataSource.value = res.data;
      //   }
      // })
      // 近7日算法报警
      getRightBottom().then(res=>{
        if(res.code === 0 && res.data){
          const axis = res.data.map((item)=>{
            return item.algorithmName;
          })
          const data = res.data.map((item,index)=>{
            if(index < 3){
              return {
                name:`${item.channelName}_${item.presetName}_${item.algorithmName}`,
                value:item.alarmCount,
                itemStyle:{
                  color: '#a90000'
                }
              }
            }
            return {
              name: `${item.channelName}_${item.presetName}_${item.algorithmName}`,
              value: item.alarmCount
            }
          })
          reportTop10(axis,data)
        }
      })

      // 仪表盘、历史
      getleftBottomList().then(res=>{
        if(res.code === 0){
          if(!isRefresh){
            algorithm.value = res.data[0].algorithmDetailId;
            topRight01Id.value = res.data[0].algorithmDetailId;
            topRight02Id.value = res.data[0].algorithmDetailId;
          }
          res.data.forEach((item,index)=>{
            if(index >= 1){
              if(item.algorithmInstanceId === res.data[index-1].algorithmInstanceId){
                item.presetName = `${item.presetName}(${index})`
                res.data[index-1].presetName = `${res.data[index-1].presetName}(${index - 1})`
              }
            }
          })

          algorithmOptions.value = res.data.map((item)=>{

            return{
              algorithmInstanceId: item.algorithmInstanceId,
              value: item.algorithmDetailId,
              label: `${item.channelName}_${item.algorithmName}_${item.presetName}`
            }
          })
          console.log(algorithmOptions.value,'algorithmOptions.value')
        }
      }).then(()=>{
        const data = {
          algorithmInstanceId:getAlgorithmInstanceId(algorithm.value),
          algorithmDetailId: algorithm.value,
          hour:historytime.value,
        }
        getleftBottom(data).then((ress)=>{
          if(ress.code === 0){
            const xAxis = ress.data.map((item)=>{
              return Object.keys(item)[0];
            })
            const barData =  ress.data.map((item)=>{
              return Object.values(item)[0];
            })
            historyBar(xAxis,barData);
          }
        });
        const dataTopRight01 = {
          algorithmInstanceId:getAlgorithmInstanceId(topRight01Id.value),
          algorithmDetailId: topRight01Id.value,
        }
        getRightTop(dataTopRight01).then(res=>{
          if(res.code === 0){
            topRight01Title.value = (algorithmOptions.value as any).find((item)=> item.value === topRight01Id.value).label
            const data = {
              title: (algorithmOptions.value as any).find((item)=> item.value === topRight01Id.value).label,
              value: res.data.number,
              range: res.data.range
            }
            topRight(topRight01,data.title,data.value, data.range)
          }
        })
        const dataTopRight02 = {
          algorithmInstanceId:getAlgorithmInstanceId(topRight02Id.value),
          algorithmDetailId: topRight02Id.value,
        }
        getRightTop(dataTopRight02).then(res=>{
          topRight02Title.value = (algorithmOptions.value as any).find((item)=> item.value === topRight02Id.value).label
          if(res.code === 0){
            const data = {
              title: (algorithmOptions.value as any).find((item)=> item.value === topRight02Id.value).label,
              value: res.data.number,
              range: res.data.range
            }
            topRight(topRight02,data.title,data.value, data.range)
          }
        })
      })

    }
    const initChart = ()=>{
      return new Promise<void>((resolve,reject)=>{
        const gauge1 = document.getElementById('gauge1');
        gauge1.setAttribute('_echarts_instance_', '');
        LeftChart1 = echart.init(gauge1);
        //
        const gauge2 = document.getElementById('gauge2');
        gauge2.setAttribute('_echarts_instance_', '');
        LeftChart2 = echart.init(gauge2);
        //
        const bar1 = document.getElementById('bar1');
        bar1.setAttribute('_echarts_instance_', '');
        topCenterChart = echart.init(bar1);
        //
        const reportTop10 = document.getElementById('reportTop10');
        reportTop10.setAttribute('_echarts_instance_', '');
        bottomRight = echart.init(reportTop10,'dark');
        //
        const allVariableCount = document.getElementById('allVariableCount');
        allVariableCount.setAttribute('_echarts_instance_', '');
        topRight01 = echart.init(allVariableCount,'dark');
        //
        const todayInsPointCount = document.getElementById('todayInsPointCount');
        todayInsPointCount.setAttribute('_echarts_instance_', '');
        topRight02 = echart.init(todayInsPointCount,'dark');
        //
        const historyBar = document.getElementById('historyBar');
        historyBar.setAttribute('_echarts_instance_', '');
        bottomLeft = echart.init(historyBar,'dark');
        resolve();
      })
    }
    SetInterval.add('intervalUse',()=>{
      getAllData(true);
    },10000)
    const destroyChart = ()=>{
      LeftChart1.clear();
      LeftChart2.clear();
      topCenterChart.clear();
      bottomRight.clear();
      topRight01.clear();
      topRight02.clear();
      bottomLeft.clear();
      console.log('清除成功');
    }
    onMounted(() => {
      initChart().then(()=>{
        getAllData();
        openWs();
      })

      SetInterval.run('intervalUse');
    });
    onBeforeUnmount(()=>{
      closeWs();
      destroyChart();
      SetInterval.close('intervalUse');
    })
    return {
      currentDate,
      // 数据统计
      allExecuteRecords,
      accumulateAlarmCount,
      todayAlarmCount,
      allInspPoint,
      historytime,
      timeOptions,
      handleTimeChange,
      algorithm,
      algorithmOptions,
      handleAlgorithmChange,
      columns,
      dataSource,
      topRight01Id,
      topRight02Id,
      handletopRight01,
      handletopRight02,
      topRight01Title,
      topRight02Title,
      add,
      // test
    };
  },
});
</script>

<style scoped>
.row1 {
  height: 365px;
}
.row2 {
  height: 365px;
}
.col1-1 {
  background: url(../../assets/border1.png);
  background-size: 97% 100%;
  background-repeat: no-repeat;
  background-position: top center;
  width: 95%;
  height: 99%;
  margin-bottom: 1%;
}
.col2-1 {
  background: url(../../assets/border1.png);
  background-size: 97% 100%;
  background-repeat: no-repeat;
  background-position: top center;
  width: 99%;
  height: 99%;
  margin-bottom: 1%;
}
.tith2 {

  width: 30%;
  font-size: 16px;
  padding-top: 4.5%;
  font-weight:bolder;
  letter-spacing: 1px;
  overflow: hidden;
  color: #fff;
  margin-left: 5%;
}
.lefttoday_tit {
  overflow: hidden;
  /* padding: 1.9% 5% 0.2%; */
  height: 6%;
  position: relative;
}
.lefttoday_tit p.fl {
  font-size: 12px;
  color: rgba(255, 255, 255, 1);
  position: absolute;
  left: 5%;
  top: 22%;
}
.leftTop{
  color: #00b3f8;
}
.lefttoday_number {
  overflow: hidden;
  height: 76%;
  width: 91%;
  margin: 3% 3%;
}
.widget-inline-box {
  text-align: center;
  color: rgba(255, 255, 255, 0.9);
  width: 50%;
  font-size: 18px;
  float: left;
  overflow: hidden;
}
.widget-inline-box h3 {
  font-size: 22px;
  font-weight: 100;
  font-weight: normal;
  margin-bottom: -5px;
  color: #fff;
}

/* select style */
/* :deep(.ant-select){
  position: absolute;
} */
:deep(.ant-select:not(.ant-select-customize-input) .ant-select-selector){
  background-color: rgb(6, 56, 119) !important;
  border: 1px solid rgb(1, 97, 161);
  color: #fff;

}

:deep(.style-select-dropdown){
  background-color: rgb(6, 56, 119) !important;
}

:deep(.ant-select-item){
  background-color: rgb(6, 56, 119) !important;
  color: #fff !important;
}

:deep(.ant-select-item-option-active){
  background-color: rgb(8, 100, 212) !important;
  color: #fff !important;
}

/* table Style */

:deep(.ant-table) {
  background: #06122e;
  color: white;
}
:deep(.ant-table-body)::-webkit-scrollbar {
  width: 5px !important;
}
:deep(.ant-table-body)::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgb(6, 56, 119);
}
:deep(.ant-table-body)::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
:deep(.ant-table-thead > tr > th) {
  background: #112138;
  color: #4cb5c4;
  border-bottom: 1px solid rgb(1, 97, 161);
}
:deep(.ant-table-row:hover) {
  background: #1b317d;
}
:deep(.ant-table-tbody > tr.ant-table-placeholder:hover > td) {
  background: #112138;
}
:deep(.ant-empty-normal) {
  color: white;
}

::v-deep .ant-table-tbody > tr.ant-table-row:hover > td {
  background: none !important;
}
.outputTable{
  width: 90%;
  border: 1px solid #008ef1;
  margin: 10px 20px 20px 20px;
  font-size: 20px;
  text-align: center;

}
.outputTable > thead{
  background-color: #063877;
}
.outputTable > tr {
  border: 1px solid #063877;
  width: 100%;
  font-size: 20px;
  align-items: center;
}
.outputTable > tr > td {
  border: 1px solid #063877;
  width: 50%;
  font-size: 20px;
  align-items: center;
  text-align: center;
}

</style>
