<template>
  <iframe
    src="http://**************/mesInsp/"
    style="width: 900px; height: 600px; border: none"
    allowfullscreen
    title="Jessibuca"
    id="showVideoBoxBoxaa"
  ></iframe>
  <button @click="fullScreen()">全屏</button>
</template>
<script setup lang="ts">
import { nextTick } from "process";
import { onMounted, ref } from "vue";

const fullScreen = () => {
  const video = document.getElementById("showVideoBoxBoxaa");
  if (!document.fullscreenElement) {
    video
      .requestFullscreen()
      .then(() => {
        // setTimeout(() => {
        //   emit("fullScreen");
        // }, 200);
      })

      .catch((err) => {
        alert(`尝试启用全屏模式时出错：${err.message}（${err.name}）`);
      });
  } else {
    document.exitFullscreen();
  }
};
document.addEventListener("fullscreenchange", () => {
  console.log("fullscreenchange");
});
const videoWindowRef = ref(null);
</script>

<style lang="less" scoped>
.test {
  width: 50%;
  height: 50%;
  border: 1px solid #ccc;
}
:deep(.easyplayer-loading-text) {
  color: #fff;
}
</style>
