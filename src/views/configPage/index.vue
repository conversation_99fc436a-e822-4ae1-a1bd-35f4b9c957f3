<template>
  <div>
    <a-row class="rolStyle" :gutter="16">
      <a-col :span="6">
        <div class="TreeBox">
          <div class="titleInBox"></div>
          <div class="TreeInBox">
            <configTree @nodeId="getNodeId"></configTree>
          </div>
        </div>
      </a-col>
      <a-col :span="18">
        <div class="videoBox">
          <div class="VideoTitleBox">
            <div class="titleLeft"></div>
            <div style="margin-left: 50px;margin-right: 50px;">{{days}}</div>
            <div class="titleLeft" style="transform: scaleX(-1);"></div>
          </div>
          <div class="videoInBox">
            <singleVideo :videoUrl="url" :videoId="'configVideo'" :width="1000" :height="562.5" :channel-id="channelId" style="margin-top: 100px;"></singleVideo>
          </div>

        </div>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref } from 'vue';
import { getChannelVideoLocation} from '@/api/design';
import configTree from './components/configTree.vue';
import singleVideo from '../splitScreen/components/singleVideo.vue';
import dayjs from 'dayjs';

export default defineComponent({
  components: {
    configTree,
    singleVideo,
  },

  setup() {
    const url = ref(null);
    const channelId = ref<string>('');
    const getNodeId = (val) => {
      console.log(val);
      channelId.value = val.id;
      if (val.typeNode === 'channel') {
        getChannelVideoLocation(val.id).then((res) => {
          url.value = res.data;
        });
      }
    };
    const days = dayjs().format('YYYY-MM-DD HH:mm')
    return {
      getNodeId,
      url,
      days,
      channelId,
    };
  },
});
</script>

<style scoped>
.rolStyle {
  width: 100%;
  height: 770px;
}
.TreeBox {
  width: 100%;
  height: 100%;
  background: url('../../assets/border4.png');
  background-size: 97% 100%;
  background-repeat: no-repeat;
  background-position: top center;
  color: aliceblue;
  padding-top:1px;
}

.titleInBox{
  width: 80%;
  height: 45px;
  background: url('../../assets/title2.png');
  background-repeat: no-repeat;
  background-position: top center;
  background-size: 100% 100%;
  margin-top: 10px;
  margin-left: 20px;
}
.TreeInBox{
  width: 90%;
  height: 680px;
  overflow: auto;
  margin-left: 10px;
  margin-top: 10px;
  overflow-x: hidden;
}
.TreeInBox::-webkit-scrollbar {
  width: 5px;
}
.TreeInBox::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.2);
}
.TreeInBox::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
.videoBox {
  background: url('../../assets/border5.png');
  background-repeat: no-repeat;
  background-position: top center;
  background-size: 100% 100%;
  width: 1030px;
  height: 100%;
  padding-top: 70px;
  padding-left: 10px;
}
.VideoTitleBox{
  width: 100%;
  display: flex;
}
.titleLeft{
  width: 400px;
  height: 20px;
  background: url(../../assets/title3.png);
  background-repeat: no-repeat;
  background-position: top center;
  background-size: 100% 100%;
}
.videoInBox{
  margin-top: 10px;
}
::v-deep .ant-tree {
  background: rgba(0, 0, 0, 0);
  color: aliceblue;
}
::v-deep .ant-tree .ant-tree-node-content-wrapper:hover{
  background: rgba(1, 202, 217, 0.1);
}
::v-deep .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected{
  background: rgba(1, 202, 217, 0.2);
}
</style>
