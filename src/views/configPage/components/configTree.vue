<template>
  <!-- <span>
    <span style="font-size: 15px; color: rgba(0, 0, 0, 0)"><strong>工程1</strong></span>
    <span style="float: right; padding-right: 19px; font-size: 15px"><strong>状态</strong></span>
  </span> -->
  <a-tree
    style="text-align: left; padding-bottom: 50px"
    :tree-data="treeData"
    v-model:selectedKeys="selectedkeys"
    block-node
    defaultExpandAll
    @select="selectTree"
    class="projectTree"
    :fieldNames="{ key: 'id', title: 'label' }"
    v-if="treeData.length"
  >
    <template #title="{ data }">
      <span class="custom-tree-node">
        <a-dropdown :trigger="['contextmenu']">
          <span>
            <IconFont
              :type="IconList[data.type]"
              v-if="data.type !== 'channel'"
              style="color: #fff"
            />
            <IconFont
              type="icon-qiuji-copy"
              v-if="data.type === 'channel' && data.deviceType.includes('PTZ')"
              style="color: #fff"
            />
            <IconFont
              type="icon-a-kakouqiangshishexiangtoujiankongqiangshishexiangtou-copy"
              v-if="data.type === 'channel' && !data.deviceType.includes('PTZ')"
              style="color: #fff"
            />
            <a-tooltip placement="right">
              <template #title>
                {{ data.label }}
              </template>
              <span>{{
                data.label.length > 15 ? data.label.slice(0, 15) + "..." : data.label
              }}</span>
            </a-tooltip>
          </span>
        </a-dropdown>
        <span>
          <div
            v-if="data.type === 'preset' && data.status === 'RUNNING'"
            class="statusPoint"
          ></div>
          <div
            v-else-if="data.type === 'channel' && data.status === 'CONNECT_SUCCESS'"
            class="runChannelStatus"
          ></div>
          <div
            v-else-if="data.type === 'channel' && data.status === 'CONNECT_FAILED'"
            class="stopChannelStatus"
          ></div>
          <div v-else class="emptyStatus"></div>
        </span>
      </span>
    </template>
  </a-tree>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, reactive, onUnmounted } from "vue";
import { getProjectTreeForDCS } from "@/api/design";
import { useRoute } from "vue-router";
import { useStore } from "vuex";
import IconFont from "@/components/c-iconfont";

export default defineComponent({
  components: {
    IconFont,
  },
  props: {
    randomStr: {
      type: String,
    },
    treeResize: {
      type: Number,
    },
  },
  setup(props, ctx) {
    const route = useRoute();
    const store = useStore();
    const delId = ref<string>("");
    const selectedkeys = ref<any[]>([""]);
    const treeData = ref([]);
    const projectId = ref('');
    const getTreeData = async () => {
      const data = {
        size: 20,
        current: 1,
      };
      const treeRes = await getProjectTreeForDCS();
      if (treeRes.code !== 0) return;
      // 递归树
      treeData.value = recursionTree(treeRes.data.tree);
      selectedkeys.value = [treeRes.data.tree[0].id];
    };
    // 递归树
    const filterateArray = ["ops", "ens", "control-flow"];
    const recursionTree = (data: any[]) => {
      if (data.length === 0) {
        return;
      }
      const arr = data;
      arr[0].children = arr[0].children.filter((item) => !filterateArray.includes(item.type));
      return arr;
    };
    const selectTree = (selectedKey, { selected, selectedNodes, node }) => {
      if (selected) {
        const treeNode = {
          id: node.id,
          typeNode: node.type,
          name: node.label,
        };
        ctx.emit("nodeId", treeNode);
      } else {
        const treeNode = {
          id: projectId.value,
        };
        ctx.emit("nodeId", treeNode);
      }
    };

    // 当前节点信息
    //const timer = null;

    onMounted(() => {
      getTreeData();
      // timer = setInterval(() => {
      //   getTreeData();
      // }, 60000);
    });
    onUnmounted(() => {
      // clearInterval(timer);
    });
    // 树的动态样式
    const dynamicStyle = ref({
      display: "inline-block",
      width: "120px",
      height: "19px",
      "text-overflow": "ellipsis",
      overflow: "hidden",
      "white-space": "nowrap",
    });

    // 启动
    // const start = (instanceId) => {
    //   executeInstance({ presetId: instanceId }).then((res) => {
    //     if (res.code === 0) {
    //       message.success("执行成功");
    //     }
    //   });
    // };
    // watch(
    //   () => props.treeResize,
    //   (nv: number) => {
    //     dynamicStyle.value.width = `${nv - 130}px`;
    //   }
    // );
    const typeList = reactive({
      directory: "区域",
      channel: "通道",
      "preset": "预置点",
    });
    const IconList = reactive({
      directory: "icon-file-directory",
      channel: "icon-a-kakouqiangshishexiangtoujiankongqiangshishexiangtou",
      "preset": "icon-a-biaojidingweibiaodian",
      project: "icon-shishigongkuang-copy",
      ops: "icon-yonghuguanli-1",
      ens: "icon-yonghuguanli-1",
      "control-flow": "icon-sheji",
      "web-config": "icon-13",
    });
    return {
      treeData,
      selectTree,
      selectedkeys,
      dynamicStyle,
      typeList,
      IconList,
    };
  },
});
</script>
<style lang="less" scoped>
.modelInput {
  display: inline-block;
}
.modelContent {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
.projectTree {
  .treeStyle {
    width: 100%;
    display: inline-block;
    white-space: nowrap;
  }
}
.statusIcon {
  margin-left: -5px;
  margin-right: 5px;
}
.errStyle {
  border-color: #ff4d4f;
}
.message {
  margin-left: 72px;
  margin-bottom: 6px;
  color: #ff4d4f;
  height: 18px;
}
.custom-tree-node {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: space-between;
  font-size: 14px;
  padding-right: 23px;
  height: 42px;

}
::v-deep .ant-tree-switcher {
  width: 15px !important;
}
</style>
<style scoped>
.statusPoint {
  /* display: inline; */
  width: 10px;
  height: 10px;
  margin-left: 30px;
  border-radius: 5px 5px 5px 5px;
  background-color: #50fa7b;
  animation: blink 1s infinite steps(1);
  /* vertical-align: super;
  text-align: center; */
  margin-top: -27px;
}
@keyframes blink {
  50% {
    background-color: transparent;
  }
}
.runChannelStatus {
  width: 10px;
  height: 10px;
  margin-left: 30px;
  border-radius: 5px 5px 5px 5px;
  background-color: rgb(5, 243, 5);
}
.stopChannelStatus {
  width: 10px;
  height: 10px;
  margin-left: 30px;
  border-radius: 5px 5px 5px 5px;
  background-color: red;
}
.emptyStatus {
  width: 10px;
  height: 10px;
  margin-left: 30px;
  border-radius: 5px 5px 5px 5px;
  background-color: rgba(255, 255, 255, 0);
}
</style>
