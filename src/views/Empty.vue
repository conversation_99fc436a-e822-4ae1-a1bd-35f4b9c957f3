  <template>
      <!-- <a-result title="此功能开发中...， 敬请期待">
        <template #icon>
        <smile-twoTone />
        </template>
        <template #extra>
        <a-button type="primary" @click="backHomePage" style="width: 100px;">返回首页</a-button>
        </template>
      </a-result> -->
      <a-alert
        message="关于"
        :description="`Copyright © ${currentYear} HollySys All rights reserved. `"
        type="warning"
        show-icon
        style="margin: 20px;"
      />
  </template>

  <script lang="ts">
  import { defineComponent } from 'vue';
  import { useRouter } from 'vue-router';
  import { SmileTwoTone } from '@ant-design/icons-vue';

  export default defineComponent({
    components:{
        SmileTwoTone
    },
    setup() {
      const currentYear = new Date().getFullYear();

      const router = useRouter();
      const backHomePage = ()=>{
        router.push({ name: 'project_design' });
      }
      return {
        backHomePage,
        currentYear
      };

    },
  });
  </script>

  <style scoped>
  .content {
    display: flex;
    justify-content: center;
    font-weight: bolder;
    font-size: 36px;
  }
  </style>
