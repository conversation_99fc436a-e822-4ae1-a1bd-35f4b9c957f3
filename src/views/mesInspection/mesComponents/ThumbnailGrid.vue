<template>
  <div class="thumbnail-grid">
    <div v-for="item in taskList" :key="item.id">
      <div class="thumbnail-item">
        <div v-if="item.id === selectedId" class="thumbnail-item-selected-hover"></div>
        <img
          :src="item.channelPic"
          alt="thumbnail"
          class="thumbnail-item-img"
          :class="{ 'thumbnail-item-img-selected': item.id === selectedId }"
          @click="selectTask(item.id)"
          draggable="false"
        />
        <div class="thumbnail-item-title">{{ item.label }}</div>
      </div>
    </div>
  </div>
</template>

<script lang="ts" setup>
import uEmitter from "@/common/u-event-bus";
import { before } from "node:test";
import { onBeforeMount, onBeforeUnmount, onMounted, ref } from "vue";
const selectedId = ref("8db1ac5e4b9d449292521fea63658026");
const taskList = ref([]);

// --------------------------------------------------emit/props-----------------------------------------

const props = defineProps({
  autoOrmulti: {
    type: String,
    default: "auto",
  },
});

const emit = defineEmits(["selectTaskForVideo"]);

// --------------------------------------------------接收任务列表数据--------------------------------------
const receiveTaskList = (data: any[]) => {
  // data.forEach((item) => {
  //   if (item.id === selectedId.value) {
  //     item.channelPic = "./1745370862791.png";
  //   }
  // });
  // for (let i = 0; i < 10; i++) {
  //   data.push({
  //     id: `${i}`,
  //     label: `啊实打实大苏打啊实打实打算啊实打实大大苏${i}`,
  //     channelPic: `./1745370862791.png`,
  //   });
  // }
  if (data.length === 0) {
    return;
  }
  const findRunning = data.findIndex((item) => item.status === "RUNNING");
  if (findRunning !== -1) {
    selectedId.value = data[findRunning].id;
  } else {
    selectedId.value = data[0].id;
  }
  taskList.value = data;
};

// --------------------------------------------------选择任务--------------------------------------------

const selectTask = (id: string) => {
  if (props.autoOrmulti === "MANUAL") {
    selectedId.value = id;
    const selectedTask = taskList.value.find((item) => item.id === id);
    emit("selectTaskForVideo", selectedTask);
  }
};

const autoChangeTask = (currentTask) => {
  selectedId.value = currentTask.id;
  const selectedTask = taskList.value.find((item) => item.id === currentTask.id);
  emit("selectTaskForVideo", selectedTask);
};

// 接受左右键切换任务

onMounted(() => {
  uEmitter.on("changeVideoByNext", (id, step) => {
    const index = taskList.value.findIndex((item) => item.id === id);
    if (index === -1) return;
    const nextIndex = index + step;
    if (nextIndex < 0 || nextIndex >= taskList.value.length) return;

    const nextTesk = taskList.value[nextIndex];
    selectTask(nextTesk.id);
  });
});

onBeforeUnmount(() => {
  uEmitter.off("changeVideoByNext");
});

defineExpose({
  receiveTaskList,
  autoChangeTask,
});
</script>

<style lang="less" scoped>
.thumbnail-grid {
  display: flex;
  gap: 12px;
  position: relative;
  height: 100%;
  width: 100%;
  overflow-x: auto;
  overflow-y: hidden;

  .thumbnail-item {
    width: 182px;
    height: 100%;
    border-radius: 5px;
    margin-bottom: 8px;
    position: relative;
    .thumbnail-item-img {
      width: 100%;
      height: 65%;
      object-fit: cover;
      border-radius: 5px;
      cursor: pointer;
      border: 2px solid rgba(0, 0, 0, 0);
    }
    .thumbnail-item-selected-hover {
      width: 100%;
      height: 65%;
      position: absolute;
      top: 0px;
      left: 0px;
      border-radius: 5px;
      background: url("../../../assets/checkMark.png") no-repeat #01248496;
      background-position: center;
      cursor: pointer;
    }
    .thumbnail-item-img-selected {
      border: 2px solid #66b3ff;
    }
    .thumbnail-item-title {
      font-size: 12px;
      padding-left: 15px; /* 留出图标空间 */
      background: url("../../../assets/greenPoint.png") no-repeat;
      background-position: left 0;
      background-size: 16px 16px; /* 控制图标大小 */
      color: #c7e7ff;
      text-align: left;
      display: inline-block;
      text-indent: 0;
    }
  }
}
</style>
