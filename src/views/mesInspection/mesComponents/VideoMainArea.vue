<template>
  <videoPlayer
    ref="videoplayerRef"
    :show-control="true"
    v-if="showVideo"
    :taskInfo="currentTaskInfo"
    :taskMode="true"
  ></videoPlayer>
  <a-empty v-else class="empty-box"></a-empty>
</template>

<script setup lang="ts">
import { ref, nextTick, provide } from "vue";
import videoPlayer from "@/components/videoElement/videoPlayer.vue";
import { getChannelVideoLocationDCS } from "@/api/design";

const videoplayerRef = ref(null);

const showVideo = ref(true);

const currentChannelId = ref("");
const currentPresetId = ref<string>("");
const currentTaskInfo = ref<any>();
const emit = defineEmits(["noneTask"]);

const loadvideo = async (taskInfo) => {
  const channelIdList = taskInfo.taskNodes.map((item) => item.channelId);
  // 去重
  const uniqueChannelIdList = Array.from(new Set(channelIdList));

  videoplayerRef.value.destroyVideo();
  if (channelIdList.length === 0) {
    emit("noneTask");
    return;
  }

  showVideo.value = true;

  nextTick(async () => {
    const findRunning = taskInfo.taskNodes.findIndex((item) => item.status === "RUNNING");
    if (findRunning !== -1) {
      currentChannelId.value = channelIdList[findRunning];
      currentPresetId.value = taskInfo.taskNodes[findRunning].id;
      currentTaskInfo.value = taskInfo.taskNodes[findRunning];
    } else {
      currentChannelId.value = channelIdList[0];
      currentPresetId.value = taskInfo.taskNodes[0].id;
      currentTaskInfo.value = taskInfo.taskNodes[0];
    }
    const response = await Promise.all(
      uniqueChannelIdList.map(async (channelId) => {
        const res = await getChannelVideoLocationDCS(channelId);
        return res.data;
      })
    );

    console.log("response=====>", response);

    videoplayerRef.value.playVideo(response, currentChannelId.value);
  });
};

const selectTask = async (taskDetail) => {
  console.log("taskDetail=====>", taskDetail);
  // videoplayerRef.value.destroyVideo();
  videoplayerRef.value.clearCanvas();
  currentChannelId.value = taskDetail.channelId;
  currentTaskInfo.value = taskDetail;

  // const url = await getChannelVideoLocationDCS(taskDetail.channelId);
  // videoplayerRef.value.playVideo(url.data);
  videoplayerRef.value.changeVideo(currentChannelId.value);
};

defineExpose({
  loadvideo,
  selectTask,
});
</script>

<style lang="less" scoped></style>
