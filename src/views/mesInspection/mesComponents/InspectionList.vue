<template>
  <div class="inspection-list">
    <a-tree
      :tree-data="taskTree"
      v-model:selectedKeys="selectedTaskKeys"
      block-node
      defaultExpandAll
      show-icon
      @select="selectTree"
      :fieldNames="{ key: 'id', title: 'label' }"
      v-if="taskTree.length"
    >
      <template #title="{ data }">
        <span class="custom-tree-node-title">
          <template v-if="data.isRoot">
            <span style="margin-left: 15px">{{ data.label }}</span>
          </template>
          <template v-else>
            {{ data.label }}
          </template>
        </span>
      </template>
      <template #icon="{ key, selected, type, id, data }">
        <template v-if="data.isRoot">
          <img src="@/assets/videoIcon.png" class="first-icon" />
        </template>
        <template v-else>
          <img src="@/assets/routeIcon.png" class="normal-icon" />
        </template>
      </template>
    </a-tree>
    <a-empty description="暂无数据" v-if="!taskTree.length"></a-empty>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { getMesTaskTreeInterface, getMesThisTaskPoint } from "@/api/project";

const emit = defineEmits(["getTaskScheduleInfo"]); // 定义事件

// ---------------------------------------------------巡检任务树---------------------------------------------
const taskTree = ref<any[]>([]); // 巡检任务树

const selectedTaskKeys = ref<string[]>([]); // 选中的巡检树id

const selectedTaskKeysByTemp = ref<string[]>([]); // 临时选中的巡检树id

// 选中巡检树事件

const selectTree = (selectedKeys, { selected, node }) => {
  if (selected) {
    selectedTaskKeysByTemp.value = selectedKeys;
  } else {
    selectedTaskKeys.value = selectedTaskKeysByTemp.value;
    return;
  }
  if (node.type === "TASK") {
    selectedTaskKeysByTemp.value = selectedKeys;
  } else {
    selectedTaskKeys.value = selectedTaskKeysByTemp.value;
    return;
  }

  getThisTaskinfo(selectedKeys[0]);
};

// 任务列表
const taskTitle = ref<string>("");

// 获取第一个task节点
function findFirstTask(node) {
  if (node.type === "TASK") {
    return node;
  }

  if (node.children && node.children.length > 0) {
    for (const child of node.children) {
      const result = findFirstTask(child);
      if (result) return result;
    }
  }

  return null;
}
// 获取巡检任务列表
const getTaskTree = async () => {
  const res = await getMesTaskTreeInterface();
  res.data.forEach((item, index) => {
    if (index === 0) {
      item.isRoot = true;
    }
  });
  taskTree.value = res.data; // 巡检任务树赋值
  // 初始化选择第一个task节点
  if (taskTree.value.length) {
    const firstTak = findFirstTask(taskTree.value[0]);
    if (firstTak) {
      selectedTaskKeys.value = [firstTak.id];
      getThisTaskinfo(firstTak.id);
    }
  }
};

// 获取当前任务信息
const thisTaskinfo = ref<any[]>();
const getThisTaskinfo = async (id) => {
  const res = await getMesThisTaskPoint(id);
  if (res.code !== 0) return;
  thisTaskinfo.value = res.data;
  emit("getTaskScheduleInfo", res.data);
};

onMounted(() => {
  getTaskTree();
});
</script>

<style lang="less" scoped>
@import "../style/inspectionList.less";
</style>
