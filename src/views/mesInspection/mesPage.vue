<template>
  <a-layout class="container">
    <a-layout-sider theme="light" width="408">
      <div class="list-container">
        <div class="list-title">巡检任务列表</div>
        <div class="tree-container">
          <InspectionList @getTaskScheduleInfo="getTaskScheduleInfo"></InspectionList>
        </div>
      </div>
    </a-layout-sider>
    <a-layout-content>
      <div class="video-container">
        <div class="video-title">视频巡检</div>
        <div class="video-autoOrmulti">
          <a-space>
            <a-button
              :class="[
                'video-autoOrmulti-btn',
                autoOrmulti === 'AUTOMATIC' ? 'video-autoOrmulti-btn-active' : '',
              ]"
              @click="changeAutoOrmulti('AUTOMATIC')"
              >自动</a-button
            >
            <a-button
              :class="[
                'video-autoOrmulti-btn',
                autoOrmulti === 'AUTOMATIC' ? '' : 'video-autoOrmulti-btn-active',
              ]"
              @click="changeAutoOrmulti('MANUAL')"
              >手动</a-button
            >
          </a-space>
        </div>

        <div class="video-showBox" id="showVideoBoxBox">
          <VideoMainArea ref="videoMainAreaRef" @none-task="noneTask" />
        </div>

        <div class="video-inspStepBox">
          <ThumbnailGrid
            ref="ThumbnailGridRef"
            @selectTaskForVideo="selectTaskForVideo"
            :autoOrmulti="autoOrmulti"
          ></ThumbnailGrid>
        </div>
      </div>
    </a-layout-content>
  </a-layout>
  <!-- --------------------------------------------------视频弹窗--------------------------------------- -->
  <Teleport to="body">
    <div class="videoAlarms" v-if="videoAlarmsData.length > 0">
      <Vue3DraggableResizable
        v-for="(item, index) in videoAlarmsData"
        :initW="640"
        :initH="380"
        :x="item.x"
        :y="item.y"
        :draggable="true"
        :resizable="false"
        :parent="false"
        :lock-aspect-ratio="true"
        :style="{ 'z-index': index + 1000 }"
        :key="item.channelId"
        @drag-end="handleDragEnd($event, item.channelId)"
      >
        <div class="videoAlarmsHeader">
          <div class="videoAlarmsTitle">{{ item.alarmName }}</div>
          <div class="closeBtn" @click="closeVideoAlarms(item)">
            <IconFont :type="'icon-incorrect'" />
          </div>
        </div>
        <div class="videoAlarmsBody">
          <videoPlayer
            :ref="(el) => setRefMap(el, item)"
            :show-control="false"
            :channelId="item.channelId"
            :canvasClusterId="'videoAlarmsCanvas'"
          ></videoPlayer>
        </div>
      </Vue3DraggableResizable>
    </div>
  </Teleport>
  <div class="fullScreen" v-if="isFullScreen">
    <videoPlayer
      ref="fullScreenRef"
      :show-control="true"
      :canvasClusterId="'fullScreenCanvas'"
      :showBtn="true"
    ></videoPlayer>
  </div>
</template>

<script setup lang="ts">
import { ref, provide, onMounted, onUnmounted } from "vue";
import InspectionList from "./mesComponents/InspectionList.vue";
import VideoMainArea from "./mesComponents/VideoMainArea.vue";
import ThumbnailGrid from "./mesComponents/ThumbnailGrid.vue";
import HWebsocket from "@/common/h-websocket";
import Vue3DraggableResizable from "vue3-draggable-resizable";
import "vue3-draggable-resizable/dist/Vue3DraggableResizable.css";
import videoPlayer from "@/components/videoElement/videoPlayer.vue";
import { nextTick } from "vue";
import IconFont from "@/components/c-iconfont";
import { getChannelVideoLocationDCS } from "@/api/design";
import { pauseMesTask, resumeMesTask } from "@/api/operatorStation";
import SetInterval from "@/common/SetInterval";
import uEmitter from "@/common/u-event-bus";

const taskInfoData = ref<any>();

// ------------------------------------------------------视频巡检模式--------------------------------------------
const autoOrmulti = ref<string>("AUTOMATIC"); // 自动或手动模式 AUTOMATIC/MANUAL

provide("autoOrmulti", autoOrmulti);
const changeAutoOrmulti = async (value: string) => {
  const taskInfoDataId = taskInfoData.value.id;
  if (value === "AUTOMATIC") {
    const res = await resumeMesTask({ taskId: taskInfoDataId });
    if (res.code !== 0) return;
    autoOrmulti.value = value;
  }
  if (value === "MANUAL") {
    const res = await pauseMesTask({ taskId: taskInfoDataId });
    if (res.code !== 0) return;
    autoOrmulti.value = value;
  }
};

// ------------------------------------------------------接收子组件传递的任务计划信息-----------------------------------
const videoMainAreaRef = ref<any>();

// 任务ws
let ws = null;

function openWs(taskId: string) {
  ws = new HWebsocket(`${(window as any).g.wsApi}/websocket/task-schedule/${taskId}`);
  ws.onMessage = (evt) => {
    const wsData = JSON.parse(evt.data);
    if (wsData.msgType === "PROGRESS") {
      const findTask = wsData.scheduleTaskNodeModels.find((item) => item.status === "RUNNING");
      ThumbnailGridRef.value.autoChangeTask(findTask);
    }
    if (wsData.msgType === "ALARM") {
      const data = {
        channelId: wsData.channelId,
        channelName: wsData.channelName,
      };
      showVideoAlarms(data);
    }
    if (wsData.msgType === "SWITCH_MODE") {
      autoOrmulti.value = wsData.taskMode;
    }
  };
}
// 从子组件InspectionList的emit
const getTaskScheduleInfo = (taskInfo: any) => {
  console.log("getTaskScheduleInfo===>", taskInfo);

  taskInfoData.value = taskInfo;
  videoMainAreaRef.value.loadvideo(taskInfo);
  ThumbnailGridRef.value.receiveTaskList(taskInfo.taskNodes);
  autoOrmulti.value = taskInfo.taskMode;
  if (ws) {
    ws.close();
    ws = null;
  }
  // 打开ws
  nextTick(() => {
    if (taskInfo.taskNodes.length > 0) {
      openWs(taskInfo.id);
    }
  });
};

// 手动切换从子组件ThumbnailGrid的emit
const selectTaskForVideo = (taskDetail: any) => {
  console.log("selectTaskForVideo===>", taskDetail);
  videoMainAreaRef.value.selectTask(taskDetail);
};

const noneTask = () => {
  if (ws) {
    ws.close();
    ws = null;
    console.log("关闭TaskSchedule ws");
  }
};

const isFullScreen = ref(false);
const fullScreenRef = ref<any>();
const fullScreenHandle = (channdId) => {
  isFullScreen.value = !isFullScreen.value;
  nextTick(async () => {
    const url = await getChannelVideoLocationDCS(channdId);
    if (fullScreenRef.value) {
      fullScreenRef.value.playVideo([url.data], channdId);
    }
  });
};
// ------------------------------------------------------子组件ThumbnailGrid/缩略图--------------------------------------------

const ThumbnailGridRef = ref<any>();

// ==============================================报警视频弹窗===========================================
const videoAlarmsData = ref([]);
const refMap = {};
const setRefMap = (el, item) => {
  if (el) {
    refMap[`${item.channelId}`] = el;
  }
};
const showVideoAlarms = async (data) => {
  const findIndex = videoAlarmsData.value.findIndex((item) => item.channelId === data.channelId);
  if (findIndex >= 0) {
    return;
  }
  videoAlarmsData.value.push({
    channelId: data.channelId,
    alarmName: data.channelName,
    x: videoAlarmsData.value.length * 10 + 10,
    y: 550 - videoAlarmsData.value.length * 10,
  });
  nextTick(async () => {
    const item = refMap[data.channelId];
    const url = await getChannelVideoLocationDCS(data.channelId);
    item.playVideo([url.data], data.channelId);
  });
};

const closeVideoAlarms = (element) => {
  const index = videoAlarmsData.value.findIndex((item) => item.channelId === element.channelId);
  videoAlarmsData.value.splice(index, 1);
};

const handleDragEnd = (pos, index) => {
  console.log(pos, index, "handleDragEnd");
  const findIndex = videoAlarmsData.value.findIndex((item) => item.channelId === index);
  if (findIndex >= 0) {
    videoAlarmsData.value[findIndex].x = pos.x;
    videoAlarmsData.value[findIndex].y = pos.y;
  }
};

onMounted(() => {
  SetInterval.close("loginTimer"); // 关闭全局定时器
  uEmitter.on("fullScreen", (data) => {
    // isFullScreen.value = true;
    fullScreenHandle(data);
  });
});
onUnmounted(() => {
  uEmitter.off("fullScreen");
})
</script>

<style scoped lang="less">
@import "./style/mesPage.less";
:global(#app) {
  min-width: 200px !important;
}

.fullScreen {
  position: absolute;
  width: 100vw;
  height: 100vh;
  left: 0;
  top: 0;
  background-color: rgba(0, 0, 0, 1);
  z-index: 9999;
}
</style>
