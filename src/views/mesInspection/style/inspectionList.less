@nodeColor: linear-gradient(0deg, #4b84ffb7 0%, #5c89ff38 100%);
// 树背景样式

.inspection-list{
  height: 100%;
  overflow-y: auto;
}

:deep(.ant-tree) {
  color: #fff;
  background: rgba(0, 0, 0, 0);
}

// 树节点
:deep(.ant-tree-switcher) {
  line-height: 32px;
  width: 12px;
  color: #8ae2ff;
}
:deep(.ant-tree .ant-tree-node-content-wrapper) {
  min-height: 32px;
  line-height: 32px;
  padding: 0px;
}

:deep(.ant-tree .ant-tree-node-content-wrapper .ant-tree-iconEle) {
  vertical-align: baseline;
}
// 节点选中样式
:deep(.ant-tree-treenode-selected) {
  background: @nodeColor;
  border-radius: 4px;
  border: 1px solid #2e53a1;
}

// 节点hover样式重置
:deep(.ant-tree .ant-tree-node-content-wrapper:hover) {
  background: none;
}
:deep(.ant-tree-treenode) {
  border: 1px solid rgba(0, 0, 0, 0);
}
:deep(.ant-tree-treenode:hover) {
  border-radius: 4px;
  border: 1px solid #2e53a1;
  background: @nodeColor;
}
// 节点选中样式重置
:deep(.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected) {
  background: none;
}

.first-icon {
  width: 42px;
  height: 42px;
}

.normal-icon {
  width: 14px;
  height: 14px;
}
