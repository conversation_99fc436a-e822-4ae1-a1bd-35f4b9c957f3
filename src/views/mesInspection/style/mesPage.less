@btn-primary-bg: #0a2a5a;
@btn-selected-bg: #1c4394;
.container {
  position: relative;
  height: 100vh;
  overflow: hidden;
  color: #fff;
  background: linear-gradient(180deg, #011c48 0%, #013576 100%);
}
.ant-layout-sider-light {
  background: rgba(255, 255, 255, 0);
}
.list-container {
  background: url(../../../assets/moduleBackground1.png);
  background-repeat: no-repeat;
  background-position: top center;
  background-size: 100% 100%;
  height: 100%;
  position: relative;
  .list-title {
    font-size: 16px;
    position: absolute;
    top: 9px;
    left: 40px;
    color: #fff;
  }
  .tree-container {
    height: 100%;
    padding: 58px 20px;
  }
}

.video-container {
  background: url(../../../assets/moduleBackground2.png);
  background-repeat: no-repeat;
  background-position: top center;
  background-size: 100% 100%;
  height: 100%;
  position: relative;

  .video-title {
    font-size: 16px;
    position: absolute;
    top: 1%;
    left: 55px;
    color: #fff;
  }
  .video-autoOrmulti {
    position: absolute;
    top: 6px;
    right: 45px;
    color: #fff;

    .video-autoOrmulti-btn {
      width: 80px;
      height: 32px;
      background: @btn-primary-bg;
      color: #fff;
      border: 1px solid #1a418e;
      border-radius: 4px;
    }
    .video-autoOrmulti-btn-active {
      background: @btn-selected-bg;
    }
  }

  .video-showBox {
    width: 98%;
    height: 73%;
    position: absolute;
    top: 6vh;
    left: 15px;
  }

  .video-inspStepBox{
    position: absolute;
    top: 80vh;
    left: 15px;
    width: 98%;
    height: 18vh;
  }
}
::-webkit-scrollbar:horizontal {
  height: 5px;          /* 水平滚动条高度 */
  background-color: #1c52a3; /* 轨道背景色 */
}

::-webkit-scrollbar-thumb:horizontal {   /* 滑块颜色 */
  border-radius: 19px;        /* 滑块圆角 */
  border: 3px solid #4A89E7; /* 滑块边距 */
}

// ---------------------------------------------------视频弹窗样式------------------------------------------------------------

// ============================================弹窗样式===============================
@width: 640px;
@height: 360px;
.videoAlarms {
  color: #fff;
  position: absolute;
  width: 100%;
  height: 100%;
  z-index: 9999;
  top: 0;
  left: 0;
  .videoAlarmsHeader {
    display: flex;
    align-items: center;
    gap: 10px;
    background-color: #063877;
    color: #fff;
    font-size: 16px;
    font-weight: bold;
    border-radius: 5px 5px 0 0;
    height: 20px;
    width: @width;
    padding: 0px 10px 0px 10px;
    .closeBtn {
      font-size: 15px;
      cursor: pointer;
      font-weight: bold;
      text-align: center;
      border-radius: 50%;
      position: absolute;
      right: 5px;
    }
  }
  .videoAlarmsBody {
    position: relative;
    width: @width;
    height: @height;
    background-color: #063877;
  }
}


