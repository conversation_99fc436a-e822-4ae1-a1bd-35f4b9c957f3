<template>
    <div v-if="showPage" style="position: relative; min-height: 500px;">
        <a-tabs v-model:activeKey="activeKey" :centered="true" size="large">
            <a-tab-pane key="backup" tab="备份策略配置" :disabled="true">
                <backupConfig ref="backupRef" />
            </a-tab-pane>
            <a-tab-pane key="time" tab="时间配置" :disabled="true">
                <TimeConfig ref="timeRef"></TimeConfig>
            </a-tab-pane>
            <a-tab-pane key="overall" tab="系统参数配置" :disabled="true">
                <OverallSys ref="overallRef"></OverallSys>
            </a-tab-pane>
        </a-tabs>
        <!-- 跳过按钮 -->
        <a-button
            style="position: fixed; left: 30px; bottom: 30px; z-index: 10;"
            @click="handleSkip"
            size="large"
            type="default"
        >
        跳过此配置
        </a-button>
        <!-- 上一步按钮 -->
        <a-button
            style="position: fixed; right: 250px; bottom: 30px; z-index: 10;"
            type="default"
            size="large"
            @click="handlePrev"
            :disabled="activeKey === tabKeys[0]"
        >
        上一步
        </a-button>
        <!-- 下一步按钮 -->
        <a-button
            style="position: fixed; right: 130px; bottom: 30px; z-index: 10;"
            type="primary"
            size="large"
            :disabled="activeKey === tabKeys[tabKeys.length - 1]"
            @click="handleNext"
        >
        下一步
        </a-button>
        <a-button
            :disabled="!showConfirmBtn"
            style="position: fixed; right: 30px; bottom: 30px; z-index: 10;"
            type="primary"
            size="large"
            @click="handleFinish"
        >
        完成
        </a-button>
    </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { Modal, message } from 'ant-design-vue';
import { setNPT, saveOverConf, finishFirstConfig } from "@/api/project";
import TimeConfig from '@/views/projectPage/configure/system/time/timeConf.vue';
import backupConfig from '@/views/projectPage/configure/system/backup/backupConfig.vue';
import OverallSys from '@/views/projectPage/configure/system/overallSystem/overallSys.vue';
import { useDisabled } from 'element-plus';
const showPage = ref(false);
const showConfirmBtn = ref(false);
const router = useRouter();
const activeKey = ref('backup');
const tabKeys = ['backup', 'time', 'overall'];
const serviceRef = ref();
const backupRef = ref();
const route = useRoute();
const timeRef = ref();
const overallRef = ref();
const validateMap = {
  backup: backupRef,
  time: timeRef,
  overall: overallRef,
};
/**
 * 跳过当前配置
 */
const handleSkip = () => {
  Modal.confirm({
    title: '确认跳过当前配置？',
    content: '跳过后可在系统设置中重新配置，是否继续？',
    okText: '确定',
    cancelText: '取消',
    onOk: () => {
      finishConfig();
    },
  });
};

/**
 * 上一步
 */
const handlePrev = () => {
  const idx = tabKeys.indexOf(activeKey.value);
  if (idx > 0) {
    activeKey.value = tabKeys[idx - 1];
    showConfirmBtn.value = false;
  }
};

/**
 * 下一步
 */
const handleNext = async () => {
  const idx = tabKeys.indexOf(activeKey.value);
  const currentKey = tabKeys[idx];
  try {
    // 在下一步前校验当前表单
    await validateMap[currentKey].value?.validate();
    if (idx < tabKeys.length - 1) {
      activeKey.value = tabKeys[idx + 1];
      showConfirmBtn.value = (idx + 1) === (tabKeys.length - 1); // 到最后一个tab时显示完成按钮
    } else {
      message.info('已经是最后一页');
    }
  } catch (e) {
    message.warn('当前输入格式有误，请检查后再试');
  }
};

/**
 * 完成
 */
const handleFinish = async () => {
  // 校验最后一个表单
  try {
    await overallRef.value?.validate();
  } catch (e) {
    message.warn('当前输入格式有误，请检查后再试');
    return;
  }
  // 获取所有表单的保存参数
  const backupConfig = backupRef.value?.getBackupSaveParmas?.();
  const overallConfig = overallRef.value?.getOverallSaveParmas?.();
  const systemData = { ...overallConfig, ...backupConfig };
  const NTPFormState = timeRef.value.NTPFormState;
    saveOverConf(systemData).then((res) => {
        if (res.code === 0) {
            if (NTPFormState.available) {
              setNPT(NTPFormState).then((res) => {
                if (res.code === 0) {
                    finishConfig();
                }
              });
            } else {
                finishConfig();
            }

        }
    });
};

// 完成配置先告知后端配置完成 然后跳转
const finishConfig = () => {
  finishFirstConfig().then((res) => {
    if (res.code === 0) {
      message.success("操作成功");
      router.push({ name: 'project_design' });
    }
  })
}

onMounted(() => {
  // 判断路由是否有参数
  if (Object.keys(route.query).length > 0 || Object.keys(route.params).length > 0) {
    showPage.value = false; // 有参数，不显示页面
  } else {
    showPage.value = true; // 没有参数，显示页面
  }
});
</script>

<style scoped>
.ant-tabs {
    margin: 10px;
}
</style>