<template>
  <div class="box">
    <div class="btnBox">
      <a-space>
        <a-select
          style="width: 150px"
          v-model:value="searchInfo.searchCondition"
          @change="clearValue"
          :getPopupContainer="(triggerNode) => triggerNode.parentNode"
          dropdownClassName="style-select-dropdown"
        >
          <a-select-option value="channel">通道</a-select-option>
          <a-select-option value="preset">巡检点</a-select-option>
        </a-select>
        <a-input
          v-model:value="channelOrpoint"
          placeholder="请输入通道/巡检点名称"
          style="color: white"
        ></a-input>
        <a-date-picker
          v-model:value="timeFormState.startTime"
          placeholder="开始时间"
          @change="changeStartDate"
          format="YYYY-MM-DD HH:mm:ss"
          :showTime="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }"
          :disabledDate="disabledDate"
        />
        <!-- <el-date-picker
          v-model="timeFormState.startTime"
          type="datetime"
          placeholder="开始时间"
        /> -->
        <a-date-picker
          v-model:value="timeFormState.endTime"
          placeholder="结束时间"
          @change="changeEndData"
          format="YYYY-MM-DD HH:mm:ss"
          :disabledDate="disabledDate"
          :showTime="{ defaultValue: dayjs('00:00:00', 'HH:mm:ss') }"
        />

        <a-button @click="handleSearch" type="primary">查询</a-button>
      </a-space>
    </div>
    <div class="tableClass">
      <a-table
        :columns="columns"
        :dataSource="dataSource"
        rowKey="id"
        :pagination="pagination"
        @change="pageChange"
        :row-class-name="
          (_record, index) => {
            return 'rowStyle';
          }
        "
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'number'">
            <span>{{ index + 1 }}</span>
          </template>
          <template v-if="column.dataIndex === 'model'">
            <a-tag v-if="record.model === 'MANUAL'" color="blue">手动</a-tag>
            <a-tag v-if="record.model === 'SCHEDULE'" color="green">自动</a-tag>
          </template>
          <template v-if="column.dataIndex === 'imgPath'">
            <span v-if="record.imgPath === '-'">-</span>
            <a-dropdown
              :getPopupContainer="(triggerNode) => triggerNode.parentNode"
              overlayClassName="style-select-dropdown"
              v-if="record.imgPath.length === 2"
            >
              <template #overlay>
                <a-menu @click="openPic($event, record)" class="menu">
                  <a-menu-item key="0" class="menuItem"> 首次报警截图 </a-menu-item>
                  <a-menu-item key="1" class="menuItem"> 最新报警截图 </a-menu-item>
                </a-menu>
              </template>
              <a>
                查看截图
                <DownOutlined />
              </a>
            </a-dropdown>
            <a v-else @click="openPic({ key: 0 }, record)">查看截图</a>
          </template>
          <!-- <template v-if="column.dataIndex === 'result'">
            <a-tag v-if="record.isAlarm && record.status === 200" color="#f50">报警</a-tag>
            <a-tag v-else-if="!record.isAlarm && record.status === 200" color="#87d068">正常</a-tag>
            <a-span v-if="record.status !== 200">{{ record.failureReason }}</a-span>
          </template> -->
        </template>
        <template #emptyText>
          <div style="color: white; font-size: large">暂无数据</div>
        </template>
      </a-table>
    </div>
    <Teleport to="body">
      <PicModal ref="modelParent" @closeModal="removeKeydown">
        <template #body>
          <img :src="imgInfo.url" style="width: 960px; height: 540px" alt="暂无图片" />
        </template>
      </PicModal>
    </Teleport>
  </div>
</template>

<script lang="ts">
import { defineComponent, reactive, ref, computed, onMounted, onBeforeUnmount } from "vue";
import type { ColumnsType } from "ant-design-vue/es/table/interface";
import dayjs, { Dayjs } from "dayjs";
import { inspectionRecordList, uploadPic } from "@/api/operatorStation";
import { message } from "ant-design-vue";
import SetInterval from "@/common/SetInterval";
import PicModal from "@/components/PicModal.vue";
import { LoadingOutlined, DownOutlined } from "@ant-design/icons-vue";

import { ElDatePicker } from "element-plus";

export default defineComponent({
  components: {
    PicModal,
    DownOutlined,
    ElDatePicker,
  },
  setup() {
    const modelParent = ref(null);
    const columns: ColumnsType = [
      {
        title: "编号",
        dataIndex: "number",
      },
      {
        title: "时间",
        dataIndex: "time",
      },
      {
        title: "类型",
        dataIndex: "model",
      },
      {
        title: "通道",
        dataIndex: "channelName",
      },
      {
        title: "巡检点",
        dataIndex: "presetName",
      },
      {
        title: "识别结果",
        dataIndex: "result",
        width: 200,
      },

    ];
    const dataSource = ref();
    const searchInfo = reactive({
      endTime: "",
      startTime: "",
      channelName: "",
      presetName: "",
      searchCondition: "channel",
    });
    // 输入框的值
    const channelOrpoint = ref("");
    const timeFormState = reactive({
      startTime: "",
      endTime: "",
    });
    const changeStartDate = (date) => {
      if (dayjs(date).unix() > dayjs(timeFormState.endTime).unix()) {
        timeFormState.startTime = "";
      }
    };
    const changeEndData = (date) => {
      if (dayjs(date).unix() < dayjs(timeFormState.startTime).unix()) {
        timeFormState.endTime = "";
      }
    };
    // 限制不可选日期
    const disabledDate = (current: Dayjs) => {
      const currentDate = dayjs();
      const selectedDate = dayjs(current);
      const daysDiff = selectedDate.diff(currentDate, "day");

      if (daysDiff >= -30) {
        return false;
      } else {
        return true;
      }
    };
    // 切换搜索条件
    const clearValue = () => {
      channelOrpoint.value = "";
      searchInfo.presetName = "";
      searchInfo.channelName = "";
    };
    const handleSearch = () => {
      if (timeFormState.startTime) {
        searchInfo.startTime = dayjs(timeFormState.startTime).format("YYYY-MM-DD HH:mm:ss");
      } else {
        searchInfo.startTime = "";
      }
      if (timeFormState.endTime) {
        searchInfo.endTime = dayjs(timeFormState.endTime).format("YYYY-MM-DD HH:mm:ss");
      } else {
        searchInfo.endTime = "";
      }

      if (searchInfo.searchCondition === "channel") {
        searchInfo.channelName = channelOrpoint.value;
      }
      if (searchInfo.searchCondition === "preset") {
        searchInfo.presetName = channelOrpoint.value;
      }
      getList();
      // console.log(dayjs(searchInfo.startTime).format('YYYY-MM-DD HH:mm:ss'));
    };
    // 分页
    const tableData = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      currentPageSize: 0, // 当前页面有几条数据
      tableList: [],
    });
    const pagination = computed(() => ({
      total: tableData.total,
      current: tableData.current, //
      pageSize: 10,
      showTotal: () => `共 ${tableData.total} 条`,
      showLessItems: true,
      defaultPageSize: tableData.pageSize,
      showSizeChanger: false,
    }));
    const pageChange = ({ current, total }) => {
      tableData.current = current;
      getList();
    };
    const getList = () => {
      const pageData = {
        size: pagination.value.pageSize,
        current: pagination.value.current,
      };
      const data = Object.assign(pageData, searchInfo);
      inspectionRecordList(data, searchInfo.searchCondition).then((res) => {
        if (res.code === 0) {
          dataSource.value = res.data.records;
          tableData.total = res.data.total;
        }
      });
    };
    const errorProcess = (key, record) => {
      return new Promise<void>((resolve, reject) => {
        const img = new Image();
        img.src = record.imgPath[key];
        img
          .decode()
          .then(() => {
            resolve();
          })
          .catch((err) => {
            reject();
          });
      });
    };
    const openPic = ({ key }, record) => {
      errorProcess(key, record)
        .then(() => {
          imgInfo.title = `${record.presetName}截图`;
          imgInfo.url = record.imgPath[key];
          modelParent.value.show = true;
          document.onkeydown = function (e) {
            if (e.altKey && (e.key === "U" || e.key === "u")) {
              const data = {
                presetId: record.presetId as string,
                proxyPath: record.imgPath[key] as string,
              };
              uploadPic(data).then((res) => {
                if (res.code === 0) {
                  message.success("操作成功", 2);
                }
              });
            }
          };
        })
        .catch((err) => {
          message.destroy();
          message.error("截图无法预览", 2);
        });
    };
    const removeKeydown = () => {
      document.onkeydown = null;
    };

    const imgInfo = reactive({
      title: "截图",
      url: "",
    });

    SetInterval.add(
      "intervalUse",
      () => {
        getList();
      },
      10000
    );
    onMounted(() => {
      getList();
      // SetInterval.run('intervalUse');
    });
    onBeforeUnmount(() => {
      SetInterval.close("intervalUse");
    });
    return {
      modelParent,
      dayjs,
      columns,
      dataSource,
      imgInfo,
      openPic,
      removeKeydown,
      pagination,
      pageChange,
      searchInfo,
      timeFormState,
      changeEndData,
      changeStartDate,
      disabledDate,
      channelOrpoint,
      handleSearch,
      clearValue,
    };
  },
});
</script>

<style scoped>
.box {
  margin-top: 20px;
  width: 90%;
  margin-left: 5%;
}
.btnBox {
  margin-bottom: 10px;
}
/* 表格 */
:deep(.ant-table) {
  background: #06122e;
  color: white;
}

:deep(.ant-table-row) {
  background: #112138;
}
:deep(.ant-empty-normal) {
  color: white;
}
:deep(.ant-table-thead > tr > th) {
  background: #112138;
  color: #4cb5c4;
  border-bottom: 1px solid rgb(1, 97, 161);
}

/* :deep(.tableClass .ant-table-body tr:hover:not(.ant-table-expanded-row) > td,
.ant-table-row-hover,
.ant-table-row-hover > td) {
    background: #112138 !important;
 } */

/* :deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  background: #1b317d;
} */

:deep(.rowStyle) td {
  border-bottom: 1px solid rgb(1, 97, 161);
  transition: none;
  background: #112138;
}

:deep(.rowStyle:hover > td) {
  background-color: #1b317d !important;
}

:deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
  background: #1b317d !important;
  transition: 0.3s !important;
}

:deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid #03548d !important;
  background: #06122e;
}

:deep(.ant-table-tbody > tr.ant-table-placeholder:hover > td) {
  background-color: #06122e !important;
}

:deep(.ant-table-body)::-webkit-scrollbar {
  width: 5px !important;
}
:deep(.ant-table-body)::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgb(6, 56, 119);
}
:deep(.ant-table-body)::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}

/* 分页 */
:deep(.ant-pagination) {
  color: white;
}

:deep(.ant-pagination-item-active) {
  background-color: #1b317d !important;
  border: 1px solid #4cb5c4 !important;
}
:deep(.ant-pagination-item) {
  border: none;
  background-color: #1b317d !important;
}
:deep(.ant-pagination-item a) {
  color: #fff !important;
}
:deep(.ant-pagination-prev .ant-pagination-item-link) {
  background: rgba(0, 0, 0, 0);
  color: #4cb5c4;
  border: none !important;
}

:deep(.ant-pagination-next .ant-pagination-item-link) {
  background: rgba(0, 0, 0, 0) !important;
  color: #4cb5c4;
  border: none !important;
}
:deep(.ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis) {
  color: #2e6e7d !important;
}
:deep(.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis) {
  color: #2e6e7d !important;
}
:deep(.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  background-color: rgb(6, 56, 119) !important;
  border: 1px solid rgb(1, 97, 161);
  color: #fff;
}

/* 时间选择器 */
:deep(.ant-picker) {
  background-color: #1b317d;
  border: 1px solid rgb(1, 97, 161);
}
:deep(.ant-picker-input > input) {
  color: white;
}

:deep(.ant-input) {
  background-color: #1b317d;
  border: 1px solid rgb(1, 97, 161);
}

/* 选择器 */
:deep(.ant-select:not(.ant-select-customize-input) .ant-select-selector) {
  background-color: rgb(6, 56, 119) !important;
  border: 1px solid rgb(1, 97, 161);
  color: #fff;
}

:deep(.style-select-dropdown) {
  background-color: rgb(6, 56, 119) !important;
}

:deep(.ant-select-item) {
  background-color: rgb(6, 56, 119) !important;
  color: #fff !important;
}

:deep(.ant-select-item-option-active) {
  background-color: rgb(8, 100, 212) !important;
  color: #fff !important;
}

:deep(.ant-picker-clear) {
  background-color: #1b317d;
  color: rgba(255, 255, 255, 0.25);
}

:deep(.menu) {
  background-color: rgb(6, 56, 119) !important;
}

:deep(.menuItem) {
  background-color: rgb(6, 56, 119) !important;
  color: #fff;
}

:deep(.menuItem:hover) {
  background-color: #0864d4 !important;
}

.img {
  margin-top: 80px;
  margin-left: 25px;
}
</style>
