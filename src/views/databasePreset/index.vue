<template>
    <div v-if="showContent" style="position: relative; min-height: 500px;">
        <a-tabs v-model:activeKey="activeKey" :centered="true" size="large">
            <a-tab-pane key="dbase" tab="数据底座配置" :disabled="true">
                <DatabaseConfig ref="dbaseRef" />
            </a-tab-pane>
        </a-tabs>
        <a-button
            style="position: fixed; right: 30px; bottom: 30px; z-index: 10;"
            :loading="loading"
            type="primary"
            size="large"
            @click="handleFinish"
        >
        完成
        </a-button>
    </div>
</template>
<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { useRouter } from 'vue-router';
import { message } from 'ant-design-vue';
import { saveOverConf, checkDbaseLogin, checkDbaseClientInfo } from "@/api/project";
import DatabaseConfig from '@/views/projectPage/configure/system/database/databaseConfig.vue';

const router = useRouter();
const activeKey = ref('dbase');
const dbaseRef = ref();
const loading = ref(false);
const showContent = ref(false); // 控制内容是否显示

onMounted(() => {
  // 延迟 3 秒后显示内容
  setTimeout(() => {
    showContent.value = true;
  }, 1500);
});
/**
 * 完成
 */
const handleFinish = async () => {
    try {
        await dbaseRef.value?.validate();
    } catch (e) {
        message.warn('当前输入格式有误，请检查后再试');
        return;
    }
    loading.value = true;
    // 获取所有表单的保存参数
    const serviceConfig = dbaseRef.value?.getServiceSaveParmas?.();
    // 校验登录ip
    const checkDbResult = await checkDbaseLogin(serviceConfig.dataBaseConfig?.dbBaseServerIp)
    if (checkDbResult.code !== 0) {
        loading.value = false;
        return;
    }
    // 校验登录用户名密钥
    const checkDbClient = await checkDbaseClientInfo(serviceConfig.dataBaseConfig?.dbBaseClientId, 
                                                    serviceConfig.dataBaseConfig?.dbBaseSecret, serviceConfig.dataBaseConfig?.dbBaseServerIp);
    if (checkDbClient.code !== 0) {
        loading.value = false;
        return;
    }
    saveOverConf(serviceConfig).then((res) => {
        if (res.code === 0) {
            message.success("操作成功");
            router.push({ name: 'project_design' });
            window.location.reload();
        }
    });
    loading.value = false;
};
</script>

<style scoped>
.ant-tabs {
    margin: 10px;
}
</style>