<template>
    <div>
        <!-- <a href="@/assets/hollysysLogo.png" download  id="download">ada</a> -->
        <img src="/hollysysLogo.png" alt="暂无图片"/>
    </div>
  </template>

  <script lang="ts">
  import { defineComponent, onMounted, reactive, ref } from 'vue';

  export default defineComponent({
    setup() {
      onMounted(()=>{
        // const a = document.createElement('a')
        // a.href = '/hollysysLogo.png'
        // a.download = 'hollysysLogo.png'
        // a.style.display = 'none'
        // document.body.appendChild(a)
        // a.click()
        // a.remove()
      })

      return {

      };
    },
  });
  </script>

  <style scoped>
  </style>
