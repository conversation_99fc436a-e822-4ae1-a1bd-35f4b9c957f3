<template>
  <!-- 根盒子  -->
  <div class="screen" id="scaleBox">
    <el-config-provider :locale="zhCn">
      <div class="listBox">
        <div class="flexTree">
          <a-card :bordered="true">
            <div id="treeBox">
              <a-dropdown :trigger="['click']">
                <a-input-search
                  v-model:value="searchValue"
                  style="margin-bottom: 8px"
                  placeholder="输出节点"
                  @change="searchTree"
                ></a-input-search>
                <template #overlay>
                  <a-menu v-if="searchList.length">
                    <a-menu-item
                      v-for="item in searchList"
                      :key="item.key"
                      @click="handleSelect(item)"
                    >
                      {{ item.label }}
                    </a-menu-item>
                  </a-menu>
                  <a-empty description="" v-else></a-empty>
                </template>
              </a-dropdown>
              <a-tree
                style="text-align: left"
                :tree-data="treeData"
                v-model:selectedKeys="selectedkeys"
                block-node
                defaultExpandAll
                @select="selectTree"
                class="projectTree"
                :fieldNames="{ key: 'id', title: 'label' }"
                :autoExpandParent="autoExpandParent"
                v-model:expanded-keys="expandedKeys"
                @expand="expandNode"
                v-if="treeData.length"
              >
                <template #title="{ data }">
                  <span style="font-size: 14px">
                    <div :id="'tree-node_' + data.id" class="node-title">
                      <IconFont
                        :type="IconList[data.type]"
                        v-if="data.type !== 'channel'"
                        class="iconClass"
                      />
                      <IconFont
                        type="icon-qiuji"
                        v-if="data.type === 'channel' && data.deviceType.includes('PTZ')"
                        class="iconClass"
                      />
                      <IconFont
                        type="icon-a-kakouqiangshishexiangtoujiankongqiangshishexiangtou"
                        v-if="data.type === 'channel' && !data.deviceType.includes('PTZ')"
                        class="iconClass"
                      />
                      <a-tooltip placement="topLeft">
                        <template #title>
                          {{ !data.des ? data.label : data.des }}
                        </template>

                        <span
                          :ref="(el) => (nodeRefs[data.id] = el)"
                          :class="{ searchBack: searchKey === data.id }"
                          >{{ data.label }}</span
                        >
                      </a-tooltip>
                      <span
                        v-if="
                          (data.type === 'directory' || data.type === 'business-root') &&
                          getTotalChannelCount(data) !== 0
                        "
                        style="margin-left: 5px"
                      >
                        (<span class="area">{{ getOnlineChannelCount(data) }}</span> /
                        {{ getTotalChannelCount(data) }})
                      </span>
                    </div>
                  </span>
                </template>
              </a-tree>
            </div>
          </a-card>
        </div>

        <div class="flexTable">
          <!-- <a-card style="background-color: #f5f5f5;" :bordered="false"> -->
          <a-tabs v-model:activeKey="currentAlarm" @change="changeListTabs">
            <a-tab-pane key="realAlarm" tab="实时报警">
              <a-space style="margin-bottom: 10px">
                <el-date-picker
                  v-model="listParams.time"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="首次报警开始日期"
                  end-placeholder="首次报警结束日期"
                  @change="changeTime"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  size="small"
                >
                </el-date-picker>
                <a-input
                  v-model:value="listParams.searchStr"
                  placeholder="请输入视频通道/预置点/算法实例"
                  allow-clear
                  style="width: 250px"
                  size="small"
                ></a-input>
                <a-button type="primary" @click="searchAlarm()" size="small">搜索</a-button>
              </a-space>
              <a-table
                :columns="columns"
                :dataSource="dataSource"
                :pagination="pagination"
                @change="pageChange"
                :row-class-name="getRowClass"
                :scroll="{ x: 100 }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === 'confirm'">
                    <CheckCircleFilled
                      v-if="record.confirm"
                      class="confirmIconStyle"
                    ></CheckCircleFilled>
                    <CloseCircleFilled v-else class="unconfirmIconStyle"></CloseCircleFilled>
                  </template>
                  <template v-if="column.dataIndex === 'comfirmBtn'">
                    <a-space :size="20">
                      <a-button
                        type="primary"
                        @click="comfirmAlarm(record.id, record.confirm)"
                        size="small"
                        style="width: 80px"
                        :disabled="record.confirm"
                      >
                        {{ "确认报警" }}
                      </a-button>
                      <a-button
                        type="primary"
                        @click="falseAlarm(record)"
                        size="small"
                        style="width: 80px"
                      >
                        {{ "误报" }}
                      </a-button>
                      <a-dropdown>
                        <template #overlay>
                          <a-menu @click="displayImage($event, record, 'resultImgs')">
                            <a-menu-item key="0"> 首次报警截图 </a-menu-item>
                            <a-menu-item key="1" v-if="record.resultImgs.length === 2">
                              最新报警截图
                            </a-menu-item>
                          </a-menu>
                        </template>
                        <a style="color: #fff">
                          查看截图
                          <DownOutlined />
                        </a>
                      </a-dropdown>
                      <a-button @click="showVideo(record)" size="small" type="primary"
                        >查看视频</a-button
                      >
                    </a-space>
                  </template>
                  <template v-if="column.dataIndex === 'channelName'">
                    <a-tooltip placement="right">
                      <template #title>
                        {{ record.channelName }}
                      </template>
                      <span>{{
                        record.channelName.length > 6
                          ? record.channelName.slice(0, 6) + "..."
                          : record.channelName
                      }}</span>
                    </a-tooltip>
                  </template>

                  <template v-if="column.dataIndex === 'presetName'">
                    <a-tooltip placement="right">
                      <template #title>
                        {{ record.presetName }}
                      </template>
                      <span>{{
                        record.presetName.length > 12
                          ? record.presetName.slice(0, 12) + "..."
                          : record.presetName
                      }}</span>
                    </a-tooltip>
                  </template>

                  <!-- <template v-if="column.dataIndex === 'type'">
                    <a-tooltip placement="right">
                      <template #title>
                        {{ record.type }}
                      </template>
                      <span>{{
                        record.type.length > 5 ? record.type.slice(0, 5) + "..." : record.type
                      }}</span>
                    </a-tooltip>
                  </template> -->
                </template>
              </a-table>
            </a-tab-pane>
            <a-tab-pane key="hisAlarm" tab="历史报警">
              <a-space style="margin-bottom: 10px">
                <el-date-picker
                  v-model="hisListParams.time"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="首次报警开始日期"
                  end-placeholder="首次报警结束日期"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  size="small"
                  @change="changeHisTime"
                >
                </el-date-picker>
                <a-input
                  placeholder="请输入视频通道/预置点/算法实例"
                  v-model:value="hisSearchValue"
                  allow-clear
                  style="width: 250px"
                  size="small"
                />
                <a-select
                  v-model:value="hisListParams.confirm"
                  style="width: 100px"
                  size="small"
                  :allowClear="true"
                  placeholder="是否确认"
                >
                  <a-select-option value="true">是</a-select-option>
                  <a-select-option value="false">否</a-select-option>
                </a-select>
                <a-select
                  v-model:value="hisListParams.falseAlarm"
                  style="width: 100px"
                  size="small"
                  :allowClear="true"
                  placeholder="是否误报"
                >
                  <a-select-option value="true">是</a-select-option>
                  <a-select-option value="false">否</a-select-option>
                </a-select>
                <a-button type="primary" @click="searchHisAlarm" size="small">搜索</a-button>
              </a-space>
              <a-table
                :columns="historyCol"
                :dataSource="hisDataSource"
                :pagination="hisPgination"
                @change="hisPageChange"
                :row-class-name="'confirmedRowStyle'"
                :scroll="{ x: 100 }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === 'comfirmBtn'">
                    <a-space :size="20">
                      <!-- <a-button type="primary" @click="comfirmAlarm(record.id, record.confirm)" size="small" style="width: 80px;" :disabled="record.confirm">
                              {{ '确认' }}
                            </a-button> -->
                      <a-dropdown>
                        <template #overlay>
                          <a-menu @click="displayImage($event, record, 'resultImgs')">
                            <a-menu-item key="0"> 首次报警截图 </a-menu-item>
                            <a-menu-item key="1" v-if="record.resultImgs.length === 2">
                              最新报警截图
                            </a-menu-item>
                          </a-menu>
                        </template>
                        <a style="color: #fff">
                          查看截图
                          <DownOutlined />
                        </a>
                      </a-dropdown>
                      <!-- <a-button @click="showVideo(record.channelId)" size="small" type="primary">查看视频</a-button> -->
                    </a-space>
                  </template>

                  <template v-if="column.dataIndex === 'channelName'">
                    <a-tooltip placement="right">
                      <template #title>
                        {{ record.channelName }}
                      </template>
                      <span>{{
                        record.channelName.length > 6
                          ? record.channelName.slice(0, 6) + "..."
                          : record.channelName
                      }}</span>
                    </a-tooltip>
                  </template>

                  <template v-if="column.dataIndex === 'presetName'">
                    <a-tooltip placement="right">
                      <template #title>
                        {{ record.presetName }}
                      </template>
                      <span>{{
                        record.presetName.length > 8
                          ? record.presetName.slice(0, 8) + "..."
                          : record.presetName
                      }}</span>
                    </a-tooltip>
                  </template>

                  <template v-if="column.dataIndex === 'confirm'">
                    <span v-if="record.confirm">是</span>
                    <span v-else>否</span>
                  </template>

                  <template v-if="column.dataIndex === 'falseAlarm'">
                    <span v-if="record.falseAlarm">是</span>
                    <span v-else>否</span>
                  </template>

                  <!-- <template v-if="column.dataIndex === 'type'">
                    <a-tooltip placement="right">
                      <template #title>
                        {{ record.type }}
                      </template>
                      <span>{{
                        record.type.length > 4 ? record.type.slice(0, 4) + "..." : record.type
                      }}</span>
                    </a-tooltip>
                  </template> -->
                </template>
              </a-table>
            </a-tab-pane>
            <!-- -----------------------------------------------------------------预置点执行记录- ----------------------------------------------->

            <a-tab-pane key="preset" tab="预置点执行记录" v-if="isShowPreset">
              <a-space style="margin-bottom: 10px">
                <el-date-picker
                  v-model="presetListParams.time"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  @change="changePresetTime"
                  size="small"
                >
                </el-date-picker>
                <a-input
                  placeholder="请输入通道名或预置点"
                  v-model:value="presetListParams.searchStr"
                  allow-clear
                  size="small"
                />
                <a-button type="primary" @click="searchPreset()" size="small">搜索</a-button>
              </a-space>
              <a-table
                :dataSource="presetData"
                :pagination="presetPgination"
                @change="presetPageChange"
                :columns="presetCol"
                :row-class-name="'confirmedRowStyle'"
                :scroll="{ x: 100 }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === 'channelName'">
                    <a-tooltip placement="right">
                      <template #title>
                        {{ record.channelName }}
                      </template>
                      <span>{{
                        record.channelName.length > 6
                          ? record.channelName.slice(0, 6) + "..."
                          : record.channelName
                      }}</span>
                    </a-tooltip>
                  </template>

                  <template v-if="column.dataIndex === 'presetName'">
                    <a-tooltip placement="right">
                      <template #title>
                        {{ record.presetName }}
                      </template>
                      <span>{{
                        record.presetName.length > 8
                          ? record.presetName.slice(0, 8) + "..."
                          : record.presetName
                      }}</span>
                    </a-tooltip>
                  </template>
                </template>
              </a-table>
            </a-tab-pane>
            <a-tab-pane key="failureAlarm" tab="算法执行记录" v-if="isShowPreset">
              <a-space style="margin-bottom: 10px">
                <el-date-picker
                  v-model="executeListParams.time"
                  type="datetimerange"
                  range-separator="至"
                  start-placeholder="开始日期"
                  end-placeholder="结束日期"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  size="small"
                  @change="changeExTime"
                >
                </el-date-picker>
                <a-input
                  placeholder="请输入通道名或预置点"
                  v-model:value="executeListParams.searchStr"
                  allow-clear
                  size="small"
                />
                <a-button type="primary" @click="searchExecute()" size="small">搜索</a-button>
              </a-space>
              <a-table
                :columns="executeCol"
                :dataSource="executeData"
                :pagination="executePgination"
                @change="executePageChange"
                :row-class-name="'confirmedRowStyle'"
                :scroll="{ x: 100 }"
              >
                <template #bodyCell="{ column, record }">
                  <template v-if="column.dataIndex === 'inputImages'">
                    <a-space :size="20">
                      <a-dropdown>
                        <template #overlay>
                          <a-menu @click="displayImage($event, record, 'inputImages')">
                            <a-menu-item v-for="(item, index) in record.inputImages" :key="index">
                              图片{{ index + 1 }}
                            </a-menu-item>
                          </a-menu>
                        </template>
                        <a style="color: #fff">
                          查看截图
                          <DownOutlined />
                        </a>
                      </a-dropdown>
                      <!-- <a-button @click="showVideo(record.channelId)" size="small" type="primary">查看视频</a-button> -->
                    </a-space>
                  </template>

                  <template v-if="column.dataIndex === 'outputImage'">
                    <a
                      @click="displayImage({ key: 0 }, record, 'outputImage')"
                      v-if="record.outputImage"
                      >查看算法输出</a
                    >
                    <span style="color: #999" v-else>查看算法输出</span>
                  </template>

                  <template v-if="column.dataIndex === 'channelName'">
                    <a-tooltip placement="right">
                      <template #title>
                        {{ record.channelName }}
                      </template>
                      <span>{{
                        record.channelName.length > 6
                          ? record.channelName.slice(0, 6) + "..."
                          : record.channelName
                      }}</span>
                    </a-tooltip>
                  </template>

                  <template v-if="column.dataIndex === 'presetName'">
                    <a-tooltip placement="right">
                      <template #title>
                        {{ record.presetName }}
                      </template>
                      <span>{{
                        record.presetName.length > 8
                          ? record.presetName.slice(0, 8) + "..."
                          : record.presetName
                      }}</span>
                    </a-tooltip>
                  </template>

                  <!-- <template v-if="column.dataIndex === 'type'">
                    <a-tooltip placement="right">
                      <template #title>
                        {{ record.type }}
                      </template>
                      <span>{{
                        record.type.length > 4 ? record.type.slice(0, 4) + "..." : record.type
                      }}</span>
                    </a-tooltip>
                  </template> -->
                </template>
              </a-table>
            </a-tab-pane>
          </a-tabs>
          <!-- </a-card> -->
        </div>
      </div>
    </el-config-provider>
  </div>
  <Teleport to="#scaleBox">
    <Transition>
      <div v-if="picModal" class="modal-mask">
        <Vue3DraggableResizable
          :initW="picWidth"
          :initH="picHeight"
          :x="picX"
          :y="picY"
          :draggable="true"
          :resizable="false"
          :parent="true"
        >
          <div style="display: flex">
            <span style="color: white">{{ videoTitle }}</span>
            <div style="margin-left: auto">
              <CloseCircleOutlined
                class="closeVideoBtn"
                @click="closeVideoModal"
              ></CloseCircleOutlined>
            </div>
          </div>
          <img :src="imgInfo.url" style="width: 960px; height: 540px" alt="暂无图片" />
        </Vue3DraggableResizable>
      </div>
    </Transition>
  </Teleport>
  <Teleport to="#scaleBox">
    <Transition>
      <div v-if="videoModal" class="modal-mask">
        <!-- <div style="width: 30px;height: 50px;margin-left: auto;margin-top: 5px;">
          <CloseCircleOutlined  style="color: #ffffff;font-size: 14px;cursor: pointer;" @click="closeVideoModal"></CloseCircleOutlined>
        </div> -->
        <Vue3DraggableResizable
          :initW="videoWidth"
          :initH="videoHeight"
          :x="videoX"
          :y="videoY"
          :draggable="true"
          :resizable="true"
          :parent="true"
          @resize-end="resizeEnd"
          :lock-aspect-ratio="true"
        >
          <div style="width: 100%; height: 100%">
            <div style="display: flex; height: 5%">
              <span style="color: white">{{ videoTitle }}</span>
              <div style="margin-left: auto">
                <CloseCircleOutlined
                  class="closeVideoBtn"
                  @click="closeVideoModal"
                ></CloseCircleOutlined>
              </div>
            </div>
            <div style="position: relative; width: 100%; height: 95%">
              <videoPlayer ref="video"></videoPlayer>
            </div>
          </div>
        </Vue3DraggableResizable>
      </div>
    </Transition>
  </Teleport>
</template>
<script lang="ts">
import {
  computed,
  defineComponent,
  nextTick,
  onMounted,
  reactive,
  ref,
  onUnmounted,
  watch,
} from "vue";
import scaleBox from "@/components/scaleBox.vue";
import IconFont from "@/components/c-iconfont";
import { IconList } from "@/components/datatypeComponent";
import { getProjectForDCS } from "@/api/project";
import { getOnlineChannelCount, getTotalChannelCount } from "@/common/utils";
import { getChannelVideoLocationDCS, getProjectTreeForDCS } from "@/api/design";
import {
  comfirmAlarmForDcs,
  getAlarmTableForDcs,
  getAlarmTableForDcsHis,
  getExecuteListForDcs,
  getPresetListForDcs,
  falseAlarmForDcs,
} from "@/api/operatorStation";
import {
  CheckCircleFilled,
  CloseCircleFilled,
  DownOutlined,
  CloseCircleOutlined,
  WindowsFilled,
} from "@ant-design/icons-vue";
import { zhCn } from "element-plus/es/locale";
import videoPlayer from "@/components/videoElement/videoPlayer.vue";
import Vue3DraggableResizable from "vue3-draggable-resizable";
import "vue3-draggable-resizable/dist/Vue3DraggableResizable.css";
import SetInterval from "@/common/SetInterval";

import disableScaling from "@/common/disableScaling";

import { ElConfigProvider, ElDatePicker } from "element-plus";
import { message, Modal } from "ant-design-vue";
import { isEmpty } from "@/common/utils";

interface TableType {
  confirm: boolean;
  resultImgs: string[];
  channelName: string | number;
  presetName: string;
  createTime: string;
  confirmTime: string;
  updateTime: string;
  id: string | number;
  algorithmId?: string;
  alarmLevel: string;
  levelValue: string | number;
  channelId: string;
  outputParamName: string;
  algorithmInstanceName: string;
}

export default defineComponent({
  components: {
    scaleBox,
    IconFont,
    DownOutlined,
    CheckCircleFilled,
    CloseCircleFilled,
    CloseCircleOutlined,
    Vue3DraggableResizable,
    ElConfigProvider,
    ElDatePicker,
    videoPlayer,
  },
  setup() {
    // ----------------------------------------------------------- 工程树 ---------------------------------------------
    const treeData = ref<any[]>([]); // 树结构书数据
    const dataList = ref([]);
    const expandedKeys = ref<(string | number)[]>([]);
    const selectedkeys = ref<string[] | number[]>([]); // 树结构选中节点
    const tempSelectedkeys = ref<string[] | number[]>([]); // 树结构选中节点
    /**
     *
     * @param selectedKeys 选择的key
     * @param selectedNodes 选择的节点信息
     */
    const selectTree = (selectedKey, { selected, selectedNodes }) => {
      if (!selected) {
        selectedkeys.value = tempSelectedkeys.value;
        return;
      }
      selectedkeys.value = selectedKey;
      tempSelectedkeys.value = selectedKey;
      switch (currentAlarm.value) {
        case "realAlarm":
          listParams.current = 1;
          listParams.size = 20;
          getAlarmTable();
          break;
        case "hisAlarm":
          hisListParams.current = 1;
          hisListParams.size = 20;
          getHisAlarmTable();
          break;
        case "failureAlarm":
          executeListParams.current = 1;
          executeListParams.size = 20;
          getExecuteList();
          break;
        case "preset":
          presetListParams.current = 1;
          presetListParams.size = 20;
          getPresetList();
          break;
        default:
          break;
      }
    };

    // 过滤树中工程师站、操作员站、控制流节点
    const filterTree = (data: any[]) => {
      data.forEach((item) => {
        item.children = item.children.filter(
          (item) => !["ops", "ens", "control-flow"].includes(item.type)
        );
      });
      const tree = data;
      return tree;
    };

    // 请求工程列表
    const getProjectList = () => {
      return new Promise((resolve, reject) => {
        const data = {
          size: 20,
          current: 1,
        };
        getProjectForDCS(data).then((res) => {
          if (res.code === 0) {
            getProjectTreeForDCS().then((res1) => {
              treeData.value = filterTree(res1.data.tree);
              // 选中第一个工程
              resolve(treeData.value[0].id);

              generateList(treeData.value);

              //展开所有
              autoExpandParent.value = true;
              expandedKeys.value = dataList.value.map((item) => item.key);
            });
          }
        });
      });
    };

    // -------------------------------------------------- 报警列表tabs ----------------------------------------

    const currentAlarm = ref<string>("realAlarm");
    const isShowPreset = ref<boolean>(true);

    const changeListTabs = (key) => {
      switch (key) {
        case "realAlarm":
          listParams.current = 1;
          listParams.size = 20;
          listParams.time = "";
          listParams.searchStr = "";
          getAlarmTable();
          SetInterval.pause("hisAlarmList");
          SetInterval.pause("executeList");
          SetInterval.pause("presetList");
          SetInterval.run("alarmList");
          break;
        case "hisAlarm":
          hisListParams.current = 1;
          hisListParams.size = 20;
          hisListParams.time = "";
          hisSearchValue.value = "";
          getHisAlarmTable();
          SetInterval.pause("alarmList");
          SetInterval.pause("executeList");
          SetInterval.pause("presetList");
          SetInterval.run("hisAlarmList");
          break;
        case "failureAlarm":
          executeListParams.current = 1;
          executeListParams.size = 20;
          executeListParams.time = "";
          executeListParams.searchStr = "";
          getExecuteList();
          SetInterval.pause("alarmList");
          SetInterval.pause("hisAlarmList");
          SetInterval.pause("presetList");
          SetInterval.run("executeList");
          break;
        case "preset":
          presetListParams.current = 1;
          presetListParams.size = 20;
          presetListParams.time = "";
          presetListParams.searchStr = "";
          getPresetList();
          SetInterval.pause("executeList");
          SetInterval.pause("hisAlarmList");
          SetInterval.pause("alarmList");
          SetInterval.run("presetList");
          break;
        default:
          break;
      }
    };
    // --------------------------------------------------- 实时报警列表 ----------------------------------------
    const columns = [
      {
        title: "状态",
        dataIndex: "confirm",
        width: "70px",
        align: "center",
        fixed: "left",
      },
      {
        title: "首次报警",
        dataIndex: "createTime",
        width: "170px",
        align: "center",
      },
      {
        title: "最近报警",
        dataIndex: "updateTime",
        width: "170px",
        align: "center",
      },
      {
        title: "确认时间",
        dataIndex: "confirmTime",
        width: "170px",
        align: "center",
      },
      {
        title: "视频通道",
        dataIndex: "channelName",
        width: "110px",
        align: "center",
      },

      {
        title: "预置点",
        dataIndex: "presetName",
        width: "150px",
        align: "center",
      },
      {
        title: "算法实例",
        dataIndex: "algorithmInstanceName",
        width: "150px",
        align: "center",
      },
      {
        title: "算法输出",
        dataIndex: "outputParamName",
        width: "120px",
        align: "center",
      },
      {
        title: "触发条件",
        dataIndex: "alarmLevel",
        width: "110px",
        align: "center",
      },
      {
        title: "报警界限值",
        dataIndex: "levelValue",
        width: "120px",
        align: "center",
      },
      {
        title: "操作",
        dataIndex: "comfirmBtn",
        width: "290px",
        align: "center",
      },
    ];

    // 报警类表数据
    const dataSource = ref<TableType[]>([]);

    // 格式时间
    function formatDateTime(input) {
      if (input === null || input === undefined || input === "") return "";
      const date = new Date(input);

      // 提取年、月、日并补零
      const year = date.getFullYear();
      const month = String(date.getMonth() + 1).padStart(2, "0"); // 月份从0开始，需+1
      const day = String(date.getDate()).padStart(2, "0");

      // 提取时、分、秒并补零
      const hours = String(date.getHours()).padStart(2, "0");
      const minutes = String(date.getMinutes()).padStart(2, "0");
      const seconds = String(date.getSeconds()).padStart(2, "0");

      // 组合成目标格式
      return `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
    }
    // 获取报警列表

    const confirmColor = ref<string>("#adff2f"); // 已确认报警颜色
    const unconfirmedColor1 = ref<string>("#adff2f"); // 已确认报警颜色
    const unconfirmedColor2 = ref<string>("#adff2f"); // 已确认报警颜色
    const listParams = reactive({
      size: 20,
      current: 1,
      total: 0,
      time: "",
      searchStr: "",
    });
    const getAlarmTable = () => {
      const startAndEndTime = {
        startTime: listParams.time ? listParams.time[0] : "",
        endTime: listParams.time ? listParams.time[1] : "",
        searchStr: listParams.searchStr,
        nodeId: selectedkeys.value[0],
        size: listParams.size,
        current: listParams.current,
      };

      getAlarmTableForDcs(startAndEndTime).then((res) => {
        if (res.code === 0) {
          dataSource.value = (res.data.records as TableType[]).map((item) => {
            item.createTime = formatDateTime(item.createTime);
            item.updateTime = formatDateTime(item.updateTime);
            item.confirmTime = formatDateTime(item.confirmTime);
            return item;
          });
          listParams.total = res.data.total;
          let pages = Math.ceil(res.data.total / res.data.size);
          // 查询列表后，如果当前页大于总页数，则当前页为总页数
          if (listParams.current > pages && pages > 0) {
            listParams.current = pages;
          }
          confirmColor.value = `rgb(${res.data.dcsAlarmStateColor.confirmed[0]},${res.data.dcsAlarmStateColor.confirmed[1]},${res.data.dcsAlarmStateColor.confirmed[2]})`;
          unconfirmedColor1.value = `rgb(${res.data.dcsAlarmStateColor.unconfirmed1[0]},${res.data.dcsAlarmStateColor.unconfirmed1[1]},${res.data.dcsAlarmStateColor.unconfirmed1[2]})`;
          unconfirmedColor2.value = `rgb(${res.data.dcsAlarmStateColor.unconfirmed2[0]},${res.data.dcsAlarmStateColor.unconfirmed2[1]},${res.data.dcsAlarmStateColor.unconfirmed2[2]})`;
          isShowPreset.value = res.data.executeRecordShowable;
        }
      });
    };
    // 时间搜索
    const changeTime = () => {
      // 修改时间后从第一页开始
      listParams.current = 1;
      getAlarmTable();
    };

    const searchAlarm = () => {
      listParams.current = 1;
      getAlarmTable();
    };

    // 误报
    const falseAlarm = (record) => {
      Modal.confirm({
          content: "确认误报？",
          onOk() {
            falseAlarmForDcs(record.id).then((res) => {
              if (res.code === 0) {
                getAlarmTable();
              }
            });
          },
        });

    }

    // 分页
    const pagination = computed(() => ({
      total: listParams.total,
      current: listParams.current, //
      pageSize: listParams.size,
      showTotal: () => `共 ${listParams.total} 条`,
      showLessItems: true,
      defaultPageSize: listParams.size,
      showSizeChanger: false,
    }));
    const pageChange = ({ current, total }) => {
      listParams.current = current;
      getAlarmTable();
      // setCurrentPage(current);
    };

    /**
     * 确认报警
     *  @param record 行的属性
     */
    const comfirmAlarm = (id, comfirm) => {
      comfirmAlarmForDcs({ id, confirm: !comfirm }).then((res) => {
        if (res.code === 0) {
          getAlarmTable();
        }
      });
    };

    const getRowClass = (record) => {
      return "confirmedRowStyle";
    };

    // ------------------------------------------------------ 图像弹窗 ------------------------------------------------
    const picModal = ref<boolean>(false);
    const picWidth = ref<number>(962);
    const picHeight = ref<number>(565);

    const picX = ref<number>(0);
    const picY = ref<number>(0);

    // 展示图片
    const imgInfo = reactive({
      url: "",
    });
    const displayImage = ({ key }, record, imgKey) => {
      if (isEmpty(record[imgKey])) {
        message.warn("图片暂时无法展示，请关闭后重新打开");
        return;
      }
      picX.value = document.getElementById("scaleBox").clientWidth / 4;
      picY.value = document.getElementById("scaleBox").clientHeight / 9;
      videoTitle.value = `${record.channelName}_${record.presetName}_${record.algorithmInstanceName}`;
      const image = document.createElement("img");
      image.onload = () => {
        console.log("图片加载成功");
        imgInfo.url = typeof record[imgKey] === "string" ? record[imgKey] : record[imgKey][key];
        picModal.value = true;
      };
      image.onerror = () => {
        message.warn("图片暂时无法展示，请关闭后重新打开");
      };
      image.src = typeof record[imgKey] === "string" ? record[imgKey] : record[imgKey][key];
      image.remove();
    };

    // ----------------------------------------------------- 视频弹窗 --------------------------------------------------
    const videoTitle = ref<string>("");
    const video = ref(null);
    const videoModal = ref<boolean>(false);

    const videoWidth = ref<number>(768);
    const videoHeight = ref<number>(460);

    const videoX = ref<number>(641);
    const videoY = ref<number>(100);
    const showVideo = async (record) => {
      videoModal.value = true;
      videoTitle.value = `${record.channelName}_${record.presetName}_${record.algorithmInstanceName}`;
      videoX.value = document.getElementById("scaleBox").clientWidth / 3;
      videoY.value = document.getElementById("scaleBox").clientHeight / 8;

      nextTick(async () => {
        const url = await getChannelVideoLocationDCS(record.channelId);
        if (url.code !== 0) {
          return;
        }
        // video.value.playVideo(record.channelId);
        video.value.playVideo([url.data], record.channelId);
      });
    };

    const closeVideoModal = () => {
      videoModal.value = false;
      picModal.value = false;
      video.value = null;
    };

    const resizeEnd = (data) => {
      // console.log(data)
      const dataVideo = {
        x: data.x,
        y: data.y,
        w: data.w,
        h: data.h - 22,
      };
    };

    // ------------------------------------------------------ 历史报警列表 -----------------------------------------------

    const changeConfirmTime = () => {
      getHisAlarmTable();
    };

    const changeRestoreTime = () => {
      getHisAlarmTable();
    };

    const historyCol = [
      {
        title: "是否确认",
        dataIndex: "confirm",
        width: "100px",
        align: "center",
        fixed: "left",
      },
      {
        title: "是否误报",
        dataIndex: "falseAlarm",
        width: "100px",
        align: "center",
      },
      {
        title: "首次报警",
        dataIndex: "createTime",
        width: "170px",
        align: "center",
      },
      {
        title: "最近报警",
        dataIndex: "updateTime",
        width: "170px",
        align: "center",
      },
      {
        title: "确认时间",
        dataIndex: "confirmTime",
        width: "170px",
        align: "center",
      },
      {
        title: "恢复时间",
        dataIndex: "restoreTime",
        width: "170px",
        align: "center",
      },
      {
        title: "通道名",
        dataIndex: "channelName",
        width: "160px",
        align: "center",
      },

      {
        title: "预置点",
        dataIndex: "presetName",
        width: "160px",
        align: "center",
      },
      {
        title: "算法实例",
        dataIndex: "algorithmInstanceName",
        width: "120px",
        align: "center",
      },
      {
        title: "算法输出",
        dataIndex: "outputParamName",
        width: "110px",
        align: "center",
      },
      {
        title: "触发条件",
        dataIndex: "alarmLevel",
        width: "110px",
        align: "center",
      },
      {
        title: "报警界限值",
        dataIndex: "levelValue",
        width: "120px",
        align: "center",
      },
      {
        title: "操作",
        dataIndex: "comfirmBtn",
        width: "230px",
        align: "center",
      },
    ];

    const hisDataSource = ref([]);

    const hisListParams = reactive({
      size: 20,
      current: 1,
      total: 0,
      confirm_time: "",
      restore_time: "",
      time: "",
      confirm: null,
      falseAlarm: null,
    });
    const getHisAlarmTable = () => {
      const getListParams = {
        startTime: hisListParams.time ? hisListParams.time[0] : "",
        endTime: hisListParams.time ? hisListParams.time[1] : "",
        searchStr: encodeURIComponent(hisSearchValue.value),
        nodeId: selectedkeys.value[0],
        current: hisListParams.current,
        size: hisListParams.size,
        confirm: hisListParams.confirm,
        falseAlarm: hisListParams.falseAlarm,
      };
      getAlarmTableForDcsHis(getListParams).then((res) => {
        if (res.code === 0) {
          hisDataSource.value = res.data.records.map((item) => {
            item.createTime = formatDateTime(item.createTime);
            item.updateTime = formatDateTime(item.updateTime);
            item.confirmTime = formatDateTime(item.confirmTime);
            item.restoreTime = formatDateTime(item.restoreTime);
            return item;
          });
          hisListParams.total = res.data.total;
          let pages = Math.ceil(res.data.total / res.data.size);
          // 查询列表后，如果当前页大于总页数，则当前页为总页数
          if (hisListParams.current > pages && pages > 0) {
            hisListParams.current = pages;
          }
        }
      });
    };

    // 时间筛选
    const changeHisTime = () => {
      hisListParams.current = 1;
      getHisAlarmTable();
    };

    // 历史名称筛选
    const hisSearchValue = ref<string>("");
    const searchHisAlarm = () => {
      hisListParams.current = 1;
      getHisAlarmTable();
    };
    // 分页
    const hisPgination = computed(() => ({
      total: hisListParams.total,
      current: hisListParams.current, //
      pageSize: hisListParams.size,
      showTotal: () => `共 ${hisListParams.total} 条`,
      showLessItems: true,
      defaultPageSize: hisListParams.size,
      showSizeChanger: false,
    }));
    const hisPageChange = ({ current, total }) => {
      hisListParams.current = current;
      getHisAlarmTable();
      // setCurrentPage(current);
    };

    // ------------------------------------------------------- 算法执行列表 ---------------------------------------------
    const executeCol = [
      {
        title: "执行时间",
        dataIndex: "startTime",
        width: "170px",
        align: "center",
        fixed: "left",
      },
      {
        title: "通道名",
        dataIndex: "channelName",
        width: "160px",
        align: "center",
      },
      {
        title: "预置点",
        dataIndex: "presetName",
        width: "160px",
        align: "center",
      },
      {
        title: "算法实例",
        dataIndex: "algorithmInstanceName",
        width: "120px",
        align: "center",
      },
      {
        title: "执行状态",
        dataIndex: "resultStatus",
        width: "110px",
        align: "center",
      },
      {
        title: "错误原因",
        dataIndex: "failureReason",
        width: "120px",
        align: "center",
      },
      {
        title: "输入图片",
        dataIndex: "inputImages",
        width: "230px",
        align: "center",
      },
      {
        title: "输出图片",
        dataIndex: "outputImage",
        width: "230px",
        align: "center",
      },
    ];

    const executeData = ref([]);

    const executeListParams = reactive({
      size: 20,
      current: 1,
      total: 0,
      startTime: "",
      endTime: "",
      searchStr: "",
      resultStatus: "",
      time: "",
    });

    const getExecuteList = () => {
      const getListParams = {
        startTime: executeListParams.time ? executeListParams.time[0] : "",
        endTime: executeListParams.time ? executeListParams.time[1] : "",
        searchStr: encodeURIComponent(executeListParams.searchStr),
        nodeId: selectedkeys.value[0],
        size: executeListParams.size,
        current: executeListParams.current,
      };
      getExecuteListForDcs(getListParams).then((res) => {
        if (res.code === 0) {
          executeData.value = res.data.records.map((item) => {
            item.startTime = formatDateTime(item.startTime);
            return item;
          });
          executeListParams.total = res.data.total;
          let pages = Math.ceil(res.data.total / res.data.size);
          // 查询列表后，如果当前页大于总页数，则当前页为总页数
          if (executeListParams.current > pages && pages > 0) {
            executeListParams.current = pages;
          }
        }
      });
    };

    // 分页
    const executePgination = computed(() => ({
      total: executeListParams.total,
      current: executeListParams.current, //
      pageSize: executeListParams.size,
      showTotal: () => `共 ${executeListParams.total} 条`,
      showLessItems: true,
      defaultPageSize: executeListParams.size,
      showSizeChanger: false,
    }));
    const executePageChange = ({ current, total }) => {
      executeListParams.current = current;
      getExecuteList();
      // setCurrentPage(current);
    };

    const changeExTime = () => {
      executeListParams.current = 1;
      getExecuteList();
    };

    const searchExecute = () => {
      executeListParams.current = 1;
      getExecuteList();
    };
    // ------------------------------------------------------------预置点执行记录-----------------------------------------------------------
    const presetCol = [
      {
        title: "执行时间",
        dataIndex: "startTime",
        width: "170px",
        align: "center",
        fixed: "left",
      },
      {
        title: "通道名",
        dataIndex: "channelName",
        width: "160px",
        align: "center",
      },
      {
        title: "预置点",
        dataIndex: "presetName",
        width: "160px",
        align: "center",
      },
      {
        title: "执行状态",
        dataIndex: "resultStatus",
        width: "110px",
        align: "center",
      },
      {
        title: "错误原因",
        dataIndex: "failureReason",
        width: "120px",
        align: "center",
      },
      {
        title: "执行次数",
        dataIndex: "times",
        width: "110px",
        align: "center",
      },
    ];

    const presetData = ref([]);

    const presetListParams = reactive({
      size: 20,
      current: 1,
      total: 0,
      startTime: "",
      endTime: "",
      searchStr: "",
      resultStatus: "",
      time: "",
    });

    const getPresetList = () => {
      const getListParams = {
        startTime: presetListParams.time ? presetListParams.time[0] : "",
        endTime: presetListParams.time ? presetListParams.time[1] : "",
        searchStr: encodeURIComponent(presetListParams.searchStr),
        nodeId: selectedkeys.value[0],
        size: presetListParams.size,
        current: presetListParams.current,
      };
      getPresetListForDcs(getListParams).then((res) => {
        if (res.code === 0) {
          presetData.value = res.data.records.map((item) => {
            item.startTime = formatDateTime(item.startTime);
            return item;
          });
          presetListParams.total = res.data.total;
          let pages = Math.ceil(res.data.total / res.data.size);
          // 查询列表后，如果当前页大于总页数，则当前页为总页数
          if (presetListParams.current > pages && pages > 0) {
            presetListParams.current = pages;
          }
        }
      });
    };

    // 分页
    const presetPgination = computed(() => ({
      total: presetListParams.total,
      current: presetListParams.current, //
      pageSize: presetListParams.size,
      showTotal: () => `共 ${presetListParams.total} 条`,
      showLessItems: true,
      defaultPageSize: presetListParams.size,
      showSizeChanger: false,
    }));
    const presetPageChange = ({ current, total }) => {
      presetListParams.current = current;
      getPresetList();
      // setCurrentPage(current);
    };

    const changePresetTime = () => {
      presetListParams.current = 1;
      getPresetList();
    };

    const searchPreset = () => {
      presetListParams.current = 1;
      getPresetList();
    };

    const addTimerForAnyList = () => {
      // 定时实时报警列表
      SetInterval.add(
        "alarmList",
        () => {
          getAlarmTable();
        },
        3000
      );
      // 定时历史报警列表
      SetInterval.add(
        "hisAlarmList",
        () => {
          getHisAlarmTable();
        },
        3000
      );
      // 定时算法执行列表
      SetInterval.add(
        "executeList",
        () => {
          getExecuteList();
        },
        3000
      );
      // 定时预置点执行列表
      SetInterval.add(
        "presetList",
        () => {
          getPresetList();
        },
        3000
      );
    };

    onMounted(() => {
      getProjectList().then((id: string) => {
        selectedkeys.value = [id];
        getAlarmTable();
      });
      addTimerForAnyList();
      SetInterval.run("alarmList");
      // 适配分辨率
      new disableScaling().init();
      SetInterval.close("loginTimer"); // 关闭全局定时器
    });
    onUnmounted(() => {
      SetInterval.close("alarmList");
      clearTimeout(searchTimer);
    });

    //-------------------------------节点搜索-------------------------------------------------
    // 树节点搜索
    const generateList = (data) => {
      for (let i = 0; i < data.length; i++) {
        const node = data[i];
        const key = node.id;
        dataList.value.push({ key, title: node.label });
        if (node.children) {
          generateList(node.children);
        }
      }
    };

    const expandNode = (keys: string[] | number[]) => {
      expandedKeys.value = keys;
      autoExpandParent.value = false;
    };

    const getParentKey = (key: string | number, tree: any): string | number | undefined => {
      let parentKey;
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
          if (node.children.some((item) => item.id === key)) {
            parentKey = node.id;
          } else if (getParentKey(key, node.children)) {
            parentKey = getParentKey(key, node.children);
          }
        }
      }
      return parentKey;
    };
    const autoExpandParent = ref<boolean>(true);

    let searchTimer = null;

    const findAncestor = (el, selector) => {
      while (el && el !== document) {
        if (el.matches(selector)) {
          return el;
        }
        el = el.parentElement;
      }
      return null;
    };

    // const onSearchNode = (val) => {
    //   if (searchTimer) {
    //     clearTimeout(searchTimer);
    //   }
    //   searchTimer = setTimeout(() => {
    //     const scrollContainer = document.getElementById("treeBox");
    //     const anchorElement = document.getElementById(searchValue.value);
    //     const parentElement = findAncestor(anchorElement, ".ant-tree-treenode");

    //     if (scrollContainer && parentElement) {
    //       const elementPosition = parentElement.offsetTop - scrollContainer.offsetTop;
    //       console.log(elementPosition, parentElement, scrollContainer, "elementPosition");
    //       scrollContainer.scrollTo({
    //         top: elementPosition,
    //         behavior: "smooth",
    //       });
    //     }
    //   }, 300);
    // };

    // -------------------------------------------------------------------------树节点搜索---------------------------------------------------------
    const searchList = ref<any[]>([]); // 搜索结果列表

    const searchValue = ref<string>(""); // 当前搜索值

    const nodeRefs = ref({}); // 节点引用

    const searchKey = ref(""); // 搜索关键字

    // 找到所有父节点的ID
    function findParentIdsFromTree(treeArray, targetId) {
      const path = [];

      function dfs(node, ancestors) {
        if (node.id === targetId) {
          path.push(...ancestors);
          return true;
        }

        if (node.children && node.children.length > 0) {
          for (const child of node.children) {
            if (dfs(child, [...ancestors, node.id])) {
              return true;
            }
          }
        }

        return false;
      }

      for (const root of treeArray) {
        if (dfs(root, [])) {
          break;
        }
      }

      return path;
    }

    function searchOutputParams(tree, keyword) {
      const results = [];

      if (keyword === "") {
        return [];
      }
      function traverse(node, parent, grandparent) {
        // 匹配output-param节点且包含关键字
        if (node.label.toUpperCase().includes(keyword.toUpperCase())) {
          // 构建层级标签
          const labels = [];
          if (grandparent) labels.push(grandparent.label);
          if (parent) labels.push(parent.label);
          labels.push(node.label);

          // 生成格式化结果
          results.push({
            key: node.id,
            label: labels.join("-"),
          });
        }

        // 递归遍历子节点，维护父级关系
        if (node.children) {
          node.children.forEach((child) => {
            // 当前节点成为父级，原父级成为祖父级
            traverse(child, node, parent);
          });
        }
      }

      // 从根节点开始遍历
      tree.forEach((root) => traverse(root, null, null));

      return results;
    }

    // 搜索树
    const searchTree = () => {
      // debugger;
      searchList.value = searchOutputParams(treeData.value, searchValue.value);
      searchKey.value = "";
    };

    // 滚动到选中节点
    const scrollToNode = () => {
      if (searchKey.value && nodeRefs.value[searchKey.value]) {
        const node = nodeRefs.value[searchKey.value];
        expandedKeys.value.push();
        node.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    };
    // 选中搜索结果
    const handleSelect = async (item) => {
      searchKey.value = item.key;
      await nextTick();
      console.log(treeData.value, item.key);
      console.log(findParentIdsFromTree(treeData.value, item.key));
      findParentIdsFromTree(treeData.value, item.key).forEach((item) => {
        expandedKeys.value.push(item);
      });
      scrollToNode();
    };

    return {
      zhCn,
      currentAlarm,
      changeListTabs,
      treeData,
      selectedkeys,
      selectTree,
      IconList,

      // onSearchNode,
      // 报警表格
      columns,
      dataSource,
      formatDateTime,
      pagination,
      pageChange,
      listParams,
      comfirmAlarm,
      getRowClass,
      getAlarmTable,
      searchAlarm,
      changeTime,
      videoTitle,
      showVideo,
      video,
      videoModal,
      closeVideoModal,
      picModal,
      picWidth,
      picHeight,
      picX,
      picY,
      imgInfo,
      displayImage,
      videoWidth,
      videoHeight,
      videoX,
      videoY,
      resizeEnd,
      confirmColor,
      unconfirmedColor1,
      unconfirmedColor2,
      isShowPreset,
      falseAlarm,

      // 历史报警
      hisListParams,
      historyCol,
      hisDataSource,
      changeConfirmTime,
      changeRestoreTime,
      changeHisTime,
      hisSearchValue,
      searchHisAlarm,
      hisPgination,
      hisPageChange,

      //节点搜索
      searchList,
      searchValue,
      expandedKeys,
      nodeRefs,
      searchKey,
      searchTree,
      handleSelect,
      autoExpandParent,
      expandNode,
      // 算法执行
      executeListParams,
      executeCol,
      executeData,
      executePgination,
      executePageChange,
      getExecuteList,
      searchExecute,
      changeExTime,
      // 预置点执行
      presetListParams,
      presetCol,
      presetData,
      presetPgination,
      presetPageChange,
      getPresetList,
      searchPreset,
      changePresetTime,

      getOnlineChannelCount,
      getTotalChannelCount,
    };
  },
});
</script>
<style scoped lang="less">
/* html,body{
  width: 1920px !important;
  overflow: hidden !important;
  margin: 0;
  padding: 0;
} */
@dcsColor: #2378f6;
.screen {
  width: 100vw;
  max-height: 100vh;
  overflow: auto;
  padding: 5px;
  /* overflow-x:hidden;
  overflow-y: auto; */
  /* position: absolute; */
}

/* .rootBox{
  height: 100vh;
  padding: 10px;
  background-color: #f5f5f5;

} */
.screen::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

.screen::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.2);
}

.screen::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}

#treeBox {
  height: 99vh;
  min-width: 250px;
  overflow-y: auto;
  overflow-x: hidden;
}

#treeBox::-webkit-scrollbar {
  width: 5px;
}

#treeBox::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.2);
}

#treeBox::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}

:deep(.ant-card-bordered) {
  border: 1px solid #ddd;
}

:deep(.ant-card-body) {
  padding: 0px;
}

:deep(.ant-table-thead > tr > th) {
  height: 40px !important;
  padding: 0px 16px !important;
  background-color: #e7e9ed;
  font-size: 14px;
}

:deep(.ant-table-tbody > tr > td) {
  border-bottom: 1px solid #333748;
  height: 35px !important;
  padding: 0px 0px !important;
  font-size: 14px;
}

:deep(.ant-table-tbody > tr > td.ant-table-cell-row-hover) {
  background: @dcsColor !important;
  transition: 0.1s !important;
}

:deep(.confirmedRowStyle) td {
  transition: none;
  background: #333748;
  color: #fff;
}

/** 树节点样式 */
:deep(.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected) {
  background-color: @dcsColor;
  color: #fff;
}

.node-title {
  width: 80%;
  word-break: break-all;
}

/** 按钮样式 */
:deep(.ant-btn-primary) {
  border-color: @dcsColor;
  background-color: @dcsColor;
}

.confirmIconStyle {
  font-size: 14px;
  color: v-bind(confirmColor);
}

.unconfirmIconStyle {
  color: v-bind(unconfirmedColor1);
  font-size: 14px;
  animation: blink 1s infinite steps(1);
}

@keyframes blink {
  50% {
    color: v-bind(unconfirmedColor2);
  }
}

:deep(.unConfirmRowStyle:hover > td) {
  background-color: @dcsColor !important;
}

:deep(.confirmedRowStyle:hover > td) {
  background-color: @dcsColor !important;
}

:deep(.ant-table-pagination.ant-pagination) {
  margin-bottom: 0px;
}

.modal-mask {
  position: fixed;
  z-index: 9998;
  top: 0;
  left: 0;
  width: 100%;
  height: 100vh;
  background-color: rgba(0, 0, 0, 0.5);
  display: flex;
  transition: opacity 0.3s ease;
}

.closeVideoBtn {
  color: #fff;
  font-size: 14px;
  cursor: pointer;
  margin-left: auto;
}

:deep(.ant-tree-switcher) {
  width: 10px;
}

:deep(.ant-tree-indent-unit) {
  width: 10px;
}

.listBox {
  width: 100%;
  height: 100%;
  display: flex;
  gap: 10px;
}

.flexTree {
  flex: 1.1;
  flex-shrink: 1;
}

.flexTable {
  flex: 5.9;
  flex-shrink: 1;
}

:global(#app) {
  min-width: 200px !important;
}
.area {
  color: #1890ff;
}

.iconClass {
  margin-right: 6px;
  color: var(--iconColor);
}

.searchBack {
  background-color: #fffb8f;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}
</style>
