<template>
  <div class="screen" id="showVideoBoxBox">
    <!-- <scaleBox>
      <template #content> -->
    <videoPlayer ref="videoPlayerRef" :show-control="true" :show-btn="true" ></videoPlayer>
    <!-- </template>
    </scaleBox> -->
  </div>
</template>
<script lang="ts" setup>
import { ref, onBeforeUnmount, onMounted, nextTick } from "vue";
import { useRoute } from "vue-router";
import scaleBox from "@/components/scaleBox.vue";
import videoPlayer from "@/components/videoElement/videoPlayer.vue";
import SetInterval from "@/common/SetInterval";
import { getChannelVideoLocationDCS } from "@/api/design";
import { jumpToPresetPoint } from "@/api/design";

const route = useRoute();
const channelId = route.query.channelId;

const presetId = route.query.presetId;

const videoPlayerRef = ref(null);

const getVideoUrl = async (id) => {
  if (presetId) {
    await jumpToPresetPoint(presetId);
  }
  const url = await getChannelVideoLocationDCS(id);
  if (url.code !== 0) {
    return;
  }
  videoPlayerRef.value.playVideo([url.data], id);
};

onMounted(() => {
  getVideoUrl(channelId);
  SetInterval.close("loginTimer");
});

onBeforeUnmount(() => {
  videoPlayerRef.value.destroyVideo();
});
</script>
<style scoped>
.screen {
  width: 100%;
  height: 100vh;
  overflow: hidden;
  position: relative;
  background: #353945;
}
/* html,body{
  background: #353945;
} */
:global(#app) {
  min-width: 200px !important;
}
</style>
