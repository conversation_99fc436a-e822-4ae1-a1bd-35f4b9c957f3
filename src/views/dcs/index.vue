<template>
  <div>
      <!-- <videoWin ref="video"></videoWin> -->
      <singleChannel ref="videoSingle" v-if="mode === '0'"></singleChannel>
      <multiChannel ref="videoMulti" v-if="mode === '1'"></multiChannel>
  </div>
</template>
<script lang="ts">
import { defineComponent, ref, onBeforeUnmount ,onMounted} from 'vue';
import { useRoute } from 'vue-router'
import { getChannelVideoLocation , clickTreeNode} from '../../api/design';
import videoWin from '../dcsSplitScreen/index.vue';
import singleChannel from '../map/components/videoScheduling.vue';
import multiChannel from '../map/components/videoMul.vue';
export default defineComponent({
  components:{
    videoWin,
    singleChannel,
    multiChannel,
  },
  setup() {
    const videoSingle = ref(null);
    const videoMulti = ref(null);
    const route = useRoute();
    const taskId = route.query.taskId; // 345561a9f35f8b7222c1ea1bf869509c
    const mode = ref<string|string[]>(route.query.mode); // 单通道 0|多通道1
    // test
    // const TaskNode = (id, mode)=>{
    //   video.value.getNodeInfo(id, mode);
    // }
    // const getNodeInfo = ()=>{
    //     // getChannelVideoLocation(channelId).then((res) => {
    //     //   if (res.data) {
    //     //     const data = {
    //     //       previewUrl:res.data,
    //     //       channelId:channelId,
    //     //       label: ''
    //     //     };
    //     //     (video.value as any).getflow(data)
    //     //   }
    //     // });
    //     video.value.getNodeInfo(taskId, mode);
      
    // };
    // 初始化ws

    // 单通道

    onMounted(() => {
      if(mode.value === '0'){
        console.log('adaad');
        videoSingle.value.getNodeInfo(taskId, Number(mode.value));
      }
      if(mode.value === '1'){
        videoMulti.value.getNodeInfo(taskId, Number(mode.value));
      }
      
    });

    return {
      videoSingle,
      videoMulti,
      mode,
    };
  },
});
</script>
<style lang="less" scoped>
</style>