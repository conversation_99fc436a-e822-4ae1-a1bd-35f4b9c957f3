<template>
  <div class="loginBack">
    <AuthorizationModal :isExpire="true" @closeModal="closeModal" />
  </div>
</template>
<script lang="ts">
import { defineComponent, onMounted, onUnmounted } from 'vue';
import { useStore } from 'vuex';
import { checkAuthorization } from '@/api/project';
import AuthorizationModal from '@/components/AuthorizationModal.vue';
import router from '@/router';
export default defineComponent({
  components: {
    AuthorizationModal,
  },

  setup() {
    onMounted(() => {
      timer = window.setInterval(checkAuth, 5000);
      store.state.design.authorizationModalVisible = true;
    });

    onUnmounted(() => {
      if (timer) clearInterval(timer);
    });


    const store = useStore();
    let timer: number | undefined;

    // 关闭授权弹框
    const closeModal = () => {
      store.commit('setAuthorizationModalVisible', false);
    };

    // 校验证书
    const checkAuth = async () => {
      const res = await checkAuthorization();
        if (res.code === 0) {
          store.state.design.authorizationModalVisible = false;
          if (timer) clearInterval(timer);
          // 如果单点登陆模式 授权完直接跳平台登录页 否则就正常跳设计页
          if (window.localStorage.getItem("plantMode") === "true") { 
            const config = JSON.parse(
              localStorage.getItem("oauth2Config"));
            const { clientId, redirectUri, authUrl, webTokenUrl, targetPath } = config;
            const tokenUrl = window.location.hash ? `/#${webTokenUrl}` : webTokenUrl;
            const redirectWeb = encodeURIComponent(
              `${window.location.origin}${tokenUrl}?path=${targetPath}`
            );
            const redirectUrl = encodeURIComponent(`${redirectUri}?redirect_web=${redirectWeb}`);
            const url = `${authUrl}?client_id=${clientId}&redirect_uri=${redirectUrl}&response_type=token`;
            window.location.href = url;
          } else {
            router.push({ path: '/design' });
          }
        }
    };

    return {
      closeModal,
    };
  },
});
</script>
<style scoped lang="less">
@import url("../clientLogin/loginStyle.less");
</style>
