<template>
  <div class="box" :style="{width: `${propsConf.width}px`, height:`${propsConf.height}px`}">
    <single-video
      v-for="(item, index) in wndList"
      :key="index"
      :videoId="`video-webrtc${index}${item.id}`"
      :canvasId="`canvas${index}`"
      :width="item.width"
      :height="item.height"
      :videoUrl="item.url"
      :channelId="item.channelId"
    ></single-video>
    <div
      :style="{
        position: 'absolute',
        'z-index': 200,
        display: 'flex',
        'flex-wrap': 'wrap',
        width: `${propsConf.width}px`,
        height: `${propsConf.height}px`,
        'align-content':'flex-start',
      }"
      v-if="propsConf.osd || showOsd"
    >
      <single-canvas
        v-for="(item, index) in canvasList"
        :key="index"
        :canvasId="`canvas${index}${item.id}`"
        :channelId="item.channelId"
        :width="item.width"
        :height="item.height"
        :coefficient="item.coefficient"
      ></single-canvas>
    </div>
    <div
      :style="{
        position: 'absolute',
        'z-index': 300,
        display: 'flex',
        'flex-wrap': 'wrap',
        'align-content':'flex-start',
        width: `${propsConf.width}px`,
        height: `${propsConf.height}px`,
      }"
      v-if="showToolBar"
    >
      <div
        v-for="(item, index) in toolBarList"
        :key="index"
        :style="{ width: `${item.width}px`, height: `${item.height}px` }"
      >
        <tool-bar
          :channelId="item.channelId"
          :videoId="`video-webrtc${index}${item.id}`"
          :isWebrtc="item.isWebrtc"
          :isBig="item.width < 390"
          :isPTZ="item.isPTZ"
          :channelName="item.channelName"
          :isMode="item.mode"
          :is-dcs="true"
        ></tool-bar>
      </div>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent, reactive, onMounted, nextTick, onBeforeUnmount, ref, watch,
} from 'vue';
import { useRoute } from 'vue-router'
import { getVideoUrl } from '@/api/operatorStation';
import singleVideo from './components/singleVideo.vue';
import singleCanvas from './components/singleCanvas.vue';
import toolBar from './components/newhkvPlayer/index.vue';

export default defineComponent({
  components: { singleVideo, singleCanvas, toolBar },
  name: 'splitScreen',
  props:{
    videoWidth:{
      type:Number,
      default:1152
    },
    videoHeight:{
      type:Number,
      default:648
    },
    showOsd:{
      type:Boolean,
      default:false,
    },
    showToolBar:{
      type:Boolean,
      default:true,
    }
  },
  setup(props) {
    const wndList = ref([]);
    const canvasList = ref([]);
    const toolBarList = ref([]);

    // 接受参数动态渲染
    const route = useRoute();
    const propsConf = reactive({
      width:route.query.width || props.videoWidth, // 窗口宽 默认1152
      height:route.query.height || props.videoHeight, // 窗口高 默认648
      osd: route.query.osd === 'true' // 是否显示osd，默认否
    })
    // 拉流
    /**
     *
     * @param info 通道信息
     * {
     * id:任务id
     * previewUrl：播放url
     * channelId：通道id
     * label：通道名称
     * }
     * @param mode
     */
    const getflow = (info, mode) => {
        let tempwndList = [];
        let tempCanvasList = [];
        let tempToolbarList = [];
        tempwndList.push({
          width: propsConf.width,
          height: propsConf.height,
          url: info.previewUrl,
          id: info.id,
          channelId:info.channelId
        });
        tempCanvasList.push({
          width: propsConf.width,
          height: propsConf.height,
          channelId: info.channelId,
          id:info.id,
          coefficient: {
            coefficientX:Number(propsConf.width) / 1920,
            coefficientY:Number(propsConf.height) / 1080,
          },
        });
        tempToolbarList.push({
          width: propsConf.width,
          height: propsConf.height,
          channelId: info.channelId,
          isWebrtc: !!info.previewUrl,
          isPTZ: info.cameraType.includes('PTZ'),
          channelName: info.channelName,
          mode:mode,
          id:info.id,
          isDcs:info.dcs
        });
        wndList.value = tempwndList;
        canvasList.value = tempCanvasList;
        toolBarList.value = tempToolbarList;

    };
    // 改变视频窗口大小
    const changeVideoBox = (data)=>{
      console.log(data,'datatdat')
      wndList.value[0].width = data.w;
      wndList.value[0].height = data.h;
      propsConf.width = data.w;
      propsConf.height = data.h;
    }

    return {
      wndList,
      // 窗口大小
      propsConf,
      // canvas列表
      canvasList,
      // 工具条
      toolBarList,
      // 拉流
      getflow,
      changeVideoBox
    };
  },
});
</script>

<style scoped>
.box {
  /* box-sizing: border-box; */
  display: flex;
  align-content: flex-start;
  flex-wrap: wrap;
  background-color: #020717;
}
.pagination {
  display: flex;
  flex-wrap: wrap;
  row-gap: 8px;
  margin-top: 10px;
  margin-left: 7%;

}
.selectWnd {
  justify-content: flex-end;
}


</style>
