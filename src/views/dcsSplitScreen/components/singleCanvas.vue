<template>
  <canvas :id="canvasId" :width="width" :height="height"></canvas>
</template>

<script lang="ts">
import {
  defineComponent, nextTick, onMounted, onUnmounted, watch, onBeforeUnmount,
} from 'vue';
import { fabric } from 'fabric';
import HWebsocket from '@/common/h-websocket';
import drawGraph from '@/hooks/darwElement';
import uEmitter from '@/common/u-event-bus';

export default defineComponent({
  props: {
    width: {
      type: Number,
      default: 1152,
    },
    height: {
      type: Number,
      default: 648,
    },
    canvasId: {
      type: String,
      default: 'canvas',
    },
    channelId: {
      type: String,
      default: '',
    },
    coefficient: {
      type: Object,
      default: ()=>{},
    },
  },
  setup(props) {
    let canvas = null;
    function initCanvas() {
      canvas = new fabric.Canvas(props.canvasId, {
        selection: false,
      });
    }
    watch(
      () => props.coefficient,
      () => {
        nextTick(() => {
          if (canvas) {
            console.log('改变画布', props.width, props.height);
            canvas.clear();
            canvas.setWidth(props.width);
            canvas.setHeight(props.height);
          }
        });
      },
      {deep:true}
    );
    watch(
      () => props.channelId,
      (nv) => {
        if (nv) {
          console.log(333)
          reconnectWs();
        }
      },
    );
    // 绘制图形
    const { draw } = drawGraph();
    // 初始化ws
    let ws = null;
    let coefficient = {
      zoomX:props.coefficient.coefficientX,
      zoomY:props.coefficient.coefficientY
    }
    function openWs() {
      // drawGraph(fakedata);
      ws = new HWebsocket(
        `${(window as any).g.wsApi}/websocket/osd/${props.channelId}`,
      );
      ws.onMessage = (evt) => {
        const wsData = JSON.parse(evt.data);
        if (wsData.eventType === 'draw') {
          // drawGraph(wsData.osdItemList);
          draw(canvas,coefficient.zoomX,coefficient.zoomY,wsData.osdItemList)
        }
        if (wsData.eventType === 'clear') {
          if(canvas){
            canvas.clear();
          }
        }
        if (wsData.eventType === 'switchChannelMode') {
          uEmitter.emit('changeChannelMode', 'AUTOMATIC' );
        }
      };
    }
    function reconnectWs() {
      let timer = null;
      if (ws === null) {

        openWs();
      } else {
        closeWs();
        if(canvas){
          canvas.clear();
          canvas.renderAll();
        }


        timer = setTimeout(() => {
          openWs();
        }, 0);
      }
    }
    function closeWs() {
      if (ws) {
        console.log('关闭ws');
        ws.close();
        ws = null;
      }
    }
    function disposeCanvas() {
      if (canvas) {
        canvas.clear();
        canvas.dispose();
        canvas = null;
      }
    }
    onMounted(() => {
      initCanvas();
      openWs();
    });
    onBeforeUnmount(() => {
      if (canvas) {
        closeWs();
        disposeCanvas();
        console.log(`%cid为${props.canvasId}的canvas已销毁`, 'color:blue');
      }
    });

    // 求出最大公约数
    function gcd(a, b) {
        if (b === 0) {
            return a;
        }
        return gcd(b, a % b);
    }
    uEmitter.on('videoSize',(val)=>{
      const divisor = gcd(val[0], val[1]) // 公约数
      const videoRatio = [val[0]/divisor,val[1]/divisor];
      coefficient.zoomX = props.width as number / val[0];
      coefficient.zoomY = props.width as number / val[0];
      if(val[0]/divisor !== 16){
        const videoLoss = 1- ((16 - videoRatio[0]) / 16); // 视频损耗率
        const widthShift = (props.width as number * videoLoss) / 2; // 视频位移数
        const zoom = (props.width as number * ((16 - videoRatio[0]) / 16)) / val[0];
        console.log(divisor,videoRatio ,widthShift,zoom,props.width)
        console.log(props.coefficient.coefficientX,props.coefficient.coefficientY,props.width,props.height,'asd')


        coefficient.zoomX = zoom;
        coefficient.zoomY = zoom;
        if(canvas){
          const viewportTransform = canvas.viewportTransform;
          viewportTransform[4] = widthShift
        }
      }

      // canvas.setViewportTransform(viewportTransform);
      // canvas.renderAll();
    })
    return {};

  },
});
</script>

<style scoped></style>
