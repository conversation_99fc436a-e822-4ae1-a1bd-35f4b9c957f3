@import './icon/index.css';
// 主样式
.hkv-player {
  width: 100%;
  height: 100%;
  position: relative;
  overflow: hidden;
  display: flex;
  background-color: rgba($color: #000000, $alpha: 0.0);

  * {
    box-sizing: border-box;
  }
  // 播放器中按钮样式
  button {
    background: none;
    border: none;
    display: flex;
    font-size: inherit;
    line-height: inherit;
    text-transform: none;
    text-decoration: none;
    cursor: pointer;
    overflow: hidden;
    box-sizing: border-box;
  }
  .hkv-player-header{
    width: 100%;
    height: 40px;
    line-height: 40px;
    position: absolute;
    top: 0;
    left: 0;
    display: flex;
    padding: 0 10px;
    background: #4c4b4b;
    transform: translateY(-100%);
    transition: 0.48s transform ease-in-out;
    z-index: 10;
    box-sizing: border-box;
    &.is-show {
      transform: translateY(0);

      .recording-tips {
        display: inline-flex;
        transform: translateY(0) !important;
        // transition: 0.45s display;
      }
    }
    .player-title {
      color: #fff;
      float: left;
    }
    .player-select{
      margin-left: auto;
    }

  }
  // 播放器样式
  // 底部工具条样式
  .hkv-player-toolbar{
    width: 100%;
    height: 40px;
    line-height: 40px;
    background: #4c4b4b;
    padding: 0px 8px;
    position: absolute;
    bottom: 0px;
    left: 0px;
    display: flex;
    flex-direction: row;
    align-items: center;
    transform: translateY(100%);
    transition: 0.48s transform ease-in-out;
    z-index:10;
    box-sizing: border-box;
    // 添加动画
    &.is-show{
      transform: translateY(0);
    }
    // 工具条中元素样式
    .toolbar-item{
      color: whitesmoke !important;
      opacity: 0.8;
      transition: 0.28s opacity ease-in-out, 0.28s color;
      // 添加工具条中元素hover动画
      &.hover{
        opacity: 1;
      }
      img.icon {
        object-fit: scale-down;
        max-width: 100%;
        max-height: 100%;
      }
    }
    // 工具条样式
    >.toolbar-item{
      max-width: 35px;
      max-height: 35px;
      font-size: 24px;
    }
    .fullscreen-btn{
      margin-left: auto;
    }
  }
}
.table-item {
  width: 100%;
  table-layout: fixed;
  font-size: 30px;
}
.table-item > tr {
  height: 50px;
}
.table-item > tr > td {
  text-align: center;
}
.table-PTZ {
  width: 100%;
  table-layout: fixed;
  font-size: 28px;
  // margin-top: 10px;
}
.table-PTZ > tr {
  height: 30px;
}
.table-PTZ > tr > td {
  text-align: center;
}
.PTZicon {
  font-size: 48px;
  cursor: pointer;
  color: black;
}