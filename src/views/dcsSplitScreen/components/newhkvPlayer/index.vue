<template>
  <div
    class="hkv-player"
    @mouseenter="handlePlayerMouseEnter"
    @mouseleave="handlePlayerMouseLeave"
    ref="rootRef"
  >
    <div class="hkv-player-header" :class="{ 'is-show': playerStatus.playerHover && isWebrtc }">
      <span class="player-title" style="font-size: 28px">
        {{ channelName }}
      </span>

      <button style="font-size: 24px">手动报警</button>
      <div class="player-select">
        <a-radio-group v-model:value="mode" size="small" @change="handleMode" style="color: white">
          <a-radio value="AUTOMATIC" style="color: #fff; font-size: 24px">自动</a-radio>
          <a-radio value="MANUAL" style="color: #fff; font-size: 24px">手动</a-radio>
        </a-radio-group>
      </div>
    </div>
    <div
      class="hkv-player-toolbar"
      :class="{ 'is-show': playerStatus.playerHover && isWebrtc }"
      @mouseenter="handleToolbarMouseEnter"
      @mouseleave="handleToolbarMouseLeave"
    >
      <el-popover
        placement="bottom"
        :width="300"
        trigger="hover"
        @show="showPtzControls"
        :teleported="isBig"
        v-if="mode !== 'AUTOMATIC'"
      >
        <table class="table-item" aria-describedby="hk-video" style="margin-left: 20px">
          <thead>
            <tr>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td>
                <CIconFont
                  type="icon-fangda-copy"
                  class="PTZicon"
                  @mousedown="controlPTZ('5')"
                  @mouseup="stopContorlPTZ"
                ></CIconFont>
              </td>
              <td>
                <CIconFont type="icon-a-bianjiao" class="PTZicon"></CIconFont>
              </td>
              <td>
                <CIconFont type="icon-a-guangquan" class="PTZicon"></CIconFont>
              </td>
            </tr>
            <tr>
              <td>
                <CIconFont
                  type="icon-suoxiao1-copy"
                  class="PTZicon"
                  @mousedown="controlPTZ('6')"
                  @mouseup="stopContorlPTZ"
                ></CIconFont>
              </td>
              <td>
                <CIconFont type="icon-bianjiao-" class="PTZicon"></CIconFont>
              </td>
              <td>
                <CIconFont type="icon-guangquan-" class="PTZicon"></CIconFont>
              </td>
            </tr>
          </tbody>
        </table>
        <table class="table-PTZ" aria-describedby="PTZ" style="margin-left: 20px">
          <thead>
            <tr>
              <th></th>
            </tr>
          </thead>
          <tbody>
            <tr>
              <td></td>
              <td>
                <CIconFont
                  type="icon-shuyi_shangyi-jiantou"
                  class="PTZicon"
                  @mousedown="controlPTZ('3')"
                  @mouseup="stopContorlPTZ"
                ></CIconFont>
              </td>
              <td></td>
            </tr>
            <tr>
              <td>
                <CIconFont
                  type="icon-shuyi_zuoyi-jiantou"
                  class="PTZicon"
                  @mousedown="controlPTZ('1')"
                  @mouseup="stopContorlPTZ"
                ></CIconFont>
              </td>
              <td></td>
              <td>
                <CIconFont
                  type="icon-shuyi_youyi-jiantou"
                  class="PTZicon"
                  @mousedown="controlPTZ('2')"
                  @mouseup="stopContorlPTZ"
                ></CIconFont>
              </td>
            </tr>
            <tr>
              <td></td>
              <td>
                <CIconFont
                  type="icon-shuyi_xiayi-jiantou"
                  class="PTZicon"
                  @mousedown="controlPTZ('4')"
                  @mouseup="stopContorlPTZ"
                ></CIconFont>
              </td>
              <td></td>
            </tr>
          </tbody>
        </table>
        <template #reference>
          <button type="button" title="摄像头控制" style="font-size: 28px">
            <CIconFont
              type="icon-shexiangtoukongzhi"
              style="font-size: 28px; color: white"
            ></CIconFont>
          </button>
        </template>
      </el-popover>
      <!-- <button
        class="toolbar-item snapshot-btn jm-icon-screenshots"
        title="截图"
        type="button"
        @click="handleToolBar('snapshot')"
      ></button> -->
      <!-- <button
        class="toolbar-item fullscreen-btn"
        type="button"
        :class="playerStatus.fullscreen ? 'jm-icon-fullscreen-exit' : 'jm-icon-fullscreen'"
        :title="playerStatus.fullscreen ? '取消全屏' : '全屏'"
        @click="handleToolBar('fullscreen')"
      ></button> -->
    </div>
  </div>
</template>

<script lang="ts">
import {
  computed,
  defineComponent,
  reactive,
  ref,
  onMounted,
  onUnmounted,
  watch,
} from "vue";
import { useRoute } from "vue-router";
import { PTZContorls, PTZStopContorls, PTZContorlsDCS, PTZStopContorlsDCS } from "@/api/design";
import { changeMode, changeModeDCS } from "@/api/operatorStation";
import fullscreen from "./fullscreen";
import { message } from "ant-design-vue";
import uEmitter from "@/common/u-event-bus/index";
import { ElPopover } from "element-plus";

export default defineComponent({
  props: {
    channelId: {
      type: String,
      default: "",
    },
    channelName: {
      type: String,
      default: "",
    },
    isWebrtc: {
      type: Boolean,
      default: true,
    },
    videoId: {
      type: String,
      default: "",
    },
    isBig: {
      type: Boolean,
      default: true,
    },
    isPTZ: {
      type: Boolean,
      default: true,
    },
    isMode: {
      type: String,
      default: "AUTOMATIC",
    },
    isDcs: {
      type: Boolean,
      default: false,
    },
  },

  components: {
    ElPopover,
  },
  setup(props, { emit }) {
    // const route = useRoute();
    const displayPlay = ref(useRoute().query.showplay);
    // 播放器状态
    const playerStatus = reactive({
      /**
       * 是否处于无信号状态
       * 1. 当流中断事件触发后，15秒后还没有收到ws消息
       * 2. ws关闭事件触发
       */
      noSignal: false,
      /** 是否已获取到视频分辨率 */
      gotResolution: false,
      /** 是否鼠标悬停在播放器内部 */
      playerHover: false,
      /** 是否处于全屏播放 */
      fullscreen: false,
      //
      playing: false,
      backgroud: false,
      currentTime: 0,
      recording: false,
      recordingDuration: 0,
      volume: 0,
      paused: true,
    });
    // 根节点
    const rootRef = ref(null);
    // settimeout计时器
    const timer = {
      noSignal: null,
      canvasMouseMove: null,
    };
    // 鼠标移入视频窗口触发工具条
    const handlePlayerMouseEnter = () => {
      playerStatus.playerHover = true;
    };
    // 鼠标移入视频源3秒关闭视频源
    const handleCanvasMouseMove = () => {
      playerStatus.playerHover = true;
      clearTimeout(timer.canvasMouseMove);
      timer.canvasMouseMove = setTimeout(() => {
        playerStatus.playerHover = false;
      }, 3000);
    };
    // 鼠标移除视频源
    const handlePlayerMouseLeave = () => {
      clearTimeout(timer.canvasMouseMove);
      playerStatus.playerHover = false;
    };
    // 鼠标移入工具条
    const handleToolbarMouseEnter = () => {
      playerStatus.playerHover = true;
      clearTimeout(timer.canvasMouseMove);
    };
    // 鼠标移除工具条
    // eslint-disable-next-line @typescript-eslint/no-empty-function
    const handleToolbarMouseLeave = () => {};
    /**
     * 工具条按钮事件
     */
    const mode = ref<string>("MANUAL");
    watch(
      () => props.isMode,
      (nv) => {
        if (nv) {
          mode.value = nv as string;
        }
      },
      { immediate: true }
    );
    uEmitter.on("changeChannelMode", (val) => {
      mode.value = val;
    });

    const handleMode = () => {
      const data = {
        id: props.channelId as string,
        mode: mode.value as string,
      };

      // console.log(props.isDcs,'dcs');

      if (props.isDcs) {
        changeModeDCS(data).then((res) => {
          if (res.code === 0) {
            message.destroy();
            message.success("更新成功", 2);
            uEmitter.emit("changeChannelMode", data.mode);
          }
        });
      } else {
        changeMode(data).then((res) => {
          if (res.code === 0) {
            message.destroy();
            message.success("更新成功", 2);
            uEmitter.emit("changeChannelMode", data.mode);
          }
        });
      }
    };

    // 工具条按钮
    const handleToolBar = (cmd) => {
      switch (cmd) {
        case "fullscreen":
          toggleFullscreen();
          break;
        case "mute":
          // toggleMute();props中的属性变为响应式
          // isMuted.value = !isMuted.value;
          toggleMutes();
          break;
        case "controlPlay":
          togglePauseAndPlay();
        default:
          break;
      }
    };
    // 全屏
    const toggleFullscreen = () => {
      const currVideoDom: any = document.getElementById(props.videoId as string);
      if (playerStatus.fullscreen) {
        fullscreen.exit(currVideoDom);
      } else {
        fullscreen.request(currVideoDom, () => {
          playerStatus.fullscreen = false;
        });
        console.log(currVideoDom);
      }
      playerStatus.fullscreen = !playerStatus.fullscreen;
    };

    // 控制播放
    const togglePauseAndPlay = () => {
      playerStatus.paused = !playerStatus.paused;
      uEmitter.emit("contorlPlay", playerStatus.paused);
    };
    // PTZ
    const showPtzControls = () => {
      playerStatus.playerHover = true;
    };

    const controlPTZ = (action) => {
      const data = {
        channelId: props.channelId,
        action,
      };
      if (props.isDcs) {
        PTZContorlsDCS(data).then((res) => {
          console.log(res);
        });
      } else {
        PTZContorls(data).then((res) => {
          console.log(res);
        });
      }
    };
    const stopContorlPTZ = () => {
      if (props.isDcs) {
        PTZStopContorlsDCS(props.channelId).then((res) => {
          if (res.code === 0) {
            console.log(res);
          }
        });
      } else {
        PTZStopContorls(props.channelId).then((res) => {
          if (res.code === 0) {
            console.log(res);
          }
        });
      }
    };

    //========================声音===========================

    const isMuted = ref<boolean>(true);

    const toggleMutes = () => {
      const currVideoDom: any = document.getElementById(props.videoId as string);
      currVideoDom.muted = !isMuted.value;
      isMuted.value = !isMuted.value;
    };

    onMounted(() => {
      // console.log(document.getElementById('figureCanvas').clientWidth, 'wwwwccccc');
      // mode.value = props.isMode as string
    });
    // onUpdated(() => {
    //   mode.value = props.isMode as string
    // });
    onUnmounted(() => {});
    return {
      rootRef,
      playerStatus,
      handlePlayerMouseEnter,
      handleCanvasMouseMove,
      handlePlayerMouseLeave,
      handleToolbarMouseEnter,
      handleToolbarMouseLeave,
      handleToolBar,
      controlPTZ,
      stopContorlPTZ,
      showPtzControls,
      mode,
      handleMode,
      displayPlay,
      isMuted,
    };
  },
});
</script>

<style lang="scss" src="./style/index.scss" scoped></style>
