<template>
  <a-spin tip="视频加载中..." :spinning="spinning" size="large">
    <template #indicator>
      <LoadingOutlined></LoadingOutlined>
    </template>
    <div :style="{ width: `${width}px`, height: `${height}px` }" class="error" v-if="!videoUrl">
      无法预览
    </div>
    <video
      :id="videoId"
      muted
      :autoplay = "displayPlay"
      :width="width"
      :height="height"
      v-if="videoUrl"
      webkit-playsinline="true"
      playsinline="true"
      x5-playsinline
      @loadeddata="finishFirstKey"
    ></video>
  </a-spin>

</template>

<script lang="ts">
import {
  defineComponent, nextTick, onMounted, onUnmounted, watch, ref
} from 'vue';
import { useRoute } from 'vue-router';
import { LoadingOutlined } from '@ant-design/icons-vue';
import JSWebrtc from '@/components/jswebrtc.min.js';
import { getChannelVideoLocationDCS, getDcsVideoResolution } from '@/api/design';
import SetInterval from '@/common/SetInterval';
import uEmitter from '@/common/u-event-bus';

export default defineComponent({
  props: {
    videoId: {
      type: String,
      default: 'video-webrtc',
    },
    width: {
      type: Number,
      default: 1152,
    },
    height: {
      type: Number,
      default: 648,
    },
    videoUrl: {
      type: String || null,
      default: null,
    },
    channelId:{
      type:String,
      default:null,
    }
  },
  components:{
    LoadingOutlined,
  },
  setup(props, { emit }) {
    let player = null; // webrtc对象实例
    const prevideoId = ref<string>(null);
    const displayPlay = ref<boolean>(useRoute().query.showplay !== '1');
    const initVideo = () => {
      let timer = null;
      spinning.value = true;
      if (props.videoUrl) {
        const video = document.getElementById(props.videoId as string);
        if(player){
          player.destroy();
          player = null;
          
        }
        timer = setTimeout(() => {
          player = new JSWebrtc.Player(props.videoUrl, {
            video,
            autoplay: false,
          });
          if(displayPlay.value){
            player.play(() => {
              console.log('开始播放');
            });
            reconnectVideoTimer();
          }
          

        }, 0);
        
      } else {
        if (player) {
          player.destroy();
          console.log(`%cid为${props.videoId}的窗口已销毁`, 'color:red');
        }
      }
    };

    watch(
      () => props.videoUrl,
      () => {
        nextTick(() => {
          initVideo();
        });
      },
    );
    onMounted(() => {
      initVideo();
    });
    onUnmounted(() => {
      if (player && props.videoUrl) {
        player.destroy();
        console.log(`%cid为${props.videoId}的窗口已销毁`, 'color:red');
      }
      closeVideoTimer();
    });
    const spinning = ref<boolean>(false);
    const finishFirstKey = ()=>{
      const data = {
        id:props.channelId,
        type:'channel'
      }
      console.log(data,'data')
      getDcsVideoResolution(data).then((res)=>{
        if(res.code === 0){
          uEmitter.emit('videoSize',[res.data.width, res.data.height]);
        }
      })
     
      spinning.value = false;
    }
    const preTime = ref<number>(null);

    const videoInterruptHandler = ()=>{
      const videoElement:HTMLMediaElement = document.getElementById(props.videoId as string) as HTMLMediaElement;
      console.log(videoElement.currentTime);
      if(preTime.value !== videoElement.currentTime){
        preTime.value = videoElement.currentTime;
        return;
      }
      if(preTime.value === videoElement.currentTime){
        spinning.value = true; // 开启loading
        getChannelVideoLocationDCS(props.channelId).then(()=>{
            if(player && props.videoUrl){
        
              player.destroy();
              player = null;
              console.log(`%c视频窗口刷新销毁`, 'color:#FF7036');
            }
            const video = document.getElementById(props.videoId as string);
            player = new JSWebrtc.Player(props.videoUrl, {
              video,
              autoplay: true,
            });
            player.play(()=>{
              //
            });
            console.log('%c重新开始播放','color:#164BFF');
        })
      }
    }

    function openVideoTimer(){
      SetInterval.add(`videoTimer${props.videoId}`,videoInterruptHandler,3000);
      SetInterval.run(`videoTimer${props.videoId}`);
    }
    function closeVideoTimer(){
      SetInterval.close(prevideoId.value);
      console.log('关闭定时器');
    };
    function reconnectVideoTimer() {
      if(prevideoId.value === null){
        prevideoId.value = `videoTimer${props.videoId}`;
      }
      if(SetInterval.hasTimer(prevideoId.value)){
        closeVideoTimer();
        setTimeout(() => {
          openVideoTimer();
          prevideoId.value = `videoTimer${props.videoId}`;
        }, 0);
      } else {
        openVideoTimer();
      }
    }
    uEmitter.on('contorlPlay',(val)=>{
      if(!val){
        player.play(() => {
          console.log('开始播放');
        });
        reconnectVideoTimer();
        console.log('播放');
      }else{
        player.pause();
        closeVideoTimer();
        console.log('暂停')

      }
    })

    return {
      spinning,
      finishFirstKey,
      displayPlay,
    };
  },
});
</script>

<style scoped>
.error {
  background-color: black;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #fff;
}
/* video::-webkit-media-controls-enclosure {
  display: none !important;
} */
</style>
