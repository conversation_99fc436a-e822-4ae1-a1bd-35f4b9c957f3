<template>
  <div>
   <a-row :gutter="16" class="rolStyle">
     <a-col :span="6">
       <div class="TreeBox">
         <div class="TreeInBox">
           <div class="Tabs">
             <div class="TabButton" :class="{ active: channelTabs === 1}">巡检任务列表</div>
             <!-- <div class="TabButton" :class="{ active: channelTabs === 0}" @click="changeChannelTabs(0)">单通道</div> -->
             <!-- <div class="TabButton" :class="{ active: channelTabs === 1}" @click="changeChannelTabs(1)">多通道</div> -->
           </div>
           <ul class="TaskList">
             <li 
               v-for="item in channelTabs === 0? singleTaskList : multiTaskList" 
               @click="TaskNode(item.id, channelTabs)"
               class="TaskNode"
               :class="{TaskNodeActive:item.id === nodeActiveId}"
             >
               {{ item.name }}
             </li>
           </ul>
         </div>
       
       </div>
     </a-col>
     <a-col :span="18">
       <div class="tableBox">
         <div class="videoBox">
           <p class="textMode">{{ isManual?'手动':'自动' }}</p>
           <videoScheduling ref="videoScheduling" v-if="channelTabs === 0"></videoScheduling>
           <videoMul ref="videoMulity" v-if="channelTabs === 1"></videoMul>
         </div>
         
       </div>
     </a-col>
   </a-row>
   </div>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, nextTick } from 'vue';
import { useStore } from 'vuex';
import { getDCSTaskList } from '@/api/operatorStation';
import videoScheduling from './components/videoScheduling.vue';
import videoMul from './components/videoMul.vue';
import uEmitter from '@/common/u-event-bus';

export default defineComponent({
 components:{
   videoScheduling,
   videoMul,
 },
 setup() {
   //单通道多通道状态
   const channelTabs = ref<number>(1);
   // 巡检列表
   const singleTaskList = ref([]);    
   const multiTaskList = ref([]);    
   const nodeActiveId = ref<string>();
   const videoScheduling = ref(null); // 单通道ref
   const videoMulity = ref(null); // 多通道ref
   const TaskNode = (id, mode)=>{
     nodeActiveId.value = id;
     if(mode === 0){
       videoScheduling.value.getNodeInfo(id, mode);
     }else{
       videoMulity.value.getNodeInfo(id,mode);
     }
     
   }
   const changeChannelTabs = (val)=>{
     channelTabs.value = val;
     if(val === 0 && singleTaskList.value.length){
       nextTick(()=>{
         nodeActiveId.value = singleTaskList.value[0].id;
         TaskNode(singleTaskList.value[0].id, channelTabs.value)
       })

     }
     if(val === 1 && multiTaskList.value.length){
       nextTick(()=>{
         nodeActiveId.value = multiTaskList.value[0].id;
         TaskNode(multiTaskList.value[0].id, channelTabs.value)
       })
       
     }
       
   }
   // 接收变量（手动自动）
   const isManual = ref<boolean>(false)
   uEmitter.on('changeChannelMode',(data)=>{
     isManual.value = data === 'MANUAL'
   })
   onMounted(() => {
    getDCSTaskList('').then((res)=>{
       if(res.code === 0){
        //  if(Object.keys(res.data).includes('SINGLE_CHANNEL')){
        //    singleTaskList.value = res.data.SINGLE_CHANNEL;
        //    nodeActiveId.value = res.data.SINGLE_CHANNEL[0].id;
        //    TaskNode(res.data.SINGLE_CHANNEL[0].id, channelTabs.value)
        //  }
         
        //  if(Object.keys(res.data).includes('MULTI_CHANNEL')){
           multiTaskList.value = res.data;
        //  }

         if(Object.keys(res.data).includes('MULTI_CHANNEL') && !Object.keys(res.data).includes('SINGLE_CHANNEL')){
           changeChannelTabs(1);
         }
         TaskNode(multiTaskList.value[0].id, channelTabs.value)
       }
     })
   });
   return {
     channelTabs,
     changeChannelTabs,
     singleTaskList,
     multiTaskList,
     nodeActiveId,
     TaskNode,
     videoScheduling,
     videoMulity,
     isManual,
     
   };
 },
});
</script>
<style lang="less" scoped>

.rolStyle {
 width: 100%;
 height: 770px;
}

.TreeBox {
 width: 100%;
 height: 100%;
 background: url('../../assets/border4.png');
 background-size: 97% 100%;
 background-repeat: no-repeat;
 background-position: top center;
 color: aliceblue;
 padding-top:1px;
}

.TreeInBox{
 width: 90%;
 height: 680px;
 overflow: auto;
 margin-left: 10px;
 margin-top: 10px;
 overflow-x: hidden;
 .Tabs{
   display: flex;
   width: 98%;
   height: 28px;
   border-bottom: 1px solid #020718;
   margin-top: 20px;
   margin-bottom: 5px;
   .TabButton{
     border: 1px solid #027ce0;
     border-radius: 5px 5px 0px 0px;
     cursor: pointer;
     font-size: 16px;
     align-items: center;
     justify-items: center;
     text-align: center;
     margin-bottom: 1px;
     background: #022146;
     height: 100%;
     color: white;
     width: 120px;
     margin-right: 2px;
   }
   .active{
     background-color: #027ce0;
     border-bottom: #027ce0;
     box-shadow: 1px 1px 1px black;
   }
   
 }
 ul {
   list-style: none;
   margin: 0;
   padding: 0;
   width: 98%;
   li {
     border: 2px solid #022146;
     border-bottom: none ;
     height: 40px;
     cursor: pointer;
     padding: 8px 16px;
   }
   li:last-child{  
     border-bottom: 2px solid #022146 ;
   }
   .TaskNodeActive{
     background-color: #027ce0;
   }
 }
}
.TreeInBox::-webkit-scrollbar {
 width: 5px;
}
.TreeInBox::-webkit-scrollbar-thumb {
 border-radius: 10px;
 background: rgba(0, 0, 0, 0.2);
}
.TreeInBox::-webkit-scrollbar-track {
 border-radius: 0;
 background: rgba(0, 0, 0, 0.1);
}
.tableBox {
 margin-top: 20px;
 .videoBox {
   background: url('../../assets/border5.png');
   background-repeat: no-repeat;
   background-position: top center;
   background-size: 100% 100%;
   width: 1000px;
   height: 730px;
   padding-top: 70px;
   padding-left: 10px;
   // .textMode{
   //   margin-top: -20px;
   // }
 }
}
.textMode{
 position: absolute;
 top: 40px;
 left: 40px;
 font-size: 20px;
 font-weight: 600;
}

</style>
