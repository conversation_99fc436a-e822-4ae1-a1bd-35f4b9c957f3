<template>
  <div>
    <div v-if="channelList.length !== 0">
      <!-- <videoWin ref="winRef1" :key="Math.random()"></videoWin>
      <videoWin ref="winRef2" :key="Math.random()" ></videoWin> -->
      <videoWin ref="winRef"></videoWin>
      <div :style="{'margin-top': '20px', width: stepWidth+'px'}">
        <a-steps :current="current" type="navigation" size="small">
          <a-step
            v-for="(item,index) in channelList"
            status="process"
          >
            <template #icon>
              <MinusCircleTwoTone v-if="index !== current" two-tone-color="#cccccc"/>
              <CheckCircleTwoTone v-else />
            </template>
            <template #title>
              <a-tooltip placement="bottom">
                <template #title>{{ item.label }}</template>
                <p style="width: 80%;white-space: normal;" :style="{'font-weight':current === index? 600:'',color:route.query.taskId?'#000000':'#ffffff'}">{{ item.label }}</p>
              </a-tooltip>
            </template>
          </a-step>
        </a-steps>
      </div>
    </div>
    <div v-else>
      <a-empty></a-empty>
    </div>

  </div>
</template>
<script lang="ts">
import { defineComponent, ref, onBeforeUnmount,provide,nextTick } from 'vue';
import { useRoute } from 'vue-router'
import videoWin from '../../dcsSplitScreen/index.vue';
import { getTaskNode } from '@/api/operatorStation';
import HWebsocket from '@/common/h-websocket';
import { MinusCircleTwoTone, CheckCircleTwoTone } from '@ant-design/icons-vue';
import uEmitter from '@/common/u-event-bus';
export default defineComponent({
  components:{
    videoWin,
    CheckCircleTwoTone,
    MinusCircleTwoTone
  },
  setup() {
    // rotue
    const route = useRoute();
    const stepWidth = ref(route.query.width || 1152);
    // 进度条
    const current = ref<number>(0); // 当前进度
    // 视频组件的ref
    const winRef = ref(null);
    // const winBoxList = ref<HTMLElement[] | any[]>([]);
    // const winList = (el) => {
    //   if(el){
    //     winBoxList.value.push(el);
    //   }
    // }
    const taskNodeId = ref<string>(''); // 当前选择调度id
    const taskNodeMode = ref<number>(0); // 单通道 0 多通道1
    const channelList = ref([]) // 调度列表

    /**
     * 获取任务信息
     * @param id 任务id
     * @param mode 单通道|多通道
     */
    const getNodeInfo = (id, mode)=>{
      // debugger;
      taskNodeId.value = id;
      taskNodeMode.value = mode;
      const isDcs = !!route.query.taskId
      getTaskNode(id,isDcs).then((res)=>{
        if(res.code === 0){
          // 单通道模式下
          if(mode === 0){
            channelList.value = res.data;
            reconnectWs();
            winSingle(res.data).then((handleMode)=>{
                if(playInfo.value){
                  winRef.value.getflow(playInfo.value, handleMode)
                }

            })

          }
        }
      })
    };
    // 初始化ws
    let ws = null;
    function openWs() {
      ws = new HWebsocket(
        `${(window as any).g.wsApi}/websocket/task-schedule/${taskNodeId.value}`,
      );
      ws.onMessage = (evt) => {
        const wsData = JSON.parse(evt.data);
        handleWs(wsData,taskNodeMode.value);
      };
    };
    /**
     * 处理ws数据
     * @param evt ws数据
     * @param mode 单通道 0 |多通道 1
     */
    const handleWs = (evt:any[], mode)=>{
      if(mode === 0){
        if(isManual.value){
          const manualItem = evt.find((item)=> item.id === currentPlayId.value);
          if(manualItem.mode === 'MANUAL'){
            return;
          }else{
            uEmitter.emit('changeChannelMode', 'AUTOMATIC');
            provide('changeAuto','AUTOMATIC');
          }
        }
        const findRunning = evt.findIndex((item) => item.status === 'RUNNING')
        current.value = findRunning;
      }
      if(mode === 1){
        if(isManual.value){
          const manualItem = evt.find((item)=> item.id === currentPlayId.value);
          if(manualItem.mode === 'MANUAL'){
            return;
          }
        }
        // const newPlayList = [];
        const findRunning = evt.findIndex((item) => item.status === 'RUNNING')
        currentPlayId.value = evt[findRunning].id;
        current.value = findRunning;

      }
    }
    function reconnectWs() {
      let timer = null;
      if (ws === null) {
        openWs();
      } else {
        closeWs();
        timer = setTimeout(() => {
          openWs();
        }, 0);
      }
    }
    function closeWs(){
      if (ws) {
        console.log('关闭ws');
        ws.close();
        ws = null;
      }
    }

    const playInfo = ref<any>(null);// 任务信息
    const currentPlayId = ref<string>(''); //当前播放的id
    const isManual = ref<boolean>(false); // 是否是手动或自动
    uEmitter.on('changeChannelMode',(data)=>{
      if(data === 'MANUAL'){
        isManual.value = true;
        console.log(isManual.value,'修改闸门变量')
      }else{
        isManual.value = false;
        console.log(isManual.value,'修改闸门变量')
      }
    })
    // 单通道
    const winSingle = (taskInfo:any[]) => {
      return new Promise((resolve,reject)=>{
        if(taskInfo.length !== 0){
          if(taskInfo[0].mode === 'MANUAL'){
            current.value = 0;
            currentPlayId.value = taskInfo[0].id;
            playInfo.value = taskInfo[0];
            uEmitter.emit('changeChannelMode','MANUAL')
            resolve('MANUAL')
            return;
          }

          let count = 0; // 计数器
          // 遍历调度列表找出正在运行的调度
          taskInfo.forEach((item,index)=>{
            if(item.status === 'RUNNING'){
              count++
              playInfo.value = item;
              currentPlayId.value = item.id;
              current.value = index;
            }
          })
          // 如果没有进行的调度默认播放第一个
          if(count === 0){
            // console.log(winBoxList.value,playList.value,'boooox')
            currentPlayId.value = taskInfo[0].id;
            playInfo.value = taskInfo[0];
            // playList.value.push(taskInfo[0]);
          }
          // 向上下级组件发送手动或自动状态
          uEmitter.emit('changeChannelMode','AUTOMATIC')
          resolve('AUTOMATIC')
        }
      })

    }
    onBeforeUnmount(() => {
      closeWs();
    });

    return {
      current,
      channelList,
      getNodeInfo,
      winRef,
      currentPlayId,
      stepWidth,
      route,
    };
  },
});
</script>
<style lang="less" scoped>
</style>
