<template>
  <div>
    <div v-if="channelList.length !== 0">
      <div
        v-for="(item, index) in playList"
        :key="index"
        :style="{ display: currentPlayId === item.id ? '' : 'none' }"
      >
        <videoWin :ref="(el) => (myRefs[index] = el)" :key="index"></videoWin>
      </div>
      <div :style="{ 'margin-top': '20px', width: stepWidth + 'px' }">
        <a-steps :current="current" type="navigation" size="small">
          <a-step v-for="(item, index) in channelList" status="process">
            <template #icon>
              <MinusCircleTwoTone v-if="index !== current" two-tone-color="#cccccc" />
              <CheckCircleTwoTone v-else />
            </template>
            <template #description>
              <a-tooltip placement="bottom">
                <template #title>{{ item.label }}</template>
                <p
                  style="width: 80%; white-space: normal"
                  :style="{
                    'font-weight': current === index ? 600 : '',
                    color: route.query.taskId ? '#000000' : '#ffffff',
                  }"
                >
                  {{ item.label }}
                </p>
              </a-tooltip>
            </template>
            <!-- <template #description> -->
            <!-- <a-tooltip placement="bottom">
                <template #title>{{ item.description }}</template>
                {{ `P: ${item.pan}` }}<br/>{{ `T: ${item.tilt}` }}<br/>{{ `Z: ${item.zoom}` }}
              </a-tooltip> -->
            <!-- {{ `P: ${item.pan}` }}<br/>{{ `T: ${item.tilt}` }}<br/>{{ `Z: ${item.zoom}` }} -->
            <!-- </template> -->
          </a-step>
        </a-steps>
        <!-- <a-button @click="test">test</a-button> -->
      </div>
    </div>
    <div v-else>
      <a-empty></a-empty>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, ref, onBeforeUnmount, provide, nextTick, Ref } from "vue";
import { useRoute } from "vue-router";
import videoWin from "../../dcsSplitScreen/index.vue";
import { getTaskNode } from "@/api/operatorStation";
import HWebsocket from "@/common/h-websocket";
import { MinusCircleTwoTone, CheckCircleTwoTone } from "@ant-design/icons-vue";
import uEmitter from "@/common/u-event-bus";
export default defineComponent({
  components: {
    videoWin,
    CheckCircleTwoTone,
    MinusCircleTwoTone,
  },
  setup() {
    // rotue
    const route = useRoute();
    const stepWidth = ref(route.query.width);
    // const textColor = ref<string>(route.query.taskId ? '#ffffff')
    // ref
    const myRefs = ref([]);

    // 进度条
    const current = ref<number>(0); // 当前进度
    const taskNodeId = ref<string>(""); // 当前选择调度id
    const channelList = ref([]); // 调度列表

    /**
     * 获取任务信息
     * @param id 任务id
     * @param mode 单通道|多通道
     */
    const getNodeInfo = async (id, mode) => {
      taskNodeId.value = id;
      const isDcs = true;
      // 获取任务详情
      const resTask = await getTaskNode(id, isDcs);
      if (resTask.code !== 0) return;
      channelList.value = resTask.data;
      // 打开ws
      reconnectWs();
      const handleMode = await winMultiple(resTask.data);
      nextTick(() => {
        if (playList.value.length) {
          console.log(myRefs.value, playList.value);
          myRefs.value.forEach((item, index) => {
            if (playList.value[index].previewUrl !== null) {
              item.getflow(playList.value[index], handleMode);
            }
          });
        }
      });
    };
    // 初始化ws
    let ws = null;
    function openWs() {
      ws = new HWebsocket(`${(window as any).g.wsApi}/websocket/task-schedule/${taskNodeId.value}`);
      ws.onMessage = (evt) => {
        const wsData = JSON.parse(evt.data);
        handleWs(wsData);
      };
    }
    /**
     * 处理ws数据
     * @param evt ws数据
     * @param mode 单通道 0 |多通道 1
     */
    const handleWs = (evt: any[]) => {
      // if(isManual.value){
      //   const manualItem = evt.find((item)=> item.id === currentPlayId.value);
      //   if(manualItem.mode === 'MANUAL'){
      //     return;
      //   }
      // }
      if (evt.find((item) => item.mode === "MANUAL")) {
        uEmitter.emit("changeChannelMode", "MANUAL");
        return;
      } else {
        uEmitter.emit("changeChannelMode", "AUTOMATIC");
        provide("changeAuto", "AUTOMATIC");
      }
      // const newPlayList = [];
      const findRunning = evt.findIndex((item) => item.status === "RUNNING");
      currentPlayId.value = evt[findRunning].id;
      current.value = findRunning;

      playList.value.forEach((item, index) => {
        if (item.previewUrl === null && evt[index].previewUrl) {
          item.previewUrl = evt[index].previewUrl;
          myRefs.value[index].getflow(evt[index], "AUTOMATIC");
        }
        item['is-dcs'] = true;
      });

      // winBoxList.value.forEach((item,index)=>{
      //   if(findRunning === evt.length -1){
      //     item.getflow(evt[0], 'AUTOMATIC');
      //   }
      //   if(findRunning === index){
      //     item.getflow(evt[index + 1], 'AUTOMATIC');
      //   }
      // })
      // evt.forEach((item,index)=>{
      //   if(item.status === 'RUNNING'){
      //     // newPlayList.push(item);
      //     // if(index === evt.length -1){
      //     //   newPlayList.push(evt[0])
      //     // }else{
      //     //   newPlayList.push(evt[index+1])
      //     // }
      //     currentPlayId.value = item.id;
      //     current.value = index;
      //   }
      // })
      // console.log(newPlayList, current.value)
      // playList.value = newPlayList
    };
    function reconnectWs() {
      let timer = null;
      if (ws === null) {
        openWs();
      } else {
        closeWs();
        timer = setTimeout(() => {
          openWs();
        }, 0);
      }
    }
    function closeWs() {
      if (ws) {
        console.log("关闭ws");
        ws.close();
        ws = null;
      }
    }

    const playList = ref([]); // 播放视频列表
    const currentPlayId = ref<string>(""); //当前播放的id
    const isManual = ref<boolean>(false); // 是否是手动或自动
    uEmitter.on("changeChannelMode", (data) => {
      if (data === "MANUAL") {
        isManual.value = true;
      } else {
        isManual.value = false;
      }
    });
    // 多通道
    const winMultiple = (taskInfo: any[]) => {
      return new Promise((resolve, reject) => {
        if (taskInfo.length !== 0) {
          // 找出第一个手动的
          const manualIndex = taskInfo.findIndex((item) => item.mode === "MANUAL");
          // 如果有手动
          if (manualIndex >= 0) {
            playList.value.length = 0;
            currentPlayId.value = taskInfo[manualIndex].id;
            current.value = manualIndex;
            playList.value.push(taskInfo[manualIndex]);
            if (manualIndex === taskInfo.length - 1) {
              playList.value.push(taskInfo[0]);
            } else {
              playList.value.push(taskInfo[manualIndex + 1]);
            }
            // playList.value.push(taskInfo[manualIndex + 1]);
            uEmitter.emit("changeChannelMode", "MANUAL");
            resolve("MANUAL");
            return;
          }
          playList.value.length = 0;
          // 判断是否在执行状态
          const findRunning = taskInfo.findIndex((item) => item.status === "RUNNING");
          if (findRunning >= 0) {
            currentPlayId.value = taskInfo[findRunning].id;
            current.value = findRunning;
            playList.value = taskInfo;
          } else {
            current.value = 0;
            currentPlayId.value = taskInfo[0].id;
            playList.value = taskInfo;
          }
          uEmitter.emit("changeChannelMode", "AUTOMATIC");
          resolve("AUTOMATIC");
        }
      });
    };
    onBeforeUnmount(() => {
      closeWs();
    });

    return {
      current,
      channelList,
      getNodeInfo,
      playList,
      currentPlayId,
      stepWidth,
      route,
      myRefs,
    };
  },
});
</script>
<style lang="less" scoped></style>
