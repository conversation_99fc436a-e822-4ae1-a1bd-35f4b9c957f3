<template>
  <canvas id="canvasCluster"></canvas>
</template>

<script lang="ts">
import { defineComponent, nextTick, onMounted, onUnmounted, watch, onBeforeUnmount } from "vue";
import { fabric } from "fabric";
import HWebsocket from "@/common/h-websocket";
import drawGraph from "@/hooks/darwElement";
import uEmitter from "@/common/u-event-bus";

export default defineComponent({
  props: {
    width: {
      type: Number,
      default: 1152,
    },
    height: {
      type: Number, 
      default: 648,
    },
    // channelId: {
    //   type: String,
    //   default: "",
    // },
    coefficient: {
      type: Object,
      default: () => {},
    },
  },
  setup(props) {
    let canvas = null;
    function initCanvas() {
      canvas = new fabric.Canvas('canvasCluster', {
        selection: false,
      });
      const parent = document.getElementById("canvasCluster");
      console.log(parent);
      if(parent){
        canvas.setWidth(parent.offsetWidth);
        canvas.setHeight(parent.offsetHeight);
        canvas.renderAll();
      }
    }

    // watch(
    //   () => props.channelId,
    //   (nv) => {
    //     if (nv) {
    //       console.log(333);
    //       reconnectWs();
    //     }
    //   },
    // );
    // 绘制图形
    const { draw } = drawGraph();
    // 初始化ws
    let ws = null;
    function openWs(channelId) {

      // drawGraph(fakedata);
      ws = new HWebsocket(`${(window as any).g.wsApi}/websocket/osd/${channelId}`);
      ws.onMessage = (evt) => {
        const wsData = JSON.parse(evt.data);
        if (wsData.eventType === "draw") {
          // drawGraph(wsData.osdItemList);
          draw(canvas, 1, 1, wsData.osdItemList);
        }
        if (wsData.eventType === "clear") {
          if (canvas) {
            canvas.clear();
          }
        }
        if (wsData.eventType === "switchChannelMode") {
          uEmitter.emit("changeChannelMode", "AUTOMATIC");
        }
      };
    }
    function reconnectWs(channelId) {
      let timer = null;
      if (ws === null) {
        openWs(channelId);
      } else {
        closeWs();
        if (canvas) {
          canvas.clear();
          canvas.renderAll();
        }

        timer = setTimeout(() => {
          openWs(channelId);
        }, 0);
      }
    }
    function closeWs() {
      if (ws) {
        console.log("关闭ws");
        ws.close();
        ws = null;
      }
    }
    function disposeCanvas() {
      if (canvas) {
        canvas.clear();
        canvas.dispose();
        canvas = null;
      }
    }
    onMounted(() => {
      initCanvas();
      // reconnectWs();

    });
    onBeforeUnmount(() => {
      if (canvas) {
        closeWs();
        disposeCanvas();
        console.log(`%cid为canvasCluster的canvas已销毁`, "color:blue");
      }
    });
  },
});
</script>

<style scoped>
#canvasCluster {
  width: 100%;
  height: 100%;
}

</style>
