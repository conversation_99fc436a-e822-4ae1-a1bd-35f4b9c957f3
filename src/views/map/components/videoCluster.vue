<template>
  <!-- <div v-if="currentTaskId"> -->
  <div class="video-box">
    <a-spin tip="视频加载中..." :spinning="spinning">
      <template #indicator>
        <LoadingOutlined></LoadingOutlined>
      </template>
      <video
        ref="videoClusterRef"
        autoplay
        muted
        @loadeddata="finishFirstKey"
        class="videoTest"
        style="background-color: black;object-fit: fill;"
      ></video>
    </a-spin>
  </div>

  <!-- </div> -->
  <!-- <a-empty v-else /> -->
</template>

<script setup lang="ts">
import { ref, nextTick } from "vue";
import WebRTCPlayer from "./webRTCCluster";
import HWebsocket from "@/common/h-websocket";
import canvasCluster from "./canvasCluster.vue";
import { LoadingOutlined } from "@ant-design/icons-vue";

const videoInstance = ref(null); // videoWebRTCPlayer的实例
const videoClusterRef = ref(null); // 视窗的Ref

const currentTaskId = ref<string>("");

// 视频loading
const spinning = ref<boolean>(false);
const finishFirstKey = () => {
  spinning.value = false;
};

/**
 * @param task 当前的任务
 * @param scheduleList 当前任务的调度列表
 */
const setVideoCluster = (task, scheduleList) => {
  nextTick(() => {
    // 判断video实例是否建立
    if (videoInstance.value) return;
    // 创建video实例
    videoInstance.value = new WebRTCPlayer(
      task.scheduleEnable,
      scheduleList,
      videoClusterRef.value // 传入video的ref
    );


    // 赋值当前的任务id
    currentTaskId.value = task.id;

    // 开启loading
    spinning.value = true;
  });
};

// 当任务执行中，切换调度视频
const loadCurrentSchedule = (currentSchedule:any)=>{
  videoInstance.value.changeVideo(currentSchedule);
}


defineExpose({
  setVideoCluster, // 初始化视频
  loadCurrentSchedule,
});

</script>

<style lang="less" scoped>
/** 视频播放区域 */
.video-box {
  position: relative;
  width: 100%;
  height: 100%;
  overflow: hidden;
}
.videoTest{
  width: 100%;
  height: 100%;
}
:deep(.ant-spin-nested-loading){
  height: 100%;
}
:deep(.ant-spin-container){
  height: 100%;
}
</style>
