import { getChannelVideoLocationDCS } from "@/api/design";

function replacePortInUrl(url, newPort) {
  return url.replace(/:\d+/, `:${newPort}`);
  // return 'http://192.168.253.216:1985/rtc/v1/play/'
  // return url.replace('/insp', `:${newPort}`).replace('https', `http`);
}
// 发送http请求
async function HttpPost(url, data) {
  const answer = await fetch(url, {
    method: "POST",
    headers: {
      "Content-Type": "application/json",
    },
    body: JSON.stringify(data),
  });
  const answerData = await answer.json();
  return answerData;
}
class WebRTCPlayer {
  scheduleList: any[]; // 当前任务的调度列表
  channelUrlList: string[]; // 当前任务的播放地址
  pcList: any[]; // 连接列表
  video: HTMLVideoElement; // 视频播放器dom
  MediaStreamList: any[]; // 媒体流列表
  streamUrl: any[]; // 流地址
  constructor(
    isEnable: boolean, // 当前任务是否执行
    scheduleList: any[],
    video: HTMLVideoElement
  ) {
    if (typeof video === "undefined") {
      console.error("video is null");
      return;
    }
    this.scheduleList = scheduleList;
    this.video = video;
    this.MediaStreamList = [];
    this.pcList = [];

    // 获取流地址
    this.getStreamUrl(scheduleList).then((res) => {
      // this.channelUrlList = res.map((item) => item.previewUrl);
      this.scheduleList = res;
      this.initPlayer(isEnable);
      console.log("WebRTCCluster initPlayer=====>", this.MediaStreamList, this.video);
    });
  }
  // 初始化播放器

  async initPlayer(isEnable) {
    const { channelUrlList, video, MediaStreamList, scheduleList } = this;

    scheduleList.forEach(async (schedule, index) => {
      const handledStreams = new Set();
      const pc = new RTCPeerConnection();
      pc.ontrack = (event) => {
        const stream = event.streams[0];
        if (!handledStreams.has(stream.id)) {
          // 添加唯一性检查
          console.log("WebRTCCluster ontrack", MediaStreamList);
          MediaStreamList.push(Object.assign(schedule, { stream: stream }));
          if (!isEnable) {
            video.srcObject = MediaStreamList[0].stream;
          }

          this.pcList.push(pc);

          handledStreams.add(stream.id); // 记录已处理流
        }
      };
      pc.addTransceiver("video", { direction: "recvonly" });
      pc.addTransceiver("audio", { direction: "recvonly" });
      const offer = await pc.createOffer();
      await pc.setLocalDescription(offer);
      const data = {
        // api: `${replacePortInUrl((window as any).g.api, port)}${api}`,
        api: (window as any).g.videoUrl,
        clientip: null,
        sdp: offer.sdp,
        streamurl: schedule.previewUrl,
      };
      const answerData = await HttpPost((window as any).g.videoUrl, data);
      pc.setRemoteDescription(new RTCSessionDescription({ type: "answer", sdp: answerData.sdp }));
    });
  }

  // 连接播放器
  private connectPlayer(channellist: number) {}
  // 切换视频
  changeVideo(currentSchedule) {
    if (this.MediaStreamList.length === 0) {
      return;
    }
    this.MediaStreamList.forEach((item) => {
      if (item.id === currentSchedule.id) {
        this.video.srcObject = item.stream;
      }
    });
  }

  // 获取流地址
  async getStreamUrl(scheduleList) {
    try {
      const response = await Promise.all(
        scheduleList.map(async (schedule) => {
          const previewUrl = await getChannelVideoLocationDCS(schedule.channelId);
          schedule.previewUrl = previewUrl.data.previewUrl;
          return schedule;
        })
      );

      return response;
    } catch (e) {
      console.error(e);
    }
  }

  // 刷新流
  async freshStream(id: string[], isEnable) {
    // 关闭当前流
    // 重新获取流地址
    await this.getStreamUrl(id);
    // 重新初始化播放器
    this.initPlayer(isEnable);
  }

  // 关闭播放器
  closePlayer() {
    this.pcList.forEach((pc) => {
      pc.close();
    });
    this.pcList = [];
    this.MediaStreamList = [];
    this.video.srcObject = null;
  }
}

export default WebRTCPlayer;
