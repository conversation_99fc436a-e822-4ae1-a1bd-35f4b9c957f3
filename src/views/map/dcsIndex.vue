<template>
  <div>
    <a-row class="rolStyle">
      <!-- 菜单 -->
      <a-col :span="3">
        <div class="TreeInBox">
          <a-input-search
            size="small"
            style="width: 100%"
            placeholder="请输入巡检名称"
            v-model:value="taskName"
            @search="onTaskSearch"
          />
          <a-menu v-model:selectedKeys="nodeActiveId" @click="taskNodeClick" mode="inline">
            <a-menu-item v-for="(item, index) in taskList" :key="item.id">{{
              item.name
            }}</a-menu-item>
          </a-menu>
        </div>
      </a-col>

      <!-- 视频播放 -->
      <a-col :span="21">
        <div class="clusterBox" v-if="nodeActiveId.length">
          <div class="canvas-box" ref="canvasBox">
            <canvasCluster></canvasCluster>
          </div>
          <div class="videoBox">
            <videoCluster ref="videoCluster"></videoCluster>
          </div>
          <div class="stepBox">
            <a-steps size="small">
              <a-step
                v-for="(item, index) in taskStepList"
                :title="item.label"
                :status="currentScheduleId === item.id ? 'process' : 'success'"
              />
            </a-steps>
          </div>
        </div>
        <div class="clusterBox" v-else>
          <a-empty description="暂无巡检任务" />
        </div>
      </a-col>

      <!-- 巡检步骤条 -->
    </a-row>
  </div>
</template>
<script lang="ts">
import { defineComponent, ref, onMounted, nextTick } from "vue";
import { useStore } from "vuex";
import { getDCSTaskList, getDCSTaskDetail, getTaskNode, getTaskList } from "@/api/operatorStation";
import videoCluster from "./components/videoCluster.vue";
import canvasCluster from "./components/canvasCluster.vue";
import uEmitter from "@/common/u-event-bus";
import SetInterval from "@/common/SetInterval";
import HWebsocket from "@/common/h-websocket";
import scaleBox from "@/components/scaleBox.vue";

export default defineComponent({
  components: {
    videoCluster,
    canvasCluster,
    scaleBox,
  },
  setup() {
    //---------------------------------------------------视频播放-------------------------------------
    const videoCluster = ref(null); // 视频ref
    // 接收变量（手动自动）
    const isManual = ref<boolean>(false);
    uEmitter.on("changeChannelMode", (data) => {
      isManual.value = data === "MANUAL";
    });

    //---------------------------------------------------巡检列表------------------------------------
    const taskList = ref<any>([]); // 巡检列表
    const taskName = ref<string>(""); // 输入框搜索内容
    const nodeActiveId = ref<string[]>([]); // 节点选中id

    // 输入框搜索
    const onTaskSearch = () => {
      getTaskList1();
    };

    // 获取巡检列表
    const getTaskList1 = async () => {
      const res = await getTaskList(taskName.value);
      if (res.code === 0) {
        taskList.value = res.data;
      }
    };

    /**
     * @abstract 任务节点点击事件
     * @param item 当前节点的信息
     * @param key 当前节点的id
     */
    const taskNodeClick = async ({ item, key }) => {
      const res = await getTaskNode(key, false);
      if (res.code === 0) {
        // 当前选择的巡检任务
        nodeActiveId.value = [key];
        // step赋值
        taskStepList.value = res.data.map((item) => {
          return {
            id: item.id,
            label: item.label,
            channelId: item.channelId,
          };
        });
        // 视窗传值
        videoCluster.value.setVideoCluster(
          item, // 当前选择任务
          res.data // 当前任务下的巡检列表
        );

        // 若是非执行状态，设置默认执行任务为第一个任务
        const currentTask = taskList.value.find((item) => item.id === key);
        console.log(key, taskList.value, currentTask, "aacc");
        if (!currentTask.scheduleEnable) {
          currentScheduleId.value = res.data[0].id;
        } else {
          openWs(key);
        }
      }

      // videoScheduling.value.getNodeInfo(id);
    };

    //---------------------------------------------------启动巡检ws----------------------------------------
    let ws = null;
    function openWs(taskId) {
      ws = new HWebsocket(`${(window as any).g.wsApi}/websocket/task-schedule/${taskId}`);
      ws.onMessage = (evt) => {
        const wsData = JSON.parse(evt.data);
        handleWs(wsData);
      };
    }

    // 处理ws数据
    function handleWs(wsData) {
      const runningId = wsData.findIndex((item) => item.status === "RUNNING");
      // 更新步骤条
      currentScheduleId.value = wsData[runningId].id;
      // 更新视频流
      console.log(wsData[runningId], "wsData");
      videoCluster.value.loadCurrentSchedule(wsData[runningId]);
    }

    //---------------------------------------------------巡检步骤条-------------------------------------
    const currentScheduleId = ref<any>("");

    interface StepItem {
      id: number;
      label: string;
      channelId: string;
    }
    const taskStepList = ref<StepItem[]>([]); // 巡检步骤条

    // -----------------------------------------------canvas--------------------------------------------
    const canvasBox = ref(null); // canvasBox ref

    onMounted(() => {
      SetInterval.close("loginTimer"); // 关闭全局定时器
      getTaskList1();
    });
    return {
      taskList,
      nodeActiveId,
      videoCluster,
      isManual,
      taskName,
      onTaskSearch,
      taskNodeClick,
      taskStepList,
      currentScheduleId,
      canvasBox,
    };
  },
});
</script>
<style lang="less" scoped>
@dcsColor: #027ce0;

@borderColor: #f0f0f0;

@stepBoxHeight: 50px;
.rolStyle {
  width: 100%;
  height: 100vh;
}

.TreeInBox {
  width: 100%;
  height: 100vh;
  overflow-x: hidden;
  overflow-y: auto;
  border-right: 1px solid @borderColor;
}
.TreeInBox::-webkit-scrollbar {
  width: 5px;
}
.TreeInBox::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.2);
}
.TreeInBox::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
.clusterBox {
  position: relative;
  width: 100%;
  height: 100vh;
  .canvas-box {
    width: 100%;
    height: calc(100vh - @stepBoxHeight);
    position: absolute;
    top: 0px;
    left: 0px;
    z-index: 100;
  }
  .videoBox {
    width: 100%;
    height: calc(100vh - @stepBoxHeight);
    position: relative;
  }
  .stepBox {
    width: 100%;
    height: @stepBoxHeight;
    padding: 8px 8px;
  }
}

/** menu重定义样式 */
:deep(.ant-menu:not(.ant-menu-horizontal) .ant-menu-item-selected) {
  background-color: @dcsColor;
  color: #fff;
}
:deep(.ant-menu-inline > .ant-menu-item) {
  height: 30px;
  line-height: 30px;
}
/** steps重定义样式 */
:deep(.ant-steps-item-process > .ant-steps-item-container > .ant-steps-item-icon) {
  background: @dcsColor;
}
:deep(
    .ant-steps-item-active
      > .ant-steps-item-container
      > .ant-steps-item-content
      > .ant-steps-item-title
  ) {
  color: @dcsColor !important;
}
</style>
