<template>
  <div>
    <a-row :gutter="16" class="rolStyle">
      <a-col :span="6">
        <div class="TreeBox">
          <div class="titleInBox"></div>
          <div class="TreeInBox">
            <configTree @nodeId="getNodeId"></configTree>
          </div>

        </div>
      </a-col>
      <a-col :span="18">
        <div class="tableBox">
          <a-space>
            <a-date-picker
                v-model:value="searchConfig.startTime"
                show-time
                placeholder="开始时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                @change = "changeStartDate"
                :showToday="false"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
              ></a-date-picker>
              <a-date-picker
                v-model:value="searchConfig.endTime"
                show-time
                placeholder="结束时间"
                format="YYYY-MM-DD HH:mm:ss"
                value-format="YYYY-MM-DD HH:mm:ss"
                @change = "changeEndData"
                :getPopupContainer="(triggerNode) => triggerNode.parentNode"
              ></a-date-picker>
            <div>
              <span style="color: white">状态：</span
              ><a-select v-model:value="searchConfig.confirm" style="width: 150px" :getPopupContainer="(triggerNode) => triggerNode.parentNode" dropdownClassName="style-select-dropdown" >
                <a-select-option value="true" >确认</a-select-option>
                <a-select-option value="false" >未确认</a-select-option>
              </a-select>
            </div>
            <a-button type="primary" @click="getDataList">查询</a-button>
            <!-- <a-button type="primary">导出</a-button> -->
            <a-button type="primary" style="width:100px" @click="getAllData">清除条件</a-button>
          </a-space>
          <a-table
            :columns="columns"
            :dataSource="dataSource"
            style="margin-top: 10px"
            :pagination="pagination"
            @change="pageChange"
            :rowKey="(record) => record.id"
            :row-class-name="
              (_record, index) => {
                if (!_record.confirm) {
                  return 'rowStyle uncomfire';
                } else {
                  return 'rowStyle';
                }
              }
            "
          >
            <template #bodyCell="{ column, record }">
              <template v-if="column.dataIndex === 'imgPath'">
                <!-- <a-button size="small" type="primary" @click="openPic(record)">查看截图</a-button> -->
                <a-dropdown :getPopupContainer="(triggerNode) => triggerNode.parentNode" overlayClassName="style-select-dropdown" v-if="record.imgPath.length === 2">
                  <template #overlay>
                    <a-menu @click="openPic($event,record.imgPath)" class="menu">
                      <a-menu-item key="0" class="menuItem">
                        首次报警截图
                      </a-menu-item>
                      <a-menu-item key="1" class="menuItem">
                        最新报警截图
                      </a-menu-item>
                    </a-menu>
                  </template>
                  <a>
                    查看截图
                    <DownOutlined />
                  </a>
                </a-dropdown>
                <a v-else @click="openPic({key:0},record.imgPath)">查看截图</a>
              </template>
              <template v-if="column.dataIndex === 'operation'">
                <a-button size="small" type="primary" @click="confirm(record.id, record.confirm)">{{
                  record.confirm ? '取消确认' : '确认'
                }}</a-button>
              </template>
            </template>
            <template #emptyText>
              <div style="color: white;font-size: large;">暂无数据</div>
            </template>
          </a-table>
        </div>
      </a-col>
    </a-row>
    <Teleport to="body">
      <PicModal ref="modelParent">
        <template #body>
          <img :src="imgInfo.url" style="width: 960px;height: 540px;" alt="暂无图片"/>
        </template>
      </PicModal>
    </Teleport>
  </div>
</template>

<script lang="ts">
import {
  defineComponent, ref, onMounted, reactive, computed, onBeforeUnmount
} from 'vue';
import dayjs from 'dayjs';
import { getProjectForDCS } from "@/api/project";
import { getAlarmTable, comfirmAlarm,getAlarmTableForDcs,comfirmAlarmForDcs } from '@/api/operatorStation';
import configTree from '../configPage/components/configTree.vue';
import { JsonParseFun } from '@/common/utils';
import SetInterval from '@/common/SetInterval';
import PicModal from '@/components/PicModal.vue';
import { LoadingOutlined, DownOutlined } from '@ant-design/icons-vue';

export default defineComponent({
  components: {
    configTree,
    PicModal,
    DownOutlined
  },
  setup() {
    const columns = [
      {
        title: '通道',
        dataIndex: 'channelName',
      },
      {
        title: '实例',
        dataIndex: 'presetName',
      },
      {
        title: '截图',
        dataIndex: 'imgPath',
      },
      {
        title: '发生时间',
        dataIndex: 'time',
      },
      {
        title: '报警类型',
        dataIndex: 'type',
      },
      {
        title: '操作',
        dataIndex: 'operation',
        width:200
      },
    ];
    const searchConfig = reactive({
      startTime: '',
      endTime: '',
      confirm: '',
      // targetId:12,
    });
    const dataSource = ref([]);
    const getDataList = async () => {
      const data = {
        size: 20,
        current: 1,
      };
      const page = {
          size: pagination.value.pageSize,
          current: pagination.value.current,
      };
      const paramData = Object.assign(page, searchConfig);
      getAlarmTable(paramData).then((res) => {
        if (res.code === 0) {
          dataSource.value = res.data.records;
          tableData.total = res.data.total;
        }
      });
    };
    // 获取全部
    const getAllData = ()=>{
      searchConfig.confirm = '';
      searchConfig.startTime = '';
      searchConfig.endTime = '';
      const page = {
        size: pagination.value.pageSize,
        current: pagination.value.current,
      };
      const paramData = Object.assign(page, searchConfig);
      getAlarmTable(paramData).then((res) => {
        if (res.code === 0) {
          dataSource.value = res.data.records;
          tableData.total = res.data.total;
        }
      });

    }
    // 时间选择
    const changeStartDate = (date)=>{
      if(dayjs(date).unix() > dayjs(searchConfig.endTime).unix() ){
        searchConfig.startTime = ''
      }
      if(!searchConfig.startTime){
        searchConfig.startTime = ''
      }
    }
    const changeEndData  = (date)=>{
      if(dayjs(date).unix() < dayjs(searchConfig.startTime).unix()){
        searchConfig.endTime = '';
      }
      console.log(searchConfig.endTime)
      if(!searchConfig.endTime){
        console.log('清空')
        searchConfig.endTime = ''
      }
    }
    const getNodeId = (val) => {
        getDataList();

    };
    // 分页
    const tableData = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      currentPageSize: 0, // 当前页面有几条数据
      tableList: [],
    });
    const pagination = computed(() => ({
      total: tableData.total,
      current: tableData.current, //
      pageSize: 10,
      showTotal: () => `共 ${tableData.total} 条`,
      showLessItems: true,
      defaultPageSize: tableData.pageSize,
      showSizeChanger: false,
    }));
    const pageChange = ({ current, total }) => {
      tableData.current = current;
      getDataList();
      // setCurrentPage(current);
    };
    // 确定报警
    const confirm = (id, isconfirm) => {
      comfirmAlarmForDcs({ id, confirm: !isconfirm }).then((res) => {
        if (res.code === 0) {
          getDataList();
        }
      });
    };
    // 图像弹窗
    const modelParent = ref(null);
    const imgInfo = reactive({
      url: '',
      title: '',
    });
    const openPic = ({key},record) => {
      imgInfo.url = record[key];
      imgInfo.title = record.type;
      modelParent.value.show = true;
    };
    SetInterval.add('intervalUse',()=>{
      getDataList();
    },10000)
    onMounted(() => {
      // size=${data.size}&current=${data.current}
      getDataList();
      SetInterval.run('intervalUse');
    });
    onBeforeUnmount(() => {
      SetInterval.close('intervalUse');
    });
    return {
      columns,
      dataSource,
      openPic,
      imgInfo,
      modelParent,
      confirm,
      pagination,
      pageChange,
      getNodeId,
      changeStartDate,
      changeEndData,
      getDataList, // 查询
      getAllData,
      searchConfig,
    };
  },
});
</script>

<style scoped>
.rolStyle {
  width: 100%;
  height: 770px;
}
.TreeBox {
  width: 100%;
  height: 100%;
  background: url('../../assets/border4.png');
  background-size: 97% 100%;
  background-repeat: no-repeat;
  background-position: top center;
  color: aliceblue;
  padding-top:1px;
}
.titleInBox{
  width: 80%;
  height: 45px;
  background: url('../../assets/title2.png');
  background-repeat: no-repeat;
  background-position: top center;
  background-size: 100% 100%;
  margin-top: 10px;
  margin-left: 20px;
}
.TreeInBox{
  width: 90%;
  height: 680px;
  overflow: auto;
  margin-left: 10px;
  margin-top: 10px;
  overflow-x: hidden;
}
.TreeInBox::-webkit-scrollbar {
  width: 5px;
}
.TreeInBox::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.2);
}
.TreeInBox::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}
.tableBox {
  margin-top: 20px;
}

:deep(.ant-select:not(.ant-select-customize-input) .ant-select-selector){
  background-color: rgb(6, 56, 119) !important;
  border: 1px solid rgb(1, 97, 161);
  color: #fff;
}

:deep(.style-select-dropdown){
  background-color: rgb(6, 56, 119) !important;
}

:deep(.ant-select-item){
  background-color: rgb(6, 56, 119) !important;
  color: #fff !important;
}

:deep(.ant-select-item-option-active){
  background-color: rgb(8, 100, 212) !important;
  color: #fff !important;
}

/* 时间选择器 */
:deep(.ant-picker){
  background-color: #1b317d;
  border: 1px solid rgb(1, 97, 161);
}

:deep(.ant-picker-clear){
  background-color: #1b317d;
  color: rgba(255, 255, 255, 0.25);
}
:deep(.ant-picker-input > input){
  color: white;
}

/* 树 */
:deep(.ant-tree) {
  background: rgba(0, 0, 0, 0);
  color: aliceblue;
}
:deep(.ant-tree .ant-tree-node-content-wrapper:hover) {
  background: rgba(1, 202, 217, 0.1);
}
:deep(.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected) {
  background: rgba(1, 202, 217, 0.2);
}
/* 表格 */
:deep(.ant-table) {
  background: #06122e;
  color: white;
}
:deep(.ant-table-thead > tr > th) {
  background: #112138;
  color: #4cb5c4;
  border-bottom: 1px solid rgb(1, 97, 161);
}
:deep(.ant-empty-normal) {
  color: white;
}
:deep(.ant-table-body)::-webkit-scrollbar {
  width: 5px !important;
}
:deep(.ant-table-body)::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgb(6, 56, 119);
}
:deep(.ant-table-body)::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}

:deep(.uncomfire) td {
  color: #f1cd1e;
}

:deep(.rowStyle) td{
  border-bottom: 1px solid rgb(1, 97, 161);
  transition: none;
  background: #112138;
}

:deep(.rowStyle:hover > td){
  background-color: #1b317d !important;
}
:deep(.ant-table-tbody>tr>td.ant-table-cell-row-hover){
  background: #1b317d !important;
  transition: 0.3s !important;
}


:deep(.ant-table-tbody > tr > td){
  border-bottom:1px solid #03548d !important;
  background: #06122e;
}

:deep(.ant-table-tbody > tr.ant-table-placeholder:hover > td){
  background-color: #06122e !important;
}

/* 分页 */
:deep(.ant-pagination) {
  color: white;
}

:deep(.ant-pagination-item-active) {
  background-color: #1b317d !important;
  border: 1px solid #4cb5c4 !important;
}
:deep(.ant-pagination-item){
  border: none;
  background-color: #1b317d !important;
}
:deep(.ant-pagination-item a){
  color: #fff !important;
}
:deep(.ant-pagination-prev .ant-pagination-item-link)  {
  background: rgba(0, 0, 0, 0);
  color: #4cb5c4;
  border: none !important;
}

:deep(.ant-pagination-next .ant-pagination-item-link)  {
  background: rgba(0, 0, 0, 0) !important;
  color: #4cb5c4;
  border: none !important;
}
:deep(.ant-pagination-jump-next .ant-pagination-item-container .ant-pagination-item-ellipsis ){
  color: #2e6e7d !important;
}
:deep(.ant-pagination-jump-prev .ant-pagination-item-container .ant-pagination-item-ellipsis){
  color:#2e6e7d !important;
}
:deep(.ant-select:not(.ant-select-customize-input) .ant-select-selector){
  background-color: rgb(6, 56, 119) !important;
  border: 1px solid rgb(1, 97, 161);
  color: #fff;
}

:deep(.menu){
  background-color: rgb(6, 56, 119) !important;
}

:deep(.menuItem){
  background-color: rgb(6, 56, 119) !important;
  color: #fff;
}

:deep(.menuItem:hover){
  background-color: #0864d4 !important;
}




</style>
