<template>
  <div class="box">
    <!-- <a-row>
        <a-col v-for="(item, index) in 4" :key="index" :span="wsSpan">
          <div style="width:100%">
          </div>
        </a-col>
      </a-row> -->
    <single-video
      v-for="(item, index) in wndList"
      :key="index"
      :videoId="`video-webrtc${index}`"
      :canvasId="`canvas${index}`"
      :width="item.width"
      :height="item.height"
      :videoUrl="item.url"
      :channelId="item.channelId"
    ></single-video>
    <div
      :style="{
        position: 'absolute',
        'z-index': 200,
        display: 'flex',
        'flex-wrap': 'wrap',
        width: '1152px',
        height: '648px',
        'align-content':'flex-start',
      }"
    >
      <single-canvas
        v-for="(item, index) in canvasList"
        :key="index"
        :canvasId="`canvas${index}`"
        :channelId="item.channelId"
        :width="item.width"
        :height="item.height"
        :coefficient="item.coefficient"
      ></single-canvas>
    </div>
    <div
      :style="{
        position: 'absolute',
        'z-index': 300,
        display: 'flex',
        'flex-wrap': 'wrap',
        'align-content':'flex-start',
        width: '1152px',
        height: '648px',
      }"
    >
      <div
        v-for="(item, index) in toolBarList"
        :key="index"
        :style="{ width: `${item.width}px`, height: `${item.height}px` }"
      >
        <tool-bar
          :channelId="item.channelId"
          :videoId="`video-webrtc${index}`"
          :isWebrtc="item.isWebrtc"
          :isBig="item.width < 390"
          :isPTZ="item.isPTZ"
          :channelName="item.channelName"
        ></tool-bar>
      </div>
    </div>
  </div>
  <div class="pagination">
    <div>
      <span style="color: white">窗口数：</span>
      <a-select 
        v-model:value="pagination.wndNum" 
        @change="changeWndNum" size="small" 
        :getPopupContainer="(triggerNode) => triggerNode.parentNode"      
        dropdownClassName="style-select-dropdown"
      >
        <a-select-option value="1">1x1</a-select-option>
        <a-select-option value="4">2x2</a-select-option>
        <a-select-option value="9">3x3</a-select-option>
        <a-select-option value="16">4x4</a-select-option>
      </a-select>
    </div>
    <div style="margin-left: 10px">
      <span style="color: white">页面数：</span>
      <a-input
        v-model:value="pagination.currentPage"
        size="small"
        style="width: 50px;
               background-color: rgb(6, 56, 119);
               border: 1px solid rgb(1, 97, 161);
               color: #fff;"
        readonly
      ></a-input>
    </div>
    <div style="margin-left: 278px">
      <a-pagination
        style="color: white"
        v-model:current="pagination.currentPage"
        v-model:page-size="pagination.pageSize"
        simple
        :total="pagination.total"
        @change="pageChange"
      />
    </div>

    <!-- <a-button size="small" style="margin-left: 434px">应用</a-button> -->
  </div>
</template>

<script lang="ts">
import {
  defineComponent, reactive, onMounted, nextTick, onBeforeUnmount, ref, watch,
} from 'vue';
import { getVideoUrl } from '@/api/operatorStation';
import singleVideo from './components/singleVideo.vue';
import singleCanvas from './components/singleCanvas.vue';
import toolBar from './components/newhkvPlayer/index.vue';

export default defineComponent({
  components: { singleVideo, singleCanvas, toolBar },
  name: 'splitScreen',
  setup() {
    const currentWndType = ref<string>('Mask1x1');
    const wndList = ref([]);
    const canvasList = ref([]);
    const toolBarList = ref([
      {
        width: 1152,
        height: 648,
        channelId: '',
        isWebrtc: true,
        channelName:'',
        isPTZ:false
      },
    ]);

    // 改变窗口数
    const pagination = reactive({
      wndNum: '1',
      pageSize: 1,
      total: 0,
      currentPage: 1,
    }); // 分页信息
    // 改变窗口数
    const changeWndNum = (val) => {
      let tempwndList = [];
      let tempCanvasList = [];
      let tempToolbarList = [];
      const data = {
        size: Number(pagination.wndNum),
        current: 1,
      };
      getVideoUrl(data).then((res) => {
        if (res.code === 0) {
          pagination.currentPage = res.data.current;
          pagination.total = res.data.total;
          console.log(res.data.records[0]);
          if (val === '1') {
            tempwndList.push({
              width: 1152,
              height: 648,
              url: res.data.records[0].url,
              channelId: res.data.records[0].id,
            });
            tempCanvasList.push({
              width: 1152,
              height: 648,
              channelId: res.data.records[0].id,
              coefficient: 0.6,
            });
            tempToolbarList.push({
              width: 1152,
              height: 648,
              channelId: res.data.records[0].id,
              isWebrtc: !!res.data.records[0].url,
              isPTZ: res.data.records[0].deviceType.includes('PTZ'),
              channelName:res.data.records[0].channelName
            });
            pagination.pageSize = 1;
            wndList.value = tempwndList;
            canvasList.value = tempCanvasList;
            toolBarList.value = tempToolbarList;
          } else if (val === '4') {
            tempwndList = res.data.records.map((item) => ({
              width: 576,
              height: 324,
              url: item.url,
              channelId: item.id,
            }));
            tempCanvasList = res.data.records.map((item) => ({
              width: 576,
              height: 324,
              channelId: item.id,
              coefficient: 0.3,
            }));
            tempToolbarList = res.data.records.map((item) => ({
              width: 576,
              height: 324,
              channelId: item.id,
              isWebrtc: !!item.url,
              isPTZ: item.deviceType.includes('PTZ'),
              channelName:item.channelName
            }));
            pagination.pageSize = 4;
            wndList.value = tempwndList;
            canvasList.value = tempCanvasList;
            toolBarList.value = tempToolbarList;
          } else if (val === '9') {
            tempwndList = res.data.records.map((item) => ({
              width: 384,
              height: 216,
              url: item.url,
              channelId: item.id,
            }));
            tempCanvasList = res.data.records.map((item) => ({
              width: 384,
              height: 216,
              channelId: item.id,
              coefficient: 0.2,
            }));
            tempToolbarList = res.data.records.map((item) => ({
              width: 384,
              height: 216,
              channelId: item.id,
              isWebrtc: !!item.url,
              isPTZ: item.deviceType.includes('PTZ'),
              channelName:item.channelName
            }));
            pagination.pageSize = 9;
            wndList.value = tempwndList;
            canvasList.value = tempCanvasList;
            toolBarList.value = tempToolbarList;
          } else if (val === '16') {
            tempwndList = res.data.records.map((item) => ({
              width: 288,
              height: 162,
              url: item.url,
              channelId: item.id,
            }));
            tempCanvasList = res.data.records.map((item) => ({
              width: 288,
              height: 162,
              channelId: item.id,
              coefficient: 0.15,
            }));
            tempToolbarList = res.data.records.map((item) => ({
              width: 288,
              height: 162,
              channelId: item.id,
              isWebrtc: !!item.url,
              isPTZ: item.deviceType.includes('PTZ'),
              channelName:item.channelName
            }));
            pagination.pageSize = 16;
            wndList.value = tempwndList;
            canvasList.value = tempCanvasList;
            toolBarList.value = tempToolbarList;
          }
        }
      });
    };
    // 改变当前页
    const pageChange = (page, pageSize) => {
      const data = {
        size: pageSize,
        current: page,
      };
      getVideoUrl(data).then((res) => {
        let tempwndList = [];
        let tempCanvasList = [];
        let tempToolbarList = [];
        if (res.code === 0) {
          pagination.currentPage = res.data.current;
          pagination.total = res.data.total;
          if (pageSize === 1) {
            tempwndList.push({
              width: 1152,
              height: 648,
              url: res.data.records[0].url,
            });
            tempCanvasList.push({
              width: 1152,
              height: 648,
              channelId: res.data.records[0].id,
              coefficient: 0.6,
            });
            tempToolbarList.push({
              width: 1152,
              height: 648,
              channelId: res.data.records[0].id,
              isWebrtc: !!res.data.records[0].url,
              isPTZ: res.data.records[0].deviceType.includes('PTZ'),
              channelName:res.data.records[0].channelName
            });
            // console.log(tempwndList);
            wndList.value = tempwndList;
            canvasList.value = tempCanvasList;
            toolBarList.value = tempToolbarList;
          } else if (pageSize === 4) {
            tempwndList = res.data.records.map((item) => ({
              width: 576,
              height: 324,
              url: item.url,
            }));
            tempCanvasList = res.data.records.map((item) => ({
              width: 576,
              height: 324,
              channelId: item.id,
              coefficient: 0.3,
            }));
            tempToolbarList = res.data.records.map((item) => ({
              width: 576,
              height: 324,
              channelId: item.id,
              isWebrtc: !!item.url,
              isPTZ: item.deviceType.includes('PTZ'),
              channelName:item.channelName
            }));
            wndList.value = tempwndList;
            canvasList.value = tempCanvasList;
            toolBarList.value = tempToolbarList;
          } else if (pageSize === 9) {
            tempwndList = res.data.records.map((item) => ({
              width: 384,
              height: 216,
              url: item.url,
            }));
            tempCanvasList = res.data.records.map((item) => ({
              width: 384,
              height: 216,
              channelId: item.id,
              coefficient: 0.2,
            }));
            tempToolbarList = res.data.records.map((item) => ({
              width: 384,
              height: 216,
              channelId: item.id,
              isWebrtc: !!item.url,
              isPTZ: item.deviceType.includes('PTZ'),
              channelName:item.channelName
            }));
            wndList.value = tempwndList;
            canvasList.value = tempCanvasList;
            toolBarList.value = tempToolbarList;
          } else if (pageSize === 16) {
            tempwndList = res.data.records.map((item) => ({
              width: 288,
              height: 162,
              url: item.url,
            }));
            tempCanvasList = res.data.records.map((item) => ({
              width: 288,
              height: 162,
              channelId: item.id,
              coefficient: 0.15,
            }));
            tempToolbarList = res.data.records.map((item) => ({
              width: 288,
              height: 162,
              channelId: item.id,
              isWebrtc: !!item.url,
              isPTZ: item.deviceType.includes('PTZ'),
              channelName:item.channelName
            }));
            wndList.value = tempwndList;
            canvasList.value = tempCanvasList;
            toolBarList.value = tempToolbarList;
          }
        }
      });
    };
    onMounted(() => {
      changeWndNum(pagination.wndNum);
    });
    // 下方工具条控制

    return {
      wndList,
      currentWndType,
      // 窗口数
      pagination,
      changeWndNum,
      pageChange,
      // canvas列表
      canvasList,
      // 工具条
      toolBarList,
    };
  },
});
</script>

<style scoped>
.box {
  width: 1152px;
  height: 648px;
  margin-left: 7%;
  margin-top: 1%;
  /* box-sizing: border-box; */
  display: flex;
  align-content: flex-start;
  flex-wrap: wrap;
  background-color: #040f2f;
}
.pagination {
  display: flex;
  flex-wrap: wrap;
  row-gap: 8px;
  margin-top: 10px;
  margin-left: 7%;

}
.selectWnd {
  justify-content: flex-end;
}

::v-deep .ant-pagination-simple-pager > input {
  background-color: rgb(6, 56, 119) !important;
  border: 1px solid rgb(1, 97, 161);
  color: #fff !important;
}

/* select style */
:deep(.ant-select:not(.ant-select-customize-input) .ant-select-selector){
  background-color: rgb(6, 56, 119) !important;
  border: 1px solid rgb(1, 97, 161);
  color: #fff;
}

:deep(.style-select-dropdown){
  background-color: rgb(6, 56, 119) !important;
}

:deep(.ant-select-item){
  background-color: rgb(6, 56, 119) !important;
  color: #fff !important;
}

:deep(.ant-select-item-option-active){
  background-color: rgb(8, 100, 212) !important;
  color: #fff !important;
}
:deep(.anticon){
  color: #fff !important;
}
</style>
