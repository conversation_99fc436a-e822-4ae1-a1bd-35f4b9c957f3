<template>
  <canvas :id="canvasId" :width="width" :height="height" style="border: 1px solid black"></canvas>
</template>

<script lang="ts">
import {
  defineComponent, nextTick, onMounted, onUnmounted, watch, onBeforeUnmount,
} from 'vue';
import { fabric } from 'fabric';
import HWebsocket from '@/common/h-websocket';
import { JsonParseFun, JsonStrFun } from '@/common/utils';
import drawGraph from '@/hooks/darwElement';
import uEmitter from '@/common/u-event-bus';

export default defineComponent({
  props: {
    width: {
      type: Number,
      default: 1152,
    },
    height: {
      type: Number,
      default: 648,
    },
    canvasId: {
      type: String,
      default: 'canvas',
    },
    channelId: {
      type: String,
      default: '',
    },
    coefficient: {
      type: Number,
      default: 0,
    },
  },
  setup(props) {
    let canvas = null;
    function initCanvas() {
      canvas = new fabric.Canvas(props.canvasId, {
        selection: false,
      });
    }
    watch(
      () => props.coefficient,
      () => {
        nextTick(() => {
          if (canvas) {
            console.log('改变画布', props.width, props.height);
            canvas.clear();
            canvas.setWidth(props.width);
            canvas.setHeight(props.height);
          }
        });
      },
    );
    watch(
      () => props.channelId,
      (nv) => {
        if (nv) {
          reconnectWs();
        }
      },
    );
    // 绘制图形
    const { draw } = drawGraph();
    // 初始化ws
    let ws = null;
    function openWs() {
      // drawGraph(fakedata);
      ws = new HWebsocket(
        `${(window as any).g.wsApi}/websocket/osd/${props.channelId}`,
      );
      ws.onMessage = (evt) => {
        const wsData = JSON.parse(evt.data);
        if (wsData.eventType === 'draw') {
          draw(canvas,props.coefficient,props.coefficient,wsData.osdItemList)
        }
        if (wsData.eventType === 'clear') {
          if(canvas){
            canvas.clear();
          }
        }
        if (wsData.eventType === 'switchChannelMode') {
          uEmitter.emit('changeChannelMode', 'AUTOMATIC');
        }
      };
    }
    function reconnectWs() {
      let timer = null;
      if (ws === null) {
        openWs();
      } else {
        closeWs();
        if(canvas){
          canvas.clear();
          canvas.renderAll();
        }


        timer = setTimeout(() => {
          openWs();
        }, 0);
      }
    }
    function closeWs() {
      if (ws) {
        console.log('关闭ws');
        ws.close();
        ws = null;
      }
    }
    function disposeCanvas() {
      if (canvas) {
        canvas.clear();
        canvas.dispose();
        canvas = null;
      }
    }
    onMounted(() => {
      initCanvas();
      openWs();
    });
    onBeforeUnmount(() => {
      if (canvas) {
        closeWs();
        disposeCanvas();
        console.log(`%cid为${props.canvasId}的canvas已销毁`, 'color:blue');
      }
    });
    return {};
  },
});
</script>

<style scoped></style>
