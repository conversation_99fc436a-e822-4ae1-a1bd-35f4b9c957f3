<template>
  <!-- <a-spin tip="视频加载中..." :spinning="spinning" size="large">
    <template #indicator>
      <LoadingOutlined></LoadingOutlined>
    </template> -->
  <!-- <a-button @click="playerRece">kkkkk</a-button> -->
  <a-spin tip="视频加载中..." :spinning="spinning" size="large" v-if="videoUrl">
    <template #indicator>
      <LoadingOutlined></LoadingOutlined>
    </template>
    <video
      :id="videoId"
      muted
      autoplay
      :width="width"
      :height="height"
      v-if="videoUrl"
      webkit-playsinline="true"
      playsinline="true"
      x5-playsinline
      style="object-fit: fill"
      @loadeddata="finishFirstKey"
    ></video>
  </a-spin>
  <div :style="{ width: `${width}px`, height: `${height}px` }" class="error" v-if="!videoUrl">
    无法预览
  </div>
</template>

<script lang="ts">
import { defineComponent, nextTick, onMounted, onUnmounted, watch, ref } from "vue";
import { LoadingOutlined } from "@ant-design/icons-vue";
import JSWebrtc from "@/components/jswebrtc.min.js";
import { JsonParseFun } from "@/common/utils";
import { clickTreeNode, getChannelVideoLocation } from "@/api/design";
import SetInterval from "@/common/SetInterval";

export default defineComponent({
  props: {
    videoId: {
      type: String,
      default: "video-webrtc",
    },
    width: {
      type: Number,
      default: 1152,
    },
    height: {
      type: Number,
      default: 648,
    },
    videoUrl: {
      type: String || null,
      default: null,
    },
    channelId: {
      type: String || null,
      default: "",
    },
  },
  components: {
    LoadingOutlined,
  },
  setup(props, { emit }) {
    let player = null; // webrtc对象实例
    const initVideo = () => {
      console.log(props.videoUrl);
      spinning.value = true;
      if (props.videoUrl) {
        const video = document.getElementById(props.videoId as string);
        player = new JSWebrtc.Player(props.videoUrl, {
          video,
          autoplay: true,
        });
        player.play(() => {
          // console.log('%c开始播放','color:green');
        });
        openVideoTimer();
        console.log("%c开始播放", "color:green");
      } else {
        console.log(player,'abababab')
        if (player) {
          
          // player.destroy();
          // console.log(`%cid为${props.videoId}的窗口已销毁`, "color:red");
        }
      }
    };
    watch(
      () => props.videoUrl,
      () => {
        console.log(player, props.videoUrl, "change");
        if (player) {
          console.log(`%c流已断开`, "color:red");
          player.destroy();
          player = null;
        }
        nextTick(() => {
          initVideo();
        });
      },
      { deep: true }
    );

    const preTime = ref<number>(null);

    const videoInterruptHandler = () => {
      const videoElement: HTMLMediaElement = document.getElementById(
        props.videoId as string
      ) as HTMLMediaElement;
      console.log(videoElement.currentTime);
      if (preTime.value !== videoElement.currentTime) {
        preTime.value = videoElement.currentTime;
        return;
      }
      if (preTime.value === videoElement.currentTime) {
        spinning.value = true; // 开启loading
        console.log(`%cchannelId`, "color:red",props.channelId);
        getChannelVideoLocation(props.channelId).then(() => {
          if (player && props.videoUrl) {
            player.destroy();
            player = null;
            console.log(`%c视频窗口刷新销毁`, "color:#FF7036");
          }
          const video = document.getElementById(props.videoId as string);
          player = new JSWebrtc.Player(props.videoUrl, {
            video,
            autoplay: true,
          });
          player.play(() => {
            //
          });
          console.log("%c重新开始播放", "color:#164BFF");
        });
      }
    };
    function openVideoTimer() {
      SetInterval.add(`videoTimer${props.videoId}`, videoInterruptHandler, 3000);
      SetInterval.run(`videoTimer${props.videoId}`);
    }
    function closeVideoTimer() {
      if (SetInterval.hasTimer(`videoTimer${props.videoId}`)) {
        SetInterval.close(`videoTimer${props.videoId}`);
      }

      console.log("关闭定时器");
    }
    // watch(
    //   () => props.channelId,
    //   (nv) => {
    //     if (nv) {
    //       openListenWs();
    //     }
    //   },
    // );
    onMounted(() => {
      initVideo();
      // openListenWs();
    });
    onUnmounted(() => {
      if (player && props.videoUrl) {
        player.destroy();
        console.log(`%cid为${props.videoId}的窗口已销毁`, "color:red");
      }
      closeVideoTimer();
    });
    const spinning = ref<boolean>(false);
    const finishFirstKey = () => {
      spinning.value = false;
    };
    return {
      spinning,
      finishFirstKey,
    };
  },
});
</script>

<style scoped>
.error {
  background-color: black;
  display: flex;
  flex-direction: row;
  align-items: center;
  justify-content: center;
  font-size: 20px;
  color: #fff;
}
/* video::-webkit-media-controls-enclosure {
  display: none !important;
} */
:deep(.ant-spin-container) {
  line-height: 0;
}

video::-webkit-media-controls-play-button {
  display: none;
}
video {
  pointer-events: none;
}
</style>
