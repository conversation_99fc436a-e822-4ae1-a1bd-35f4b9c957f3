<template>
  <div>
    <a-modal
      v-model:visible="visible"
      :title="title"
      okText="绑定"
      cancelText="关闭"
      :maskClosable="false"
      destroyOnClose
      @ok="handleOk"
      :afterClose="afterColse"
      style="width: 1186px;"

    >
     <a-table
      :columns="columns"
      :data-source="dataSource"
      :row-selection="rowSelection"
      rowKey="id"
      :pagination="false"
      :defaultExpandAllRows="true"
      :scroll="{y:600}"
      v-if="dataSource.length"
     >
     </a-table>

    </a-modal>
  </div>
</template>

<script lang="ts">
import {
  defineComponent, nextTick, onMounted, reactive, ref, toRaw,
} from 'vue';
import { message } from 'ant-design-vue';
import type { TableColumnsType } from 'ant-design-vue';
import { getThirdInterFace, bindInterface, checkBindInterface } from '../../../../../api/project';

export default defineComponent({
  setup(props,{emit}) {
    const visible = ref<boolean>(false);
    const title = ref<string>('');

    const recordId = ref<string>(); //主表的Id
    const columns:TableColumnsType = [
      {
        title:'接口名称',
        dataIndex:'name',
      },
      {
        title:'接口地址',
        dataIndex:'url'
      }
    ]
    const dataSource = ref<any>([]);

    // 保存
    const handleOk = ()=>{
      const data = {
        appId:recordId.value,
        interfaceIds: rowSelection.value.selectedRowKeys,
      }
      bindInterface(data).then(res=>{
        if(res.code === 0){
          message.success('操作成功')
          visible.value = false;
        }
      })

    }
    // 关闭弹窗
    const afterColse = ()=>{

    }
    const openModal = (record)=>{
      recordId.value = record.id;
      title.value = `接口授权 ===> ${record.name}`
      checkBindInterface(recordId.value).then(res=>{
        if(res.code === 0){
          rowSelection.value.selectedRowKeys = res.data;
          getThirdInterFace().then(res=>{
            dataSource.value = Object.keys(res.data).map((item,index)=>{
              return{
                id:`father${index}`,
                name:item,
                children:res.data[item]
              }
            })
            visible.value = true;
          })
        }
      })

    }

    const rowSelection = ref({
      checkStrictly: false,
      selectedRowKeys:[],
      onChange: (selectedRowKeys:string[], selectedRows) => {
        console.log(`selectedRowKeys: ${selectedRowKeys}`, 'selectedRows: ', selectedRows);
        rowSelection.value.selectedRowKeys = selectedRowKeys;

      },
      getCheckboxProps: (record) => ({
        disabled: record.id.includes('father'),
      }),
    });



    // 获取树结构
    return {
      visible,
      title,
      handleOk,
      afterColse,
      openModal,
      columns,
      dataSource,
      rowSelection,
    };
  },
});
</script>

<style lang="less" scoped>
.btnBox{
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 10px;
}

</style>
