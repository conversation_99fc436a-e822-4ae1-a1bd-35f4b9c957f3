<template>
  <div>
    <div class="BtnBox">
      <a-space>
        <!-- <a-input v-model:value="appName" placeholder="应用名称" allow-clear></a-input>
        <a-button @click="getTableData" :icon="h(SearchOutlined)">搜索</a-button> -->
        <a-button @click="addInterfaceBtn" type="primary">添加</a-button>
        <a-button @click="delInterfaceBtn(rowSelection.selectedRowKeys)" type="primary" danger
          >删除</a-button
        >
        <a-input v-model:value="appName" placeholder="应用名称" allow-clear size="small"></a-input>

        <a-button @click="searchData" type="default"><SearchOutlined />搜索</a-button>
      </a-space>
    </div>
    <div class="table">
      <a-table
        :columns="columns"
        :dataSource="dataSource"
        bordered
        rowKey="id"
        :pagination="pagination"
        @change="pageChange"
        :row-selection="rowSelection"
      >
        <template #bodyCell="{ column, record }">
          <template v-if="column.dataIndex === 'available'">
            <a-switch
              v-model:checked="record.available"
              :disabled="record.appCode === 'BHMI'"
              @change="handleAvailable($event, record)"
            ></a-switch>
            <!-- <span>adadda</span> -->
          </template>
          <template v-if="column.dataIndex === 'licenseCode'">
            <a-space>
              <span v-if="record.visible">{{ record.licenseCode }}</span>
              <span v-if="!record.visible" style="vertical-align: sub"
                >*********************************</span
              >
              <EyeOutlined v-if="!record.visible" @click="visibleInfo(record)"></EyeOutlined>
              <EyeInvisibleOutlined
                v-if="record.visible"
                @click="visibleInfo(record)"
              ></EyeInvisibleOutlined>
              <a-tooltip placement="right">
                <template #title>一键复制</template>
                <CopyOutlined @click="copyInfo(record)"></CopyOutlined>
              </a-tooltip>
            </a-space>
            <!-- <span>adadda</span> -->
          </template>

          <template v-if="column.dataIndex === 'operation'">
            <a-space>
              <a-button @click="editInterfaceBtn(record)" :disabled="record.appCode === 'BHMI'">编辑</a-button>
              <a-button type="primary" danger @click="delInterfaceBtn([record.id])" :disabled="record.appCode === 'BHMI'">删除</a-button>
              <a-button
                type="primary"
                style="background-color: green"
                :disabled="record.appCode === 'BHMI'"
                @click="openInterModal(record)"
                >接口授权</a-button
              >
            </a-space>
            <!-- <span>adadda</span> -->
          </template>
        </template>
      </a-table>
    </div>
    <a-modal
      v-model:visible="modalState.visible"
      :title="modalState.title === 'add' ? '添加' : '编辑'"
      okText="保存"
      cancelText="关闭"
      :maskClosable="false"
      destroyOnClose
      @ok="modalState.handleOk"
      :afterClose="modalState.afterColse"
    >
      <div class="modelContent">
        <span><span style="color: red">*</span>应用名称:</span>
        <a-input
          v-model:value="appNameInput"
          style="width: 70%; margin-left: 10px"
          @change="validate"
        ></a-input>
      </div>
      <span style="color: red; margin-left: 50px" v-if="showWarn">{{ warnMessage }}</span>
    </a-modal>
    <InterfaceAuthorization ref="interRef"></InterfaceAuthorization>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, h, computed, onMounted } from "vue";
import {
  SearchOutlined,
  EyeOutlined,
  EyeInvisibleOutlined,
  CopyOutlined,
} from "@ant-design/icons-vue";
import { Modal, message } from "ant-design-vue";
import type { TableColumnsType } from "ant-design-vue";
import {
  getInterfaceList,
  addInterface,
  editInterface,
  delInterface,
  appAvail,
} from "../../../../api/project";
import InterfaceAuthorization from "./components/interfaceAuthorization.vue";
export default defineComponent({
  components: {
    SearchOutlined,
    EyeOutlined,
    EyeInvisibleOutlined,
    CopyOutlined,
    InterfaceAuthorization,
  },
  setup() {
    const appName = ref<string>(""); // 搜索名

    const appNameLast = ref<string>(""); // 上一次搜索名

    const columns: TableColumnsType = [
      {
        title: "应用名称",
        dataIndex: "name",
      },
      {
        title: "应用CODE",
        dataIndex: "appCode",
      },
      {
        title: "应用授权码",
        dataIndex: "licenseCode",
        width: 460,
      },
      {
        title: "创建时间",
        dataIndex: "createTimeStr",
      },
      {
        title: "修改时间",
        dataIndex: "updateTimeStr",
      },
      {
        title: "是否启用",
        dataIndex: "available",
      },
      {
        title: "操作",
        dataIndex: "operation",
        width: 400,
      },
    ];

    const dataSource = ref([]);

    const getTableData = async () => {
      const data = {
        size: pagination.value.pageSize,
        current: pagination.value.current,
        name: encodeURIComponent(appNameLast.value),
      };
      // 请求接口
      const res = await getInterfaceList(data);
      if (res.code !== 0) return;
      // 删除最后一条自动向前翻页
      const newTotalPages = Math.ceil(res.data.total / pagination.value.pageSize);
      if (pagination.value.current > newTotalPages && newTotalPages !== 0) {
        pagination.value.current = Math.max(newTotalPages, 1);
        getTableData();
        return;
      }
      // 设置数据
      res.data.records.forEach((item) => {
        item.visible = false;
      });
      dataSource.value = res.data.records;
      tableData.total = res.data.total;
    };
    // 搜索
    const searchData = () => {
      tableData.current = 1;
      appNameLast.value = appName.value;
      getTableData();
    };

    // 选择
    const rowSelection = ref({
      checkStrictly: false,
      selectedRowKeys: [],
      onChange: (selectedRowKeys: string[], selectedRows) => {
        console.log(`selectedRowKeys: ${selectedRowKeys}`, "selectedRows: ", selectedRows);
        rowSelection.value.selectedRowKeys = selectedRowKeys;
      },
      getCheckboxProps: (record) => ({
        disabled: record.id.includes("father") || record.appCode === 'BHMI',
      }),
    });

    // 分页
    const tableData = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      currentPageSize: 0, // 当前页面有几条数据
      tableList: [],
    });
    const pagination = computed(() => ({
      total: tableData.total,
      current: tableData.current, //
      pageSize: 10,
      showTotal: () => `共 ${tableData.total} 条`,
      showLessItems: true,
      defaultPageSize: tableData.pageSize,
      showSizeChanger: false,
    }));
    const pageChange = ({ current, total }) => {
      tableData.current = current;
      getTableData();
      // setCurrentPage(current);
    };

    const handleAvailable = (event, record) => {
      // appAvail(record.)
      const data = {
        id: record.id,
        available: event,
      };
      appAvail(data).then((res) => {
        if (res.code === 0) {
          message.success("更新成功");
          getTableData();
        }
      });
    };

    const visibleInfo = (record) => {
      record.visible = !record.visible;
    };
    const copyInfo = (record) => {
      const textArea = document.createElement("textarea");
      textArea.value = record.licenseCode;
      document.body.appendChild(textArea);
      textArea.focus();
      textArea.select();
      return new Promise<void>((resolve, reject) => {
        // 执行复制命令并移除文本框
        document.execCommand("copy") ? resolve() : reject(new Error("出错了"));
        textArea.remove();
      })
        .then(() => {
          message.success("操作成功");
        })
        .catch((err) => {
          message.error("操作失败");
        });
    };

    // 接口授权
    const interRef = ref(null);
    const openInterModal = (record) => {
      (interRef.value as any).openModal(record);
    };

    // 添加
    const addInterfaceBtn = () => {
      modalState.visible = true;
      modalState.title = "add";
    };

    // 编辑
    const editInterfaceBtn = (record) => {
      appNameInput.value = record.name;
      modalState.id = record.id;
      modalState.visible = true;
      modalState.title = "edit";
    };
    const modalState = reactive({
      visible: false,
      title: "add",
      id: "",
      handleOk: () => {
        if (appNameInput.value === "") {
          warnMessage.value = "应用名称不能为空";
          showWarn.value = true;
          return;
        }
        const regex = /^[a-zA-Z0-9_\u4e00-\u9fa5]{1,20}$/;
        if (!regex.test(appNameInput.value)) {
          warnMessage.value = "应用名称只能包含汉字、字母、数字、下划线，且长度不能超过20";
          showWarn.value = true;
          return;
        }
        if (modalState.title === "add") {
          const data = {
            name: appNameInput.value,
          };
          addInterface(data).then((res) => {
            if (res.code === 0) {
              message.success("添加成功");
              getTableData();
              modalState.visible = false;
            }
          });
        } else {
          const data = {
            id: modalState.id,
            name: appNameInput.value,
          };
          editInterface(data).then((res) => {
            if (res.code === 0) {
              message.success("编辑成功");
              getTableData();
              modalState.visible = false;
            }
          });
        }
      },
      afterColse: () => {
        appNameInput.value = "";
        showWarn.value = false;
      },
    });

    // 添加校验
    const showWarn = ref<boolean>(false);
    const warnMessage = ref<string>("应用名称不能为空");
    const validate = () => {
      if (appNameInput.value === "") {
        warnMessage.value = "应用名称不能为空";
        showWarn.value = true;
        return;
      }
      const regex = /^[a-zA-Z0-9_\u4e00-\u9fa5]{1,20}$/;
      if (!regex.test(appNameInput.value)) {
        warnMessage.value = "应用名称只能包含汉字、字母、数字、下划线，且长度不能超过20";
        showWarn.value = true;
        return;
      }
      showWarn.value = false;
    };

    // 删除
    const delInterfaceBtn = (id: string[]) => {
      if (id.length === 0) {
        message.warning("请选择要删除的应用");
        return;
      }
      Modal.confirm({
        title: "删除",
        content: "确定删除该应用？",
        onOk() {
          delInterface(id).then((res) => {
            if (res.code === 0) {
              message.success("删除成功");
              getTableData();
              rowSelection.value.selectedRowKeys = [];
            }
          });
        },
        onCancel() {
          console.log("Cancel");
        },
      });
    };

    const appNameInput = ref<string>("");

    onMounted(() => {
      getTableData();
    });

    return {
      getTableData,
      searchData,
      appName,
      columns,
      dataSource,
      handleAvailable,
      visibleInfo,
      copyInfo,
      rowSelection,
      pagination,
      pageChange,
      interRef,
      editInterfaceBtn,
      delInterfaceBtn,
      openInterModal,
      h,
      SearchOutlined,
      addInterfaceBtn,
      modalState,
      appNameInput,
      showWarn,
      validate,
      warnMessage,
    };
  },
});
</script>
<style scoped>
.table {
  margin-top: 10px;
}
.modelContent {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}
</style>
