<template>
  <div class="pointBox">
    <a-row :gutter="16">
      <a-col :span="6">
        <a-card class="leftTree" title="报警配置">
          <a-dropdown :trigger="['click']">
            <a-input-search
              v-model:value="searchValue"
              style="margin-bottom: 8px"
              placeholder="输出节点"
              @change="searchTree"
            ></a-input-search>
            <template #overlay>
              <a-menu v-if="searchList.length">
                <a-menu-item v-for="item in searchList" :key="item.key" @click="handleSelect(item)">
                  {{ item.label }}
                </a-menu-item>
              </a-menu>
              <a-empty description="" v-else></a-empty>
            </template>
          </a-dropdown>
          <a-tree
            style="text-align: left; padding-bottom: 50px"
            :tree-data="treeData"
            v-model:selectedKeys="selectedkeys"
            :autoExpandParent="autoExpandParent"
            v-model:expanded-keys="expandedKeys"
            @expand="onExpand"
            block-node
            @select="selectTree"
            class="projectTree"
            :fieldNames="{ key: 'id', title: 'label' }"
            v-if="treeData.length"
          >
            <template #title="{ data }">
              <span class="custom-tree-node">
                <span>
                  <IconFont
                    :type="IconList[data.type]"
                    v-if="data.type !== 'channel'"
                    class="iconClass"
                  ></IconFont>
                  <IconFont
                    type="icon-a-Property1qiuxing"
                    v-if="data.type === 'channel' && data.deviceType.includes('PTZ')"
                    class="iconClass"
                  ></IconFont>
                  <IconFont
                    type="icon-a-Property1qiangji"
                    v-if="data.type === 'channel' && !data.deviceType.includes('PTZ')"
                    class="iconClass"
                  ></IconFont>
                  <a-tooltip placement="topLeft">
                    <template #title>
                      {{ !data.des ? data.label : data.des }}
                    </template>

                    <span
                      :ref="(el) => (nodeRefs[data.id] = el)"
                      :class="{ searchBack: searchKey === data.id }"
                      >{{ data.label }}
                    </span>
                  </a-tooltip>
                  <span
                    v-if="
                      (data.type === 'directory' || data.type === 'business-root') &&
                      getTotalChannelCount(data) !== 0
                    "
                    style="margin-left: 5px"
                  >
                    (<span class="area">{{ getOnlineChannelCount(data) }}</span> /
                    {{ getTotalChannelCount(data) }})
                  </span>
                </span>
              </span>
            </template>
          </a-tree>
        </a-card>
      </a-col>
      <a-col :span="18">
        <a-card class="rightTable">
          <div class="submitBtn">
            <a-button @click="handleSubmit" :disabled="!algorithmInstanceList.length" type="primary"
              >提交
            </a-button>
          </div>

          <a-Empty description="暂无数据" v-if="algorithmInstanceList.length === 0"></a-Empty>
          <div class="alCardTable" v-else>
            <a-card
              v-for="(item, index) in algorithmInstanceList"
              :title="`${index + 1}、${item.algorithmInstanceName}`"
              style="width: 45%; margin-bottom: 5px; margin-right: 5px"
            >
              <a-tabs v-model:activeKey="item.activeTabKey" size="small" type="card">
                <a-tab-pane v-for="t in item.alarmConfigs" :key="t.outputParamId">
                  <template #tab>
                    <a-tooltip>
                      <template #title>{{ t.outputName }}</template>
                      <span>
                        {{
                          t.outputName.length > 15
                            ? t.outputName.slice(0, 15) + "..."
                            : t.outputName
                        }}
                      </span>
                    </a-tooltip>
                  </template>
                  <div class="outputBoxClass">
                    <a-form-item label="算法输出名">
                      <a-input
                        v-model:value="t.outputName"
                        size="small"
                        readonly
                        style="width: 90%"
                      ></a-input>
                    </a-form-item>
                    <a-form-item label="输出类型">
                      <a-input v-model:value="t.outputDataType" readonly></a-input>
                    </a-form-item>
                  </div>

                  <div v-if="t.outputDataType === 'INTEGER' || t.outputDataType === 'FLOAT'">
                    <!-- -----------------------------------------------报警类型------------------------------------------------- -->
                    <a-form-item label="报警类型">
                      <a-select v-model:value="t.alarmType" size="small">
                        <a-select-option value="OFF_LIMIT_ALARM"> 越限报警 </a-select-option>
                        <a-select-option value="DEVIATION_ALARM"> 偏差报警 </a-select-option>
                        <a-select-option value="DEEP_ALARM"> 深度报警 </a-select-option>
                      </a-select>
                    </a-form-item>

                    <!-- -----------------------------------------------越界报警------------------------------------------------- -->
                    <div v-if="t.alarmType === 'OFF_LIMIT_ALARM'">
                      <table class="table" aria-describedby="报警规则配置">
                        <thead>
                          <tr>
                            <th scope="col"></th>
                            <th scope="col">界限值</th>
                            <th scope="col">报警文本</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(tr, i) in t.ruleLimit.ruleInfo" :key="i">
                            <td
                              scope="row"
                              style="border: 1px solid rgb(190, 190, 190); width: 100px"
                            >
                              <a-checkbox v-model:checked="tr.enable">
                                {{ grade[tr.type] }}
                              </a-checkbox>
                            </td>
                            <td style="border: 1px solid rgb(190, 190, 190)">
                              <a-input-number
                                v-model:value="tr.val"
                                size="small"
                                style="width: 100%"
                                :placeholder="tr.placeHolder"
                                :min="-999999"
                                :max="999999"
                                :controls="false"
                                :precision="5"
                                :formatter="
                                  function (value, info) {
                                    if (info.userTyping) {
                                      return info.input;
                                    }
                                    return value;
                                  }
                                "
                                @change="clearErrMsg(t.algorithmInstanceId)"
                              ></a-input-number>
                            </td>
                            <td style="border: 1px solid rgb(190, 190, 190)">
                              <a-input
                                v-model:value="tr.desc"
                                size="small"
                                :maxlength="64"
                              ></a-input>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>

                    <!-- -----------------------------------------------偏差报警------------------------------------------------- -->
                    <div v-if="t.alarmType === 'DEVIATION_ALARM'">
                      <a-form-item label="绑定点名">
                        <a-input
                          v-model:value="t.ruleDeviation.pointName"
                          @change="clearErrMsg(t.algorithmInstanceId)"
                          size="small"
                        ></a-input>
                      </a-form-item>
                      <table class="table" aria-describedby="报警规则配置">
                        <thead>
                          <tr>
                            <th scope="col"></th>
                            <th scope="col">界限值</th>
                            <th scope="col">报警文本</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(tr, i) in t.ruleDeviation.ruleInfo" :key="i">
                            <td
                              scope="row"
                              style="border: 1px solid rgb(190, 190, 190); width: 100px"
                            >
                              <a-checkbox v-model:checked="tr.enable">
                                {{ deviationGrade[tr.type] }}
                              </a-checkbox>
                            </td>
                            <td style="border: 1px solid rgb(190, 190, 190); width: 250px">
                              <a-input-number
                                v-model:value="tr.val"
                                size="small"
                                style="width: 100%"
                                :placeholder="tr.placeHolder"
                                :min="-999999"
                                :max="999999"
                                :controls="false"
                                :precision="5"
                                :formatter="
                                  function (value, info) {
                                    if (info.userTyping) {
                                      return info.input;
                                    }
                                    return value;
                                  }
                                "
                                @change="clearErrMsg(t.algorithmInstanceId)"
                              ></a-input-number>
                            </td>
                            <td style="border: 1px solid rgb(190, 190, 190); width: 200px">
                              <a-input
                                v-model:value="tr.desc"
                                size="small"
                                :maxlength="64"
                              ></a-input>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>

                    <!-- -----------------------------------------------深度报警------------------------------------------------- -->
                    <div v-if="t.alarmType === 'DEEP_ALARM'">
                      <a-form-item label="绑定点名">
                        <a-input
                          v-model:value="t.ruleDeep.pointName"
                          size="small"
                          @change="clearErrMsg(t.algorithmInstanceId)"
                        ></a-input>
                      </a-form-item>
                      <table class="table" aria-describedby="报警规则配置">
                        <thead>
                          <tr>
                            <th scope="col"></th>
                            <th scope="col">界限值</th>
                            <th scope="col">报警文本</th>
                          </tr>
                        </thead>
                        <tbody>
                          <tr v-for="(tr, i) in t.ruleDeep.ruleInfo" :key="i">
                            <td
                              scope="row"
                              style="border: 1px solid rgb(190, 190, 190); width: 100px"
                            >
                              <a-checkbox v-model:checked="tr.enable">
                                {{ deepGrade[tr.type] }}
                              </a-checkbox>
                            </td>
                            <td style="border: 1px solid rgb(190, 190, 190); width: 250px">
                              <a-input-number
                                v-model:value="tr.val"
                                size="small"
                                style="width: 100%"
                                :placeholder="tr.placeHolder"
                                :min="-999999"
                                :max="999999"
                                :controls="false"
                                :precision="5"
                                :formatter="
                                  function (value, info) {
                                    if (info.userTyping) {
                                      return info.input;
                                    }
                                    return value;
                                  }
                                "
                                @change="clearErrMsg(t.algorithmInstanceId)"
                              ></a-input-number>
                            </td>
                            <td style="border: 1px solid rgb(190, 190, 190); width: 200px">
                              <a-input
                                v-model:value="tr.desc"
                                size="small"
                                :maxlength="64"
                              ></a-input>
                            </td>
                          </tr>
                        </tbody>
                      </table>
                    </div>
                  </div>
                  <div v-if="t.outputDataType === 'STRING'">
                    <table class="table" aria-describedby="报警规则配置">
                      <thead>
                        <tr>
                          <th scope="col"></th>
                          <th scope="col">报警匹配值</th>
                          <th scope="col">报警文本</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(tr, i) in t.ruleStr.ruleInfo" :key="i">
                          <td
                            scope="row"
                            style="border: 1px solid rgb(190, 190, 190); width: 100px"
                          >
                            <a-checkbox v-model:checked="tr.enable">
                              {{ stringGrade[tr.type] }}
                            </a-checkbox>
                          </td>
                          <td style="border: 1px solid rgb(190, 190, 190)">
                            <a-input
                              v-model:value="tr.val"
                              size="small"
                              style="width: 100%"
                              maxLength="64"
                              :placeholder="tr.placeHolder"
                              @change="clearErrMsg(t.algorithmInstanceId)"
                              :disabled="tr.type === 'IS_EMPTY_STR' || tr.type === 'NOT_EMPTY_STR'"
                            ></a-input>
                          </td>
                          <td style="border: 1px solid rgb(190, 190, 190)">
                            <a-input v-model:value="tr.desc" size="small" :maxlength="64"></a-input>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                  <div v-if="t.outputDataType === 'BOOLEAN'">
                    <table class="table" aria-describedby="报警规则配置">
                      <thead>
                        <tr>
                          <th scope="col"></th>
                          <th scope="col">报警文本</th>
                        </tr>
                      </thead>
                      <tbody>
                        <tr v-for="(tr, i) in t.ruleBool.ruleInfo" :key="i">
                          <td
                            scope="row"
                            style="border: 1px solid rgb(190, 190, 190); width: 100px"
                          >
                            <a-checkbox
                              v-model:checked="tr.enable"
                              @change="booleanChange(index, t.id, i)"
                            >
                              {{ booleanGrade[tr.type] }}
                            </a-checkbox>
                          </td>
                          <td style="border: 1px solid rgb(190, 190, 190)">
                            <a-input
                              v-model:value="tr.desc"
                              size="small"
                              :maxlength="64"
                              :disabled="tr.type === 'IS_EMPTY_BOOL'"
                            ></a-input>
                          </td>
                        </tr>
                      </tbody>
                    </table>
                  </div>
                </a-tab-pane>
                <template #renderTabBar="{ DefaultTabBar, ...props }">
                  <component :is="DefaultTabBar" v-bind="props" :style="{ height: '25px' }" />
                </template>
              </a-tabs>
              <div class="errMsg">
                <div v-for="error in ErrMsgList">
                  <span
                    style="color: red; margin-right: 5px"
                    v-if="error.itemId === item.alarmConfigs[0].algorithmInstanceId"
                    >{{ error.messageStr }}</span
                  >
                </div>
                <!-- <a-alert v-if="item.errMsg" type="error"  :message="item.errMsg" /> -->
              </div>
            </a-card>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>
<script lang="ts">
import { computed, defineComponent, onMounted, onUnmounted, reactive, ref, nextTick } from "vue";
import { getProjectTree } from "@/api/design";
import { getAllAlarm, saveAllAlarm } from "@/api/project";
import { message, TreeProps } from "ant-design-vue";
import { getOnlineChannelCount, getTotalChannelCount, isEmpty } from "@/common/utils";
import IconFont from "@/components/c-iconfont";
import { IconList } from "@/components/datatypeComponent";

export default defineComponent({
  components: {
    IconFont,
  },
  setup() {
    // -------------------------------------------------------获取树结构--------------------------------------------------------

    const treeData = ref<TreeProps["treeData"]>([]); // 树结构数据

    const expandNodeList = ref([]); // 展开的树节点

    // 过滤树节点
    const filterateArray = ["ops", "ens", "control-flow"];
    const recursionTree = (data: any[]) => {
      if (data.length === 0) {
        return;
      }
      // searchConfig.targetId = data[0].id;
      const arr = data;
      arr[0].selectable = false;
      arr[0].children = arr[0].children.filter((item) => !filterateArray.includes(item.type));
      arr[0].children.forEach((item) => {
        if (item.type === "channel") {
          item.selectable = false;
        }
      });
      return arr;
    };

    // 获取树节点
    const getTreeData = () => {
      return new Promise((resolve, reject) => {
        getProjectTree().then((res) => {
          if (res.code === 0) {
            if (res.data.length !== 0) {
              // selectedkeys.value = [res.data.tree[0].id];
              treeData.value = recursionTree(res.data.tree);

              if (treeData.value[0].children.length !== 0) {
                const findPreset = treeData.value[0].children.find(
                  (item) => item.children.length !== 0
                );
                selectedkeys.value = [findFirstPresetNode(findPreset)];
                resolve(selectedkeys.value);
              }

              generateList(treeData.value);
              //展开所有
              autoExpandParent.value = true;
              expandedKeys.value = expandNodeList.value.map((item) => item.key);
            }
          }
        });
      });
    };

    // --------------------------------------------------------节点选择逻辑--------------------------------------------------
    const selectedkeys = ref<string[]>([""]); // 树结构选择的节点

    const tempSelectedkeys = ref<string[]>([""]); // 树结构选择暂存
    const selectTree = (selectedKeys, { selected, selectedNodes }) => {
      if (!selected) {
        selectedkeys.value = tempSelectedkeys.value;
        return;
      }
      if (selectedNodes[0].type !== "preset") {
        message.warn("请选择预置点节点");
        selectedkeys.value = tempSelectedkeys.value;
        return;
      }
      tempSelectedkeys.value = selectedKeys;
      ErrMsgList.value = []; // 清空错误提示列表
      getAlarm(selectedKeys[0]);
    };

    // 获取第一个预置点节点

    function findFirstPresetNode(tree) {
      // 处理数组型节点
      if (Array.isArray(tree)) {
        for (const node of tree) {
          const result = findFirstPresetNode(node);
          if (result) return result;
        }
        return null;
      }

      // 检查当前节点
      if (tree.type === "preset") {
        return tree.id;
      }

      // 递归检查子节点
      if (tree.children && tree.children.length > 0) {
        for (const child of tree.children) {
          const result = findFirstPresetNode(child);
          if (result) return result;
        }
      }

      return null;
    }

    // -----------------------------------------------------------------------获取预置点算法实例----------------------------------------------

    const algorithmInstanceList = ref<any[]>([]);
    const getAlarm = (presetId: string) => {
      getAllAlarm({ presetId: presetId }).then((res) => {
        if (res.code === 0) {
          algorithmInstanceList.value = res.data;
          algorithmInstanceList.value.forEach((item) => {
            item.activeTabKey = item.alarmConfigs[0].outputParamId;
          });
        }
      });
    };
    const grade = {
      HH: "高高",
      H: "高",
      L: "低",
      LL: "低低",
    };

    const deviationGrade = {
      BIG_DEVIATION: "大偏差",
      SMALL_DEVIATION: "小偏差",
    };

    const deepGrade = {
      BOTH_GT: "同时高于",
      BOTH_LT: "同时低于",
    };

    const stringGrade = {
      IS_EMPTY_STR: "值为空",
      NOT_EMPTY_STR: "值不为空",
      EQUALS: "等于",
      NOT_EQUALS: "不等于",
      REGEX_MATCHES: "正则匹配",
    };

    const booleanGrade = {
      IS_EMPTY_BOOL: "值为空",
      TRUE: "是(true)",
      FALSE: "否(false)",
    };
    // 提交报警配置
    const handleSubmit = () => {
      // 提取list中的val
      const listValMap = algorithmInstanceList.value.map((item, index) => {
        const temp = item.alarmConfigs.map((element, i) => {
          switch (element.outputDataType) {
            case "STRING":
              return {
                id: element.algorithmInstanceId,
                alarmVal: element.ruleStr.ruleInfo
                  .filter((val) => {
                    return val.enable;
                  })
                  .map((val) => {
                    return val.val;
                  }),
                name: element.outputName,
                alarmType: element.alarmType,
                pointName: element.ruleStr.pointName,
                outputDataType:element.outputDataType

              };
            case "BOOLEAN":
              return {
                id: element.algorithmInstanceId,
                alarmVal: element.ruleBool.ruleInfo
                  .filter((val) => {
                    return val.enable;
                  })
                  .map((val) => {
                    return val.val;
                  }),
                name: element.outputName,
                alarmType: element.alarmType,
                pointName: element.ruleBool.pointName,
                outputDataType:element.outputDataType
              };

            case "FLOAT":
              switch (element.alarmType) {
                case "OFF_LIMIT_ALARM":
                  return {
                    id: element.algorithmInstanceId,
                    alarmVal: element.ruleLimit.ruleInfo
                      .filter((val) => {
                        return val.enable;
                      })
                      .map((val) => {
                        return val.val;
                      }),
                    name: element.outputName,
                    alarmType: "OFF_LIMIT_ALARM",
                    pointName: element.ruleLimit.pointName,
                    outputDataType:element.outputDataType
                  };
                case "DEVIATION_ALARM":
                  return {
                    id: element.algorithmInstanceId,
                    alarmVal: element.ruleDeviation.ruleInfo
                      .filter((val) => {
                        return val.enable;
                      })
                      .map((val) => {
                        return val.val;
                      }),
                    name: element.outputName,
                    alarmType: "DEVIATION_ALARM",
                    pointName: element.ruleDeviation.pointName,
                    outputDataType:element.outputDataType
                  };
                case "DEEP_ALARM":
                  return {
                    id: element.algorithmInstanceId,
                    alarmVal: element.ruleDeep.ruleInfo
                      .filter((val) => {
                        return val.enable;
                      })
                      .map((val) => {
                        return val.val;
                      }),
                    name: element.outputName,
                    alarmType: "DEEP_ALARM",
                    pointName: element.ruleDeep.pointName,
                    outputDataType:element.outputDataType
                  };

                default:
                  break;
              }
            case "INTEGER":
              switch (element.alarmType) {
                case "OFF_LIMIT_ALARM":
                  return {
                    id: element.algorithmInstanceId,
                    alarmVal: element.ruleLimit.ruleInfo
                      .filter((val) => {
                        return val.enable;
                      })
                      .map((val) => {
                        return val.val;
                      }),
                    name: element.outputName,
                    alarmType: "OFF_LIMIT_ALARM",
                    pointName: element.ruleLimit.pointName,
                    outputDataType:element.outputDataType
                  };
                case "DEVIATION_ALARM":
                  return {
                    id: element.algorithmInstanceId,
                    alarmVal: element.ruleDeviation.ruleInfo
                      .filter((val) => {
                        return val.enable;
                      })
                      .map((val) => {
                        return val.val;
                      }),
                    name: element.outputName,
                    alarmType: "DEVIATION_ALARM",
                    pointName: element.ruleDeviation.pointName,
                    outputDataType:element.outputDataType
                  };
                case "DEEP_ALARM":
                  return {
                    id: element.algorithmInstanceId,
                    alarmVal: element.ruleDeep.ruleInfo
                      .filter((val) => {
                        return val.enable;
                      })
                      .map((val) => {
                        return val.val;
                      }),
                    name: element.outputName,
                    alarmType: "DEEP_ALARM",
                    pointName: element.ruleDeep.pointName,
                    outputDataType:element.outputDataType
                  };

                default:
                  break;
              }
          }
        });
        return temp;
      });
      const checkedResult = verBoundaryValue(listValMap);
      console.log(checkedResult, "checkedResult");
      if (checkedResult.isValidate) {
        ErrMsgList.value = checkedResult.ErrList;
        return;
      }
      saveAllAlarm(algorithmInstanceList.value).then((res) => {
        if (res.code === 0) {
          message.success("操作成功");
        }
      });
    };

    // -----------------------------------------------------------------校验边界值-------------------------------------------------------

    // 判断是否为顺序或者为倒序
    function isArraySorted(arr) {
      // 判重
      const set = new Set(arr);
      if (set.size !== arr.length) {
        return false;
      }
      // 判断顺序还是逆序
      // const sortedArr = [...arr].sort((a, b) => a - b);
      const reversedArr = [...arr].sort((a, b) => b - a);

      return JSON.stringify(arr) === JSON.stringify(reversedArr);
    }

    // 校验边界值
    const ErrMsgList = ref<any[]>([]); // 错误提示列表
    const verBoundaryValue = (list: any[]) => {
      console.log(list, "list");
      ErrMsgList.value = [];
      let isValidate = false;
      let ErrList: any = [];
      list.forEach((item) => {
        item.forEach((ele) => {
          if (ele.outputDataType === "FLOAT" || ele.outputDataType === "INTEGER") {
            if (ele.alarmType === "DEVIATION_ALARM" || ele.alarmType === "DEEP_ALARM") {
              let show = false;
              const regPointName =
                /^[a-zA-Z0-9_]{1,50}(\.[a-zA-Z0-9_]{1,50})?(\.[a-zA-Z0-9_]{1,10})?$/;
              if (isEmpty(ele.pointName)) {
                show = true;
              }

              const parts = isEmpty(ele.pointName) ? [] : ele.pointName.split(".");

              if (parts.length !== 3) {
                show = true;
              }
              if (!regPointName.test(ele.pointName) || ele.pointName === null) {
                show = true;
              }
              if (show) {
                ErrList.push({
                  itemId: ele.id,
                  messageStr:
                    ele.name +
                    "域点项不能为空，每个部分只包含字母、数字和下划线，且长度分别为50、50、10个字符以内，格式为abc.abc.abc",
                });
                isValidate = true;
              }
            }
            let alarmShow = false;
            if (ele.alarmVal.includes(null)) {
              // ErrList.push({ itemId: ele.id, messageStr: ele.name + "中界限值不能为空" });
              // isValidate = true;
              alarmShow = true;
            }
            if (!isArraySorted(ele.alarmVal)) {
              alarmShow = true;
            }
            if (ele.alarmType === "OFF_LIMIT_ALARM") {
              if (alarmShow) {
                ErrList.push({
                  itemId: ele.id,
                  messageStr: ele.name + "中界限值必须降序且界限值不能为空",
                });
                isValidate = true;
              }
            }
            if (ele.alarmType === "DEVIATION_ALARM") {
              if (alarmShow) {
                ErrList.push({
                  itemId: ele.id,
                  messageStr: ele.name + "中小偏差界限值必须小于大偏差界限值且界限值不能为空",
                });
                isValidate = true;
              }
            }
            if (ele.alarmType === "DEEP_ALARM") {
              if (alarmShow) {
                ErrList.push({
                  itemId: ele.id,
                  messageStr: ele.name + "中同时低于界限值必须小于同时高于界限值且界限值不能为空",
                });
                isValidate = true;
              }
            }
          }
        });
      });

      return { isValidate, ErrList };
    };

    // 清空错误提示
    const clearErrMsg = (id) => {
      console.log(id, ErrMsgList.value);
      ErrMsgList.value = ErrMsgList.value.filter((item) => item.itemId !== id);
    };

    // --------------------------------------------------------------树节点搜索-------------------------------------------

    const generateList = (data) => {
      for (let i = 0; i < data.length; i++) {
        const node = data[i];
        const key = node.id;
        expandNodeList.value.push({ key, title: node.label });
        if (node.children) {
          generateList(node.children);
        }
      }
    };

    const getParentKey = (key: string | number, tree: any): string | number | undefined => {
      let parentKey;
      for (let i = 0; i < tree.length; i++) {
        const node = tree[i];
        if (node.children) {
          if (node.children.some((item) => item.id === key)) {
            parentKey = node.id;
          } else if (getParentKey(key, node.children)) {
            parentKey = getParentKey(key, node.children);
          }
        }
      }
      return parentKey;
    };
    const expandedKeys = ref<(string | number)[]>([]);
    const autoExpandParent = ref<boolean>(true);

    // -------------------------------------------------------------------------树节点搜索---------------------------------------------------------
    const searchList = ref<any[]>([]); // 搜索结果列表

    const searchValue = ref<string>(""); // 当前搜索值

    const nodeRefs = ref({}); // 节点引用

    const searchKey = ref(""); // 搜索关键字

    // 找到所有父节点的ID
    function findParentIdsFromTree(treeArray, targetId) {
      const path = [];

      function dfs(node, ancestors) {
        if (node.id === targetId) {
          path.push(...ancestors);
          return true;
        }

        if (node.children && node.children.length > 0) {
          for (const child of node.children) {
            if (dfs(child, [...ancestors, node.id])) {
              return true;
            }
          }
        }

        return false;
      }

      for (const root of treeArray) {
        if (dfs(root, [])) {
          break;
        }
      }

      return path;
    }

    function searchOutputParams(tree, keyword) {
      const results = [];

      if (keyword === "") {
        return [];
      }
      function traverse(node, parent, grandparent) {
        // 匹配output-param节点且包含关键字
        if (node.label.toUpperCase().includes(keyword.toUpperCase())) {
          // 构建层级标签
          const labels = [];
          if (grandparent) labels.push(grandparent.label);
          if (parent) labels.push(parent.label);
          labels.push(node.label);

          // 生成格式化结果
          results.push({
            key: node.id,
            label: labels.join("-"),
          });
        }

        // 递归遍历子节点，维护父级关系
        if (node.children) {
          node.children.forEach((child) => {
            // 当前节点成为父级，原父级成为祖父级
            traverse(child, node, parent);
          });
        }
      }

      // 从根节点开始遍历
      tree.forEach((root) => traverse(root, null, null));

      return results;
    }

    // 搜索树
    const searchTree = () => {
      // debugger;
      searchList.value = searchOutputParams(treeData.value, searchValue.value);
      searchKey.value = "";
    };

    // 滚动到选中节点
    const scrollToNode = () => {
      if (searchKey.value && nodeRefs.value[searchKey.value]) {
        const node = nodeRefs.value[searchKey.value];
        expandedKeys.value.push();
        node.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    };
    // 选中搜索结果
    const handleSelect = async (item) => {
      searchKey.value = item.key;
      await nextTick();
      console.log(treeData.value, item.key);
      console.log(findParentIdsFromTree(treeData.value, item.key));
      findParentIdsFromTree(treeData.value, item.key).forEach((item) => {
        expandedKeys.value.push(item);
      });
      scrollToNode();
    };

    const onExpand = (keys: string[] | number[]) => {
      expandedKeys.value = keys;
      autoExpandParent.value = false;
    };

    let searchTimer = null;

    const booleanChange = (index, id, i) => {
      // algorithmInstanceList.value[index].alarmConfigs.forEach((item, num) => {
      //   if (item.id === id) {
      //     if (item.ruleBool.ruleInfo[i].enable) {
      //       console.log(item.ruleBool.ruleInfo[i].enable, "item.ruleBool.ruleInfo[i].enable");
      //       item.ruleBool.ruleInfo[i === 0 ? 1 : 0].enable = !item.ruleBool.ruleInfo[i].enable;
      //     }
      //   }
      // });
    };

    onMounted(() => {
      getTreeData().then((id) => {
        expandedKeys.value = expandedKeys.value.concat([id as string]);
        autoExpandParent.value = true;
        if (id[0]) {
          getAlarm(id[0] as string);
        }
      });

      // clearTimeout(searchTimer);
    });
    onUnmounted(() => {
      clearTimeout(searchTimer);
    });
    return {
      treeData,
      expandNodeList,
      selectedkeys,
      selectTree,
      IconList,
      grade,
      deviationGrade,
      deepGrade,
      stringGrade,
      booleanGrade,
      algorithmInstanceList,
      handleSubmit,
      clearErrMsg,
      // rules,
      expandedKeys,
      autoExpandParent,
      searchList,
      searchValue,
      nodeRefs,
      searchKey,
      searchTree,
      handleSelect,
      onExpand,
      ErrMsgList,
      getOnlineChannelCount,
      getTotalChannelCount,
      booleanChange,
    };
  },
});
</script>
<style scoped lang="less">
.pointBox {
  width: 98%;
  margin-left: 1%;
}

.projectTree {
  font-size: 14px;
}

.leftTree {
  width: 100%;
  height: 733px;
  overflow-y: auto;
}

.leftTree::-webkit-scrollbar {
  width: 5px;
}

.leftTree::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.2);
}

.leftTree::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}

.rightTable {
  width: 100%;
  height: 733px;
}

.table {
  margin-top: 10px;
}

.alCardTable {
  display: flex;
  flex-wrap: wrap;
  flex-direction: row;
  max-height: 650px;
  overflow-y: auto;
  /* justify-content: space-between; */
}
:deep(.ant-form-item) {
  margin-bottom: 2px;
}

:deep(.ant-form-item-label > label) {
  pointer-events: none;
}
.area {
  color: #1890ff;
}
.submitBtn {
  display: flex;
  justify-content: flex-end;
  margin-bottom: 10px;
}

.outputBoxClass {
  display: flex;
}

:deep(.ant-tree-indent-unit) {
  width: 10px !important;
}

.iconClass {
  margin-right: 6px;
  color: var(--iconColor);
}
.searchBack {
  background-color: #fffb8f;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}
</style>
