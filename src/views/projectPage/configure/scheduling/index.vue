<template>
  <div class="box">
    <a-row :gutter="16">
      <a-col :span="4">
        <a-card class="leftTree" title="巡检任务列表" :bodyStyle="{ padding: '0px' }">
          <template #extra>
            <a-button type="primary" size="small" @click="addModal">新增</a-button>
          </template>
          <a-dropdown :trigger="['click']">
            <a-input-search
              v-model:value="searchValue"
              style="margin-bottom: 8px"
              placeholder="输出节点"
              @change="searchTree"
            ></a-input-search>
            <template #overlay>
              <a-menu v-if="searchList.length">
                <a-menu-item v-for="item in searchList" :key="item.key" @click="handleSelect(item)">
                  {{ item.label }}
                </a-menu-item>
              </a-menu>
              <a-empty description="" v-else></a-empty>
            </template>
          </a-dropdown>
          <a-tree
            :tree-data="taskTree"
            v-model:selectedKeys="selectedTaskKeys"
            block-node
            defaultExpandAll
            @select="selectTree"
            v-model:expanded-keys="expandedKeys"
            :fieldNames="{ key: 'id', title: 'label' }"
            v-if="taskTree.length"
          >
            <template #title="{ data }">
              <span class="custom-tree-node">
                <span class="custom-tree-node-title">
                  <IconFont type="icon-file-directory" v-if="data.type === 'DIRECTORY'" />
                  <IconFont type="icon-task" v-else />
                  <span
                    :ref="(el) => (nodeRefs[data.id] = el)"
                    :class="{ searchBack: searchKey === data.id }"
                    >{{ data.label }}</span
                  >
                </span>
                <div class="inline-btn">
                  <a-dropdown :trigger="['click']">
                    <EllipsisOutlined />
                    <template #overlay>
                      <a-menu>
                        <a-menu-item key="0">
                          <a style="color: red" @click="handleDeleteTask(data)">删除</a>
                        </a-menu-item>
                        <a-menu-item key="1">
                          <a @click="editTaskNameModal(data)">编辑</a>
                        </a-menu-item>
                      </a-menu>
                    </template>
                  </a-dropdown>
                </div>
              </span>
            </template>
          </a-tree>
          <a-empty description="暂无数据" v-if="!taskTree.length"></a-empty>
        </a-card>
      </a-col>
      <a-modal
        v-model:visible="addTaskModal"
        title="添加节点"
        :maskClosable="false"
        okText="确定"
        cancelText="取消"
        @ok="handleAddTask"
        :afterClose="afterClose"
      >
        <a-form
          ref="formRef"
          :model="formState"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 16 }"
          :rules="rules"
        >
          <a-form-item name="label" label="名称">
            <a-input v-model:value="formState.label" />
          </a-form-item>
          <a-form-item name="type" label="类型">
            <a-select v-model:value="formState.type" :disabled="isEdit">
              <a-select-option value="DIRECTORY">目录</a-select-option>
              <a-select-option value="TASK">任务</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item name="parentId" label="所属父级">
            <a-tree-select
              v-model:value="formState.parentId"
              :tree-data="formTreeSelect"
              :dropdown-style="{ maxHeight: '400px', overflow: 'auto' }"
              tree-default-expand-all
              tree-node-filter-prop="label"
              :fieldNames="{ label: 'label', value: 'id' }"
              :treeIcon="true"
              :allowClear="true"
            >
            </a-tree-select>
          </a-form-item>
        </a-form>
      </a-modal>
      <a-col :span="20">
        <a-card
          class="rightTable"
          :title="taskTitle"
          :bodyStyle="{ 'padding-top': '10px', 'padding-left': '50px' }"
        >
          <template #extra>
            <a-button type="primary" @click="submitTask" :disabled="taskTree.length === 0"
              >提交
            </a-button>
          </template>
          <div style="width: 100%; height: 100%" v-if="!showTaskInfo">
            <a-empty description="暂无数据"></a-empty>
          </div>
          <div v-else>
            <a-form>
              <!-- ---------------------------------调度开关--------------------------------------- -->
              <a-form-item label="调度开关">
                <a-switch v-model:checked="thisTaskinfo.scheduleEnable" size="small"
              /></a-form-item>
              <!-- ----------------------------------巡检点---------------------------------------- -->
              <a-form-item>
                <template #label>
                  巡检点
                  <a-tooltip>
                    <template #title>
                      <span>可拖动右侧'已配置'项来调整调度顺序</span>
                    </template>
                    <question-circle-outlined />
                  </a-tooltip>
                </template>
                <trans ref="TransRef"></trans>
              </a-form-item>
              <!-- ----------------------------------调度间隔--------------------------------------- -->
              <a-form-item label="调度间隔">
                <a-input-number
                  v-model:value="thisTaskinfo.interval"
                  style="width: 200px"
                  size="small"
                  :controls="false"
                  :precision="0"
                  :min="0"
                  :max="100"
                  placeholder="调度间隔"
                >
                  <template #addonAfter>
                    <a-select
                      v-model:value="thisTaskinfo.intervalUnit"
                      style="width: 60px"
                      size="small"
                    >
                      <a-select-option value="SECONDS">秒</a-select-option>
                      <a-select-option value="MINUTES">分</a-select-option>
                      <a-select-option value="HOURS">时</a-select-option>
                    </a-select>
                  </template>
                </a-input-number>
              </a-form-item>
              <a-form-item label="调度机制">
                <a-radio-group name="radioGroup" v-model:value="thisTaskinfo.triggerType">
                  <a-radio value="TIME_TRIGGER">时间触发</a-radio>
                  <!--                  <a-radio value="CONDITION_TRIGGER">条件触发</a-radio>-->
                </a-radio-group>
              </a-form-item>
            </a-form>
            <scheduling
              ref="schedulingTime"
              style="margin-left: 30px"
              v-show="thisTaskinfo.triggerType == 'TIME_TRIGGER'"
            ></scheduling>

            <div class="point-config" v-show="thisTaskinfo.triggerType == 'CONDITION_TRIGGER'">
              <a-space :size="22">
                <div>变量类型</div>
                <a-select
                  ref="select"
                  v-model:value="thisTaskinfo.variableType"
                  style="width: 120px"
                  size="small"
                >
                  <!-- <a-select-option value="INTERNAL">内部变量值</a-select-option> -->
                  <a-select-option value="DCS">DCS点项值</a-select-option>
                </a-select>
              </a-space>
            </div>
            <div
              class="point-config"
              v-show="
                thisTaskinfo.triggerType == 'CONDITION_TRIGGER' &&
                thisTaskinfo.variableType == 'DCS'
              "
            >
              <a-space>
                <div style="margin-right: 28px">触发点</div>
                <a-input
                  v-model:value="thisTaskinfo.namespace"
                  placeholder="点域"
                  size="small"
                ></a-input>
                <a-input v-model:value="thisTaskinfo.tag" placeholder="点名" size="small"></a-input>
                <a-input
                  v-model:value="thisTaskinfo.item"
                  placeholder="点项"
                  size="small"
                ></a-input>
              </a-space>
            </div>
            <div
              class="point-config"
              v-show="
                thisTaskinfo.triggerType == 'CONDITION_TRIGGER' &&
                thisTaskinfo.variableType == 'INTERNAL'
              "
            >
              <a-space :size="22">
                <div>变量名</div>
                <a-input
                  v-model:value="thisTaskinfo.variableName"
                  style="width: 120px; margin-left: 14px"
                  size="small"
                ></a-input>
              </a-space>
            </div>
            <div class="point-config" v-show="thisTaskinfo.triggerType == 'CONDITION_TRIGGER'">
              <a-space :size="22">
                <div>点值</div>
                <a-select
                  ref="select"
                  v-model:value="thisTaskinfo.triggerValue"
                  style="width: 120px; margin-left: 28px"
                  size="small"
                >
                  <a-select-option value="true">true(真,1)</a-select-option>
                  <a-select-option value="false">false(假,0)</a-select-option>
                </a-select>
              </a-space>
            </div>
          </div>
        </a-card>
      </a-col>
    </a-row>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, ref, nextTick } from "vue";
import type { FormInstance } from "ant-design-vue";
import { message, Modal } from "ant-design-vue";
import {
  addTask,
  deleteTask,
  editTask,
  editTaskName,
  getTaskTreeInterface,
  getThisTaskPoint,
} from "@/api/project";
import {
  CheckOutlined,
  EditOutlined,
  MinusOutlined,
  PlusOutlined,
  QuestionCircleOutlined,
  EllipsisOutlined,
} from "@ant-design/icons-vue";
import trans from "./components/trans.vue";
import scheduling from "./components/scheduling.vue";
import IconFont from "@/components/c-iconfont";

import { cloneDeep } from "lodash";

export default defineComponent({
  components: {
    EllipsisOutlined,
    QuestionCircleOutlined,
    PlusOutlined,
    MinusOutlined,
    EditOutlined,
    CheckOutlined,
    trans,
    scheduling,
    IconFont,
  },
  setup(props) {
    // ---------------------------------------------------巡检任务树---------------------------------------------
    const taskTree = ref<any[]>([]); // 巡检任务树

    const selectedTaskKeys = ref<string[]>([]); // 选中的巡检树id

    const selectedTaskKeysByTemp = ref<string[]>([]); // 临时选中的巡检树id

    const showTaskInfo = ref<boolean>(false); // 显示任务信息

    const expandedKeys = ref<(string | number)[]>([]);

    // 选中巡检树事件
    const selectTree = (selectedKeys, e) => {
      if (e.selected) {
        selectedTaskKeysByTemp.value = selectedKeys;
      } else {
        selectedTaskKeys.value = selectedTaskKeysByTemp.value;
        return;
      }
      if (e.node.type === "DIRECTORY") {
        formState.parentId = selectedKeys[0];
        showTaskInfo.value = false;
        taskTitle.value = "";
      } else {
        formState.parentId = "";
        getThisTaskinfo(selectedKeys[0], e.node.label);
        showTaskInfo.value = true;
      }
    };

    // 任务列表
    const taskTitle = ref<string>("");
    // 剔除树结构中类型为 TASK 的节点
    function removeTaskNodes(nodes) {
      return nodes
        .filter((node) => {
          // 过滤掉当前节点的 type 为 TASK 的情况
          return node.type !== "TASK";
        })
        .map((node) => {
          // 深拷贝节点，避免修改原数据
          const newNode = { ...node };
          // 递归处理子节点
          if (newNode.children && newNode.children.length > 0) {
            newNode.children = removeTaskNodes(newNode.children);
          } else {
            newNode.children = []; // 确保 children 存在
          }
          return newNode;
        });
    }

    // 获取巡检任务列表
    // 获取所有ID
    function findNodesWithChildren(treeArray) {
      const result = [];

      function dfs(node) {
        if (Array.isArray(node.children) && node.children.length > 0) {
          result.push(node.id);
          node.children.forEach((child) => dfs(child));
        }
      }

      treeArray.forEach((root) => dfs(root));

      return result;
    }
    const getTaskTree = async () => {
      const res = await getTaskTreeInterface();
      taskTree.value = res.data; // 巡检任务树赋值
      formTreeSelect.value = removeTaskNodes(res.data); // 表单树选择赋值
      expandedKeys.value = findNodesWithChildren(res.data);
    };

    // -------------------------------------------------------------------------树节点搜索---------------------------------------------------------
    const searchList = ref<any[]>([]); // 搜索结果列表

    const searchValue = ref<string>(""); // 当前搜索值

    const nodeRefs = ref({}); // 节点引用

    const searchKey = ref(""); // 搜索关键字

    // 找到所有父节点的ID
    function findParentIdsFromTree(treeArray, targetId) {
      const path = [];

      function dfs(node, ancestors) {
        if (node.id === targetId) {
          path.push(...ancestors);
          return true;
        }

        if (node.children && node.children.length > 0) {
          for (const child of node.children) {
            if (dfs(child, [...ancestors, node.id])) {
              return true;
            }
          }
        }

        return false;
      }

      for (const root of treeArray) {
        if (dfs(root, [])) {
          break;
        }
      }

      return path;
    }

    function searchOutputParams(tree, keyword) {
      const results = [];

      if (keyword === "") {
        return [];
      }
      function traverse(node, parent, grandparent) {
        // 匹配output-param节点且包含关键字
        if (node.label.toUpperCase().includes(keyword.toUpperCase())) {
          // 构建层级标签
          const labels = [];
          if (grandparent) labels.push(grandparent.label);
          if (parent) labels.push(parent.label);
          labels.push(node.label);

          // 生成格式化结果
          results.push({
            key: node.id,
            label: labels.join("-"),
          });
        }

        // 递归遍历子节点，维护父级关系
        if (node.children) {
          node.children.forEach((child) => {
            // 当前节点成为父级，原父级成为祖父级
            traverse(child, node, parent);
          });
        }
      }

      // 从根节点开始遍历
      tree.forEach((root) => traverse(root, null, null));

      return results;
    }

    // 搜索树
    const searchTree = () => {
      // debugger;
      searchList.value = searchOutputParams(taskTree.value, searchValue.value);
      searchKey.value = "";
    };

    // 滚动到选中节点
    const scrollToNode = () => {
      if (searchKey.value && nodeRefs.value[searchKey.value]) {
        const node = nodeRefs.value[searchKey.value];
        node.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    };
    // 选中搜索结果
    const handleSelect = async (item) => {
      searchKey.value = item.key;
      await nextTick();
      console.log(taskTree.value, item.key);
      console.log(findParentIdsFromTree(taskTree.value, item.key));
      findParentIdsFromTree(taskTree.value, item.key).forEach((item) => {
        expandedKeys.value.push(item);
      });
      scrollToNode();
    };

    function findNodeById(nodes, targetId) {
      // 遍历当前层级的节点
      for (const node of nodes) {
        // 找到匹配的节点，直接返回
        if (node.id === targetId) return node;
        // 如果当前节点有子节点，递归查找子节点
        if (node.children?.length) {
          const found = findNodeById(node.children, targetId);
          if (found) return found; // 子节点中找到则提前返回
        }
      }
      return null; // 未找到
    }
    // --------------------------------------------------添加巡检任务------------------------------------------
    const addModal = () => {
      formState.id = "";
      isEdit.value = false;
      addTaskModal.value = true;
    };
    const addTaskModal = ref<boolean>(false); // 控制添加弹窗
    const formRef = ref<FormInstance>(); // 表单ref
    // 表单数据
    interface Values {
      id?: string;
      label: string;
      type: string;
      parentId: string;
    }

    const formState = reactive<Values>({
      id: "",
      label: "",
      type: "DIRECTORY",
      parentId: "",
    });

    const formTreeSelect = ref<any[]>([]); // 表单树选择

    const validatePoint = async (_rule: any, value: string) => {
      if (value === "") {
        return Promise.reject(`名称不能为空`);
      }
      const myreg = /^(?![!@#$%^&*()+=\[\]{}\\|;:'",.<>?`~])[\w\u4e00-\u9fa5]{1,20}$/;
      if (!myreg.test(value)) {
        return Promise.reject("名称不能包含特殊字符，20个字符以内");
      }
    };
    // 添加巡检树节点校验
    const rules = {
      label: [{ required: true, validator: validatePoint, trigger: "change" }],
    };
    // 执行添加巡检树节点
    const handleAddTask = async () => {
      const validate = await formRef.value.validateFields();
      if (!validate) return;

      const res = isEdit.value ? await editTaskName(formState) : await addTask(formState);
      if (res.code !== 0) return;

      message.success(isEdit.value ? "更新成功" : "添加成功");
      await getTaskTree();
      selectedTaskKeys.value = [res.data.id];
      selectedTaskKeysByTemp.value = [res.data.id];
      if (res.data.type === "DIRECTORY") {
        formState.parentId = res.data.id;
      } else {
        formState.parentId = "";
        getThisTaskinfo(res.data.id, res.data.label);
        showTaskInfo.value = true;
      }

      addTaskModal.value = false;
    };
    // 关闭弹窗后处理逻辑
    const afterClose = () => {
      formState.id = "";
      formState.label = "";
      formState.type = "DIRECTORY";
      const node = findNodeById(taskTree.value, selectedTaskKeys.value[0]);
      if (node.type === "DIRECTORY") {
        formState.parentId = selectedTaskKeys.value[0];
      } else {
        formState.parentId = "";
      }

      formRef.value.clearValidate();
    };

    // --------------------------------------------------删除巡检任务-----------------------------------------
    const handleDeleteTask = (data) => {
      Modal.confirm({
        title: "删除",
        content: "确认删除该节点?",
        onOk: async () => {
          const res = await deleteTask(data.id);
          if (res.code !== 0) return;

          message.success("删除成功");
          selectedTaskKeys.value = [];
          selectedTaskKeysByTemp.value = [];
          formState.label = "";
          formState.type = "DIRECTORY";
          formState.parentId = "";
          showTaskInfo.value = false;
          taskTitle.value = "";

          await getTaskTree();
        },
      });
    };

    // ----------------------------------------------编辑巡检任务----------------------------------------------
    const isEdit = ref<boolean>(false);
    const editTaskNameModal = (data) => {
      formState.id = data.id;
      formState.label = data.label;
      formState.type = data.type;
      formState.parentId = data.parentId;
      addTaskModal.value = true;
      isEdit.value = true;
    };

    // ----------------------------------------------当前选择巡检任务全部信息------------------------------
    const TransRef = ref(null);
    const thisTaskinfo = reactive({
      id: "",
      schedulePeriods: null,
      taskNodes: [],
      scheduleEnable: false,
      interval: null,
      intervalUnit: "SECONDS",
      triggerType: "TIME_TRIGGER",
      variableType: "DCS",
      namespace: "",
      tag: "",
      item: "",
      variableName: null,
      triggerValue: null,
    });
    // 获取当前巡检任务的信息
    const getThisTaskinfo = (id, name?) => {
      thisTaskinfo.id = id;
      if (name) {
        taskTitle.value = name;
      }
      if (id) {
        getThisTaskPoint(id).then((res) => {
          if (res.code === 0) {
            console.log("TaskPoint===》", res.data);
            const rightPoint = res.data.taskNodes.map((item) => {
              return item.id;
            });
            TransRef.value.getRight(rightPoint);
            schedulingTime.value.getFeedBackData(res.data.schedulePeriods);
            thisTaskinfo.namespace = res.data.namespace;
            thisTaskinfo.tag = res.data.tag;
            thisTaskinfo.item = res.data.item;
            thisTaskinfo.scheduleEnable = res.data.scheduleEnable;
            thisTaskinfo.interval = res.data.interval;
            thisTaskinfo.intervalUnit = res.data.intervalUnit;
            thisTaskinfo.triggerType =
              res.data.triggerType === null ? "TIME_TRIGGER" : res.data.triggerType;
            thisTaskinfo.variableName = res.data.variableName;
            thisTaskinfo.triggerValue =
              res.data.triggerValue === null ? null : String(res.data.triggerValue);
          }
        });
      }
    };
    const schedulingTime = ref(null);

    // 提交
    const submitTask = () => {
      thisTaskinfo.taskNodes = TransRef.value.getRightPoint();
      thisTaskinfo.schedulePeriods = schedulingTime.value.getData(thisTaskinfo.id);
      editTask(thisTaskinfo).then((res) => {
        if (res.code === 0) {
          message.success("操作成功");
          // getTaskTree();
        }
      });
    };

    // 穿梭框数据

    onMounted(() => {
      // getTaskTree().then((id) => {
      //   getThisTaskinfo(id);
      // });
      getTaskTree();
    });
    return {
      taskTree,
      selectedTaskKeys,
      showTaskInfo,
      selectTree,
      taskTitle,
      getThisTaskinfo,
      expandedKeys,
      searchList,
      searchValue,
      nodeRefs,
      searchKey,
      searchTree,
      addModal,
      handleSelect,
      formTreeSelect,
      addTaskModal,
      editTaskNameModal,
      isEdit,
      formRef,
      formState,
      rules,
      handleAddTask,
      afterClose,
      handleDeleteTask,
      TransRef,
      thisTaskinfo,
      schedulingTime,
      submitTask,
    };
  },
});
</script>

<style scoped>
.box {
  width: 96%;
  margin-left: 1%;
}

.leftTree {
  width: 100%;
  height: 790px;
  overflow-y: auto;
}

.rightTable {
  width: 100%;
  height: 790px;
}

.saveicon {
  margin-left: 10px;
}

.point-config {
  margin-top: 10px;
  margin-left: 71px;
}
.custom-tree-node {
  position: relative;
  display: flex;
  justify-content: space-between;
}
.custom-tree-node:hover {
  .inline-btn {
    opacity: 1;
  }
}

.inline-btn {
  position: sticky;
  right: 5px;
  opacity: 0;
}

:deep(.ant-form-item) {
  margin-bottom: 12px !important;
}

.searchBack {
  background-color: #fffb8f;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}
</style>
