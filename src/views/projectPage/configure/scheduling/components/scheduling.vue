<template>
  <div>
    <div style="margin-left: 35px">
      <!-- <span>调度间隔：</span> -->
    </div>
    <div class="c-weektime">
      <div class="c-schedue"></div>
      <div :class="{ 'c-schedue': true, 'c-schedue-notransi': mode }" :style="styleValue"></div>

      <table
        :class="{ 'c-min-table': colspan < 2 }"
        class="c-weektime-table"
        aria-describedby="weektime-table"
      >
        <thead class="c-weektime-head">
          <tr>
            <th rowspan="8" class="week-td">星期/时间</th>
            <th :colspan="12 * colspan">00:00 - 12:00</th>
            <th :colspan="12 * colspan">12:00 - 24:00</th>
          </tr>
          <tr>
            <td v-for="t in theadArr" :key="t" :colspan="colspan">{{ t }}</td>
          </tr>
        </thead>
        <tbody class="c-weektime-body">
          <tr v-for="t in weekData" :key="t.row">
            <td>{{ t.value }}</td>
            <td
              v-for="n in t.child"
              :key="`${n.row}-${n.col}`"
              :data-week="n.row"
              :data-time="n.col"
              :class="selectClasses(n)"
              @mouseenter="cellEnter(n)"
              @mousedown="cellDown(n)"
              @mouseup="cellUp(n)"
              class="weektime-atom-item"
            ></td>
          </tr>
        </tbody>
      </table>
      <table
        :class="{ 'c-min-table': colspan < 2 }"
        class="c-weektime-table"
        v-if="selectState"
        style="margin-left: 10px; max-width: 424px; overflow-y: auto; max-height: 239px"
        aria-describedby="调度周期表"
      >
        <thead class="c-weektime-head">
          <tr>
            <th>
              <div class="g-clearfix c-weektime-con" style="height: 30px">
                <a
                  @click.prevent="clearSelect"
                  class="g-pull-right"
                  style="font-size: 14px; margin-right: 10px"
                >
                  清空所有
                </a>
              </div>
            </th>
          </tr>
        </thead>
        <tbody class="c-weektime-body">
          <tr>
            <td :colspan="49" class="c-weektime-preview">
              <div class="c-weektime-time">
                <div v-for="t in selectValue" :key="t.id">
                  <p v-if="t.value">
                    <span class="g-tip-text">{{ t.week }}：</span>
                    <span>{{ t.value }}</span>
                  </p>
                </div>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, computed, toRef, toRaw } from "vue";
import weektimeData from "@/common/weektime_data";
import { JsonParseFun, JsonStrFun } from "@/common/utils";

// type
interface IWeekData {
  value: string;
  row: number;
  child: any[];
}

// 生成24位二进制字符
const createArr = (len) => {
  return Array.from(Array(len)).map((ret, id) => id);
};

export default defineComponent({
  setup(props, { emit }) {
    const width = ref<number>(0); // 根据一小格算出来的所选宽
    const height = ref<number>(0); // 所选高
    const left = ref<number>(0); // 所选左距离
    const top = ref<number>(0); // 所选顶距离
    const mode = ref<number>(0);
    const row = ref<number>(0);
    const col = ref<number>(0);
    const theadArr = ref<any[]>([]);
    theadArr.value = createArr(24);
    const weekData = ref<IWeekData[]>(weektimeData);
    const getFeedBackData = (feedbackData) => {
      const data: IWeekData[] = JsonParseFun(JsonStrFun(weektimeData));
      if (feedbackData === null) {
        intervalUnit.value = "SECONDS";
        timeInterval.value = 5;
        weekData.value = data;
        return;
      }
      intervalUnit.value = feedbackData.intervalUnit;
      timeInterval.value = feedbackData.interval;
      const feedBackString = feedbackData.periods
        .map((item) => {
          return item.timeSlot;
        })
        .join("");
      let idx = 0;
      for (let i = 0; i < data.length; i++) {
        const children = data[i].child;
        for (let j = 0; j < children.length; j++) {
          const n = feedBackString.substr(idx, 1);
          data[i].child[j].check = Boolean(parseInt(n));
          idx++;
        }
      }
      weekData.value = data;
      // return data;
    };
    const colspan = ref<number>(2); // 最小颗粒度（半小时）
    // 计算所选区域的宽高位置
    const styleValue = computed(() => {
      return {
        width: `${width.value}px`,
        height: `${height.value}px`,
        left: `${left.value}px`,
        top: `${top.value}px`,
      };
    });
    // 选中的值
    const splicing = (list: any[]) => {
      let same;
      let i = -1;
      const len = list.length;
      const arr = [];
      if (!len) return;
      while (++i < len) {
        const item = list[i];
        if (item.check) {
          if (item.check !== Boolean(same)) {
            arr.push(...["、", item.begin, "~", item.end === "00:00" ? "24:00" : item.end]);
          } else if (arr.length) {
            arr.pop();
            arr.push(item.end === "00:00" ? "24:00" : item.end);
          }
        }
        same = Boolean(item.check);
      }
      arr.shift();
      return arr.join("");
    };
    // 显示选择的值
    const selectValue = computed(() => {
      return weekData.value.map((item) => {
        return {
          id: item.row,
          week: item.value,
          value: splicing(item.child),
        };
      });
    });
    // 选取状态
    const selectState = computed(() => {
      return weekData.value.some((ret) => ret.child.some((item) => item.check));
    });
    const selectClasses = computed(() => {
      return (n) => ((n as any).check ? "ui-selected" : "");
    });
    // 点击区域判断
    const cellEnter = (item) => {
      const ele: HTMLElement = <HTMLElement>(
        document.querySelector(`td[data-week='${item.row}'][data-time='${item.col}']`)
      );
      // 判断是否处于编辑状态
      if (ele && !mode.value) {
        left.value = ele.offsetLeft;
        top.value = ele.offsetTop;
      } else if (item.col <= col.value && item.row <= row.value) {
        width.value = (col.value - item.col + 1) * ele.offsetWidth;
        height.value = (row.value - item.row + 1) * ele.offsetHeight;
        left.value = ele.offsetLeft;
        top.value = ele.offsetTop;
      } else if (item.col >= col.value && item.row >= row.value) {
        width.value = (item.col - col.value + 1) * ele.offsetWidth;
        height.value = (item.row - row.value + 1) * ele.offsetHeight;
        if (item.col > col.value && item.row === row.value) top.value = ele.offsetTop;
        if (item.col === col.value && item.row > row.value) left.value = ele.offsetLeft;
      } else if (item.col > col.value && item.row < row.value) {
        width.value = (item.col - col.value + 1) * ele.offsetWidth;
        height.value = (row.value - item.row + 1) * ele.offsetHeight;
        top.value = ele.offsetTop;
      } else if (item.col < col.value && item.row > row.value) {
        width.value = (col.value - item.col + 1) * ele.offsetWidth;
        height.value = (item.row - row.value + 1) * ele.offsetHeight;
        left.value = ele.offsetLeft;
      }
    };

    const cellDown = (item) => {
      const ele: HTMLElement = <HTMLElement>(
        document.querySelector(`td[data-week='${item.row}'][data-time='${item.col}']`)
      );
      // item.check = Boolean(item.check);
      mode.value = 1;
      if (ele) {
        width.value = ele.offsetWidth;
        height.value = ele.offsetHeight;
      }
      row.value = item.row;
      col.value = item.col;
    };

    const cellUp = (item) => {
      const thisCheck = item.check;
      if (item.col <= col.value && item.row <= row.value) {
        selectWeek([item.row, row.value], [item.col, col.value], !thisCheck);
      } else if (item.col >= col.value && item.row >= row.value) {
        selectWeek([row.value, item.row], [col.value, item.col], !thisCheck);
      } else if (item.col > col.value && item.row < row.value) {
        selectWeek([item.row, row.value], [col.value, item.col], !thisCheck);
      } else if (item.col < col.value && item.row > row.value) {
        selectWeek([row.value, item.row], [item.col, col.value], !thisCheck);
      }

      width.value = 0;
      height.value = 0;
      mode.value = 0;
    };
    const selectWeek = (row: number[], col: number[], check: boolean) => {
      const [minRow, maxRow] = row;
      const [minCol, maxCol] = col;
      weekData.value.forEach((item: any) => {
        item.child.forEach((t) => {
          if (t.row >= minRow && t.row <= maxRow && t.col >= minCol && t.col <= maxCol) {
            t.check = check;
          }
        });
      });
    };
    // 清空数据
    const clearWeektime = () => {
      weekData.value.forEach((item) => {
        item.child.forEach((t) => {
          t.check = false;
        });
      });
    };
    const clearSelect = () => {
      clearWeektime();
    };
    // 获取传递数据48位二进制
    const timeInterval = ref<number>(5);
    const intervalUnit = ref<string>("SECONDS");
    const getData = (id) => {
      const data = weekData.value.map((item) => {
        return {
          week: item.row + 1,
          timeSlot: item.child
            .map((t) => {
              return t.check ? 1 : 0;
            })
            .join(""),
        };
      });
      const toData = {
        interval: timeInterval.value,
        intervalUnit: intervalUnit.value,
        periods: data,
        taskId: id,
      };
      return toData;
    };
    return {
      weekData,
      colspan,
      theadArr,
      styleValue,
      selectValue,
      selectState,
      selectClasses,
      mode,
      cellEnter,
      cellDown,
      cellUp,
      emit,
      clearSelect,
      getData,
      getFeedBackData,
      timeInterval,
      intervalUnit,
    };
  },
});
</script>

<style lang="less" scoped>
.c-weektime {
  min-width: 640px;
  max-height: 239px;
  position: relative;
  // display: inline-block;
  display: flex;
  margin-left: 35px;
  margin-top: 10px;
}
.c-schedue {
  background: rgba(0, 140, 255, 0.4);
  position: absolute;
  width: 0;
  height: 0;
  opacity: 0.6;
  pointer-events: none;
}
.c-schedue-notransi {
  transition: width 0.12s ease, height 0.12s ease, top 0.12s ease, left 0.12s ease;
}
.c-weektime-table {
  border-collapse: collapse;
  th {
    vertical-align: inherit;
    font-weight: bold;
  }
  tr {
    height: 10px;
  }
  tr,
  td,
  th {
    user-select: none;
    border: 1px solid #dee4f5;
    text-align: center;
    min-width: 12px;
    line-height: 1.8em;
    transition: background 0.2s ease;
  }
  .c-weektime-head {
    font-size: 12px;
    border: 1px solid #dee4f5;
    .week-td {
      width: 70px;
    }
  }
  .c-weektime-body {
    font-size: 12px;
    td {
      &.weektime-atom-item {
        user-select: unset;
        background-color: #f5f5f5;
      }
      &.ui-selected {
        background-color: rgba(0, 140, 255, 0.4) ;
      }
    }
  }
  .c-weektime-preview {
    line-height: 2.4em;
    padding: 0 10px;
    font-size: 14px;
    .c-weektime-con {
      line-height: 46px;
      user-select: none;
    }
    .c-weektime-time {
      text-align: left;
      line-height: 2.4em;
      max-height: 300px;
      overflow-y: auto;
      p {
        max-width: 625px;
        line-height: 1.4em;
        word-break: break-all;
        margin-bottom: 8px;
      }
    }
  }
}
.c-min-table {
  tr,
  td,
  th {
    min-width: 24px;
  }
}
.g-clearfix {
  &:after,
  &:before {
    clear: both;
    content: " ";
    display: table;
  }
}
.g-pull-left {
  float: left;
}
.g-pull-right {
  float: right;
  color: #006bc3;
}
.g-tip-text {
  color: #999;
}
</style>
