<template>
  <div class="boxlist">
    <a-transfer
      v-model:target-keys="targetRightKeys"
      :data-source="leftListData"
      :show-search="true"
      :filter-option="(inputValue, item) => item.label.indexOf(inputValue) !== -1"
      :show-select-all="true"
      @change="onChange"
      :rowKey="(record) => record.id"
      :titles="['未配置调度', '已配置调度']"
      v-model:selected-keys="selectedKeys"
      style="max-width: 600px"
    >
      <template
        #children="{
          direction,
          filteredItems,
          selectedKeys,
          disabled: listDisabled,
          onItemSelectAll,
          onItemSelect,
        }"
      >
        <a-table
          :scroll="{ y: 200 }"
          :rowKey="(record) => record.id"
          :row-selection="
            getRowSelection({
              disabled: listDisabled,
              selectedKeys,
              onItemSelectAll,
              onItemSelect,
            })
          "
          :columns="direction === 'left' ? leftColumns : rightColumns"
          :data-source="filteredItems"
          size="small"
          :style="{ pointerEvents: listDisabled ? 'none' : null }"
          :custom-row="(record, index) => selectedRow(record, index, direction, filteredItems)"
          :pagination="false"
        />
      </template>
    </a-transfer>

    <table class="table" v-if="selectList" aria-describedby="调度配置表">
      <thead>
        <tr>
          <th>{{ selectList.label }}</th>
        </tr>
      </thead>
      <tbody>
        <tr v-for="(item, index) in Object.keys(selectList)" :key="index">
          <td v-if="item === 'available'">是否启用</td>
          <td v-if="item === 'available'">
            <a-switch v-model:checked="selectList[item]" />
          </td>
          <td v-if="item === 'preExecuteDelay'">前置延时</td>
          <td v-if="item === 'preExecuteDelay'">
            <a-input-number v-model:value="selectList[item]" :min="0" :max="50" :precision="0">
              <template #addonAfter>
                <span>秒</span>
              </template>
            </a-input-number>
          </td>
          <td v-if="item === 'postExecuteDelay'">后置延时</td>
          <td v-if="item === 'postExecuteDelay'">
            <a-input-number v-model:value="selectList[item]" :min="0" :max="50" :precision="0">
              <template #addonAfter>
                <span>秒</span>
              </template>
            </a-input-number>
          </td>
          <td v-if="item === 'validTimes'">验证次数</td>
          <td v-if="item === 'validTimes'">
            <a-input-number v-model:value="selectList[item]" :min="1" :max="10" :precision="0">
              <template #addonAfter>
                <span>次</span>
              </template>
            </a-input-number>
          </td>
          <!-- <td v-if="item === 'failureStrategy'">失败策略</td> -->
          <!-- <td v-if="item === 'failureStrategy'">
              <a-select v-model:value="selectList[item]" style="width: 100%;">
                <a-select-option value="ALARM">报警</a-select-option>
                <a-select-option value="RECORD">仅记录</a-select-option>
              </a-select>
            </td> -->
        </tr>
      </tbody>
    </table>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, reactive, ref, onMounted, toRefs } from "vue";
import { message } from "ant-design-vue";
import { getTaskPoint } from "@/api/project";
import { QuestionCircleOutlined, LeftOutlined, RightOutlined } from "@ant-design/icons-vue";
import data from "@/common/weektime_data";

export default defineComponent({
  components: {
    QuestionCircleOutlined,
    LeftOutlined,
    RightOutlined,
  },
  props: {
    channelType: {
      type: String,
      default: "",
    },
  },
  setup(props) {
    /**
     * @description 穿梭框
     */

    const { channelType } = toRefs(props);
    const leftListData = ref([]); // 左侧列表全部数据合
    const targetRightKeys = ref<string[]>([]); // 右侧列表全部数据
    const tempRightKey = ref<string[]>([]);
    const selectedKeys = ref<string[]>([]); // 左侧列表选中的数据
    // 单通道模式检查是否是同一个通道
    const checkSameChannel = (list: any[]) => {
      let isSame: boolean = true;
      list.forEach((item) => {
        if (item.channelId !== list[0].channelId) {
          isSame = false;
        }
      });
      return isSame;
    };

    const onChange = (targetKeys: string[], direction, moveKeys: string[]) => {
      if (direction === "left") {
        if (targetKeys.length === 0) {
          selectList.value = null;
          selectRightId.value = "";
        }
        if (moveKeys.includes(selectRightId.value)) {
          selectList.value = null;
          selectRightId.value = "";
        }
      }
      if (direction === "right" && channelType.value === "SINGLE_CHANNEL") {
        const moveList = leftListData.value.filter((item) => moveKeys.includes(item.id));
        // 所选算法通道不相同
        if (!checkSameChannel(moveList)) {
          message.warn("单通道类型下，所选算法必须为同一通道", 3);
          targetRightKeys.value = targetRightKeys.value.filter((item) => !moveKeys.includes(item));
          return;
        }
        // 所选算法通道相同，与选取的通道不相同
        if (tempRightKey.value.length !== 0) {
          const right = leftListData.value.filter((item) => item.id === tempRightKey.value[0]);
          if (moveList[0].channelId !== right[0].channelId) {
            message.warn("单通道类型下，所选算法必须为同一通道", 3);
            targetRightKeys.value = targetRightKeys.value.filter(
              (item) => !moveKeys.includes(item)
            );
            return;
          }
        }
      }
      tempRightKey.value = targetKeys;
    };

    const leftColumns = ref([
      {
        dataIndex: "label",
        title: "name",
      },
    ]);
    const rightColumns = ref([
      {
        dataIndex: "label",
        title: "name",
      },
    ]);
    // 勾选
    const getRowSelection = ({
      disabled,
      selectedKeys,
      onItemSelectAll,
      onItemSelect,
    }: Record<string, any>) => {
      return {
        getCheckboxProps: (item: Record<string, string | boolean>) => ({
          disabled: disabled || item.disabled,
        }),
        onSelectAll(selected: boolean, selectedRows: Record<string, string | boolean>[]) {
          const treeSelectedKeys = selectedRows
            .filter((item) => !item.disabled)
            .map(({ key }) => key);
          onItemSelectAll(treeSelectedKeys, selected);
        },
        onSelect({ key }: Record<string, string>, selected: boolean) {
          onItemSelect(key, selected);
        },
        selectedRowKeys: selectedKeys,
      };
    };

    // 点击右侧列表、拖拽元素
    const dragItem = ref<any>(); // 拖拽元素
    const dragIndex = ref<any>(); // 拖拽的index
    const targetArr = ref<any[]>([]);
    const lastData = ref<any[]>([]);
    const selectedRow = (record, index, direction, filteredItems: any[]) => ({
      style: {
        "background-color": record.id === selectRightId.value ? "#e6f7ff" : "",
        color: record.available ? "" : "red",
        width: "200px",
      },
      onClick: () => {
        // onItemSelect(key, !selectedKeys.includes(key));
        if (direction === "right") {
          selectRightId.value = record.id;
          selectList.value = record;
        }
      },
      // 鼠标移入
      onMouseenter: (event: any) => {
        if (direction === "right") {
          const ev = event || window.event;
          if (ev && ev.target) {
            ev.target.draggable = true;
          }
        }
      },
      // 开始拖拽
      onDragstart: (event: Event | undefined) => {
        if (direction === "right") {
          // 兼容IE
          const ev = event || window.event;

          // 阻止冒泡
          ev && ev.stopPropagation();

          // 赋值源数据
          dragItem.value = record;
          dragIndex.value = index;
          // console.log(dragIndex.value,dragItem.value);
        }
      },
      // 拖拽经过元素
      onDragover: (event: any) => {
        if (direction === "right") {
          // 兼容
          const ev = event || window.event;
          // 阻止默认
          ev && ev.preventDefault();
          // 拖拽自身
          if (index === dragIndex.value) {
            return;
          }
          // 在经过的元素的上面或者下面添加一条线
          // console.log(ev,'evev');
          const nowLine = ev.target.closest("tr.ant-table-row");
          if (!targetArr.value.includes(nowLine)) {
            targetArr.value.push(nowLine);
          }
          if (index > dragIndex.value) {
            if (!nowLine.classList.contains("afterLine")) {
              targetArr.value.forEach((item: any) => {
                item.classList.remove("beforLine");
                item.classList.remove("afterLine");
              });
              nowLine.classList.add("afterLine");
            }
          } else {
            if (!nowLine.classList.contains("beforLine")) {
              targetArr.value.forEach((item: any) => {
                item.classList.remove("beforLine");
                item.classList.remove("afterLine");
              });
              nowLine.classList.add("beforLine");
            }
          }
        }
      },
      onDrop: (event: Event | undefined) => {
        // 兼容
        const ev = event || window.event;

        // 阻止冒泡
        ev && ev.stopPropagation();

        // 选中的数据
        const dragData = filteredItems.filter((item: any) => item.id === dragItem.value.id);

        // 过滤其他的数据
        lastData.value = filteredItems.filter((item: any) => item.id !== dragItem.value.id);

        lastData.value.splice(index, 0, ...dragData);
        const lastId = lastData.value.map((item) => {
          return item.id;
        });

        targetRightKeys.value = lastId;

        targetArr.value.forEach((item) => {
          item.classList.remove("beforLine");
          item.classList.remove("afterLine");
        });
        targetArr.value = [];
        //
        // console.log(filteredItems,'flieter');
      },
    });
    // 右侧选中配置
    const selectList = ref(null);
    const selectRightId = ref<string>(""); // 点击的右侧id
    // 获取所有的点
    const getAllPoint = (rightPoint) => {
      getTaskPoint().then((res) => {
        if (res.code === 0) {
          leftListData.value = res.data;
          targetRightKeys.value = rightPoint;
          tempRightKey.value = rightPoint;
          selectList.value = null;
          selectRightId.value = "";
        }
      });
    };
    // 获取右侧点
    const getRight = (rightPoint: string[]) => {
      getAllPoint(rightPoint);
    };

    // 传递点信息
    const getRightPoint = () => {
      const target = targetRightKeys.value.map((item) => {
        const data = leftListData.value.find((element) => element.id === item);
        return data;
      });
      return target;
    };
    // 多通道转换为单通道
    const checkTransType = () => {
      const list = leftListData.value.filter((item) => targetRightKeys.value.includes(item.id));
      return checkSameChannel(list);
    };
    const setTask = () => {
      // 清空选中
      selectList.value = null;
      selectRightId.value = "";
      selectedKeys.value = [];
    };

    onMounted(() => {
      // getAllPoint();
    });

    return {
      leftListData,
      targetRightKeys,
      onChange,
      getRowSelection,
      selectedRow,
      selectedKeys,
      selectList,
      getAllPoint,
      getRight,
      leftColumns,
      rightColumns,
      getRightPoint,
      checkTransType,
      setTask,
    };
  },
});
</script>

<style scoped>
.boxlist {
  display: flex;
  height: 300px;
}
.leftList {
  height: 200px;
  width: 200px;
  overflow-y: auto;
}
.rightList {
  height: 200px;
  width: 200px;
  margin-left: 25px;
  overflow-y: auto;
}
.rightData {
  cursor: pointer;
}

.table {
  border: 1px solid #d9d9d9;
  height: 218px;
  margin-left: 20px;
  width: 300px;
  text-align: center;
}
.table > tr {
  border: 1px solid #d9d9d9;
}
.table > tr > td {
  width: 100px;
  border: 1px solid #d9d9d9;
  height: 35px;
}

:deep(.ant-transfer-customize-list .ant-transfer-list) {
  max-width: 277px;
}

:deep(.ant-table-tbody > tr.ant-table-row-selected > td) {
  background: none;
}
:deep(.ant-table-thead) {
  display: none;
}
:deep(.ant-table-tbody > tr > td) {
  height: 35px !important;
}

:deep(.ant-table-tbody > tr.ant-table-row:hover > td) {
  background: none !important;
}

:deep(.beforLine > .ant-table-cell) {
  border-top: 1.1px dashed #1890ff !important;
}
:deep(.afterLine > .ant-table-cell) {
  border-bottom: 1px dashed #1890ff !important;
}

/* :deep(.afterLine){
      border-bottom: 1px dashed #1890ff !important;
    } */
</style>
