<template>
  <a-space style="margin-bottom: 10px">
    <a-button @click="addAl">同步</a-button>
    <a-button @click="deleteAl">删除</a-button>
  </a-space>
  <a-table
    :columns="columns"
    :data-source="dataSource"
    bordered
    rowKey="id"
    :pagination="hisPgination"
    @change="hisPageChange"
    :rowSelection="rowConfig"
  >
    <template #headerCell="{ title, column }">
      <template v-if="column.dataIndex === 'algorithmCode'">
        {{ title }}
        <a-tooltip placement="top" title="与第三方报警平台(比如:海康电力运检平台)算法code保持一致">
          <QuestionCircleOutlined></QuestionCircleOutlined>
        </a-tooltip>
      </template>

      <template v-if="column.dataIndex === 'ruleCode'">
        {{ title }}
        <a-tooltip placement="top" title="唯一即可,用于NODE-RED中区分报文格式从而进行不同逻辑处理">
          <QuestionCircleOutlined></QuestionCircleOutlined>
        </a-tooltip>
      </template>
    </template>
    <template #bodyCell="{ column, text, record, index }">
      <template v-if="column.dataIndex === 'indexNumber'">
        <div>
          {{ index + 1 }}
        </div>
      </template>
      <template
        v-if="
          [
            'algorithmName',
            'algorithmCode',
            'algorithmLevel',
            'ruleName',
            'ruleCode',
            'intervalTime',
          ].includes(column.dataIndex)
        "
      >
        <div style="padding: 10px">
          <a-input
            v-if="
              editableData[record.id] &&
              ['algorithmName', 'algorithmCode', 'ruleName', 'ruleCode'].includes(column.dataIndex)
            "
            v-model:value="editableData[record.id][column.dataIndex]"
          />

          <a-input-number
            v-else-if="editableData[record.id] && ['intervalTime'].includes(column.dataIndex)"
            :min="0"
            :max="1800"
            v-model:value="editableData[record.id][column.dataIndex]"
          >
            <template #addonAfter>
              <span>秒</span>
            </template>
          </a-input-number>
          <a-select
            v-model:value="editableData[record.id][column.dataIndex]"
            v-else-if="editableData[record.id] && ['algorithmLevel'].includes(column.dataIndex)"
          >
            <a-select-option value="高高">高高</a-select-option>
            <a-select-option value="高">高</a-select-option>
            <a-select-option value="低">低</a-select-option>
            <a-select-option value="低低">低低</a-select-option>
          </a-select>

          <template v-else>
            {{ text }}
          </template>
        </div>
      </template>
      <template v-else-if="column.dataIndex === 'operation'">
        <div class="editable-row-operations">
          <span v-if="editableData[record.id]">
            <a-typography-link @click="save(record.id)">保存</a-typography-link>
            <a-popconfirm title="确定取消编辑吗？" @confirm="cancel(record.id)">
              <a>取消</a>
            </a-popconfirm>
          </span>
          <span v-else>
            <a @click="edit(record.id)">编辑</a>
          </span>
        </div>
      </template>
    </template>
  </a-table>

  <!-- 添加算法弹窗 -->
  <a-modal
    v-model:visible="showChannelModal"
    title="添加算法"
    :maskClosable="false"
    @ok="addAlHandle"
    @cancel="cancelHandle"
  >
    <a-form
      ref="formRef"
      :model="formState"
      :labelCol="{ span: 6 }"
      :wrapperCol="{ span: 14 }"
      :rules="rules"
    >
      <a-form-item label="算法名称" name="algorithmName">
        <a-input v-model:value="formState.algorithmName"></a-input>
      </a-form-item>
      <a-form-item label="算法等级" name="algorithmLevel">
        <!-- <a-input v-model:value="formState.algorithmLevel"></a-input> -->
        <a-select v-model:value="formState.algorithmLevel">
          <a-select-option value="高高">高高</a-select-option>
          <a-select-option value="高">高</a-select-option>
          <a-select-option value="低">低</a-select-option>
          <a-select-option value="低低">低低</a-select-option>
        </a-select>
      </a-form-item>
      <a-form-item label="算法code" name="algorithmCode">
        <a-input v-model:value="formState.algorithmCode"></a-input>
      </a-form-item>
      <a-form-item label="规则名称" name="ruleName">
        <a-input v-model:value="formState.ruleName"></a-input>
      </a-form-item>
      <a-form-item label="规则code" name="ruleCode">
        <a-input v-model:value="formState.ruleCode"></a-input>
      </a-form-item>

      <a-form-item label="间隔时间" name="intervalTime">
        <a-input-number v-model:value="formState.intervalTime" :min="0" :max="1800">
          <template #addonAfter>
            <span>秒</span>
          </template>
        </a-input-number>
      </a-form-item>
    </a-form>
  </a-modal>
</template>
<script lang="ts">
import { JsonParseFun, JsonStrFun } from "@/common/utils";
import { defineComponent, reactive, ref, onMounted, computed } from "vue";
import type { UnwrapRef } from "vue";
import { getHikAl, addHikAl, deleteHikAl, editHikAl, shengTongSyncAl } from "@/api/project";
import { message, Modal } from "ant-design-vue";
import { QuestionCircleOutlined } from "@ant-design/icons-vue";
import type { Rule } from "ant-design-vue/es/form";
import type { FormInstance } from "ant-design-vue";

const columns = [
  {
    title: "序列",
    dataIndex: "indexNumber",
    width: "80px",
    align: "center",
  },
  {
    title: "算法名",
    dataIndex: "algorithmName",
    align: "center",
  },
  {
    title: "算法code",
    dataIndex: "algorithmCode",
    align: "center",
  },
  {
    title: "算法等级",
    dataIndex: "algorithmLevel",
    align: "center",
  },
  {
    title: "规则名称",
    dataIndex: "ruleName",
    align: "center",
  },

  {
    title: "间隔时间",
    dataIndex: "intervalTime",
    align: "center",
  },
  {
    title: "规则code",
    dataIndex: "ruleCode",
    align: "center",
  },
  {
    title: "操作",
    dataIndex: "operation",
    align: "center",
  },
];

interface DataItem {
  id: string;
  algorithmName: string;
  algorithmCode: string;
  algorithmLevel: string;
  ruleName: string;
  ruleCode: string;
  intervalTime: number;
}

export default defineComponent({
  components: {
    QuestionCircleOutlined,
  },
  setup() {
    const dataSource = ref<DataItem[]>([]);
    const editableData: UnwrapRef<Record<string, DataItem>> = reactive({});

    const edit = (id: string) => {
      editableData[id] = JsonParseFun(
        JsonStrFun(dataSource.value.filter((item) => id === item.id)[0])
      );
    };
    const save = (id: string) => {
      const myrsgCode = /^[0-9]{1,9}$/;

      const myrsgName = /^[\u4e00-\u9fa5a-zA-Z0-9_]{1,20}$/;
      if (
        !myrsgCode.test(editableData[id].algorithmCode) ||
        editableData[id].algorithmCode === ""
      ) {
        message.warn("算法code只能为正整数且不能超过9位,且不能为空", 2);
        return;
      }
      if (!myrsgCode.test(editableData[id].ruleCode) || editableData[id].ruleCode === "") {
        message.warn("规则code只能为正整数且不能超过9位,且不能为空", 2);
        return;
      }
      if (
        !myrsgName.test(editableData[id].algorithmName) ||
        editableData[id].algorithmName === ""
      ) {
        message.warn("算法名称只包含中文、英文、数字和下划线且在20字符以内,且不能为空", 2);
        return;
      }

      if (!myrsgName.test(editableData[id].ruleName) || editableData[id].ruleName === "") {
        message.warn("规则名称只包含中文、英文、数字和下划线且在20字符以内,且不能为空", 2);
        return;
      }

      editHikAl(editableData[id]).then((res) => {
        if (res.code === 0) {
          message.success("编辑成功");
          Object.assign(dataSource.value.filter((item) => id === item.id)[0], editableData[id]);
          delete editableData[id];
          getHikAlList();
        }
      });
      // Object.assign(dataSource.value.filter(item => id === item.id)[0], editableData[id]);
      //     delete editableData[id];
    };
    const cancel = (id: string) => {
      delete editableData[id];
    };

    //========  分页 =============
    const hisListParams = reactive({
      size: 10,
      current: 1,
      total: 0,
    });

    const hisPgination = computed(() => ({
      total: hisListParams.total,
      current: hisListParams.current, //
      pageSize: hisListParams.size,
      showTotal: () => `共 ${hisListParams.total} 条`,
      showLessItems: true,
      defaultPageSize: hisListParams.size,
      showSizeChanger: false,
    }));
    const hisPageChange = ({ current, total }) => {
      hisListParams.current = current;
      hisListParams.total = total;
      getHikAlList();
      // setCurrentPage(current);
    };

    const getHikAlList = () => {
      const data = {
        size: hisPgination.value.pageSize,
        current: hisPgination.value.current,
      };
      getHikAl(data).then((res) => {
        if (res.code === 0) {
          hisListParams.total = res.data.total;
          dataSource.value = res.data.records;
        }
      });
    };

    //====================列表选择================
    const selectedKey = ref<string[]>([]);
    const selectedRow = ref<any[]>([]);
    const rowConfig = reactive({
      selectedRowKeys: selectedKey,
      onChange: (keys, rows) => {
        console.log(keys);
        selectedKey.value = keys;
        selectedRow.value = rows;
      },
    });

    //===================添加算法======================

    const showChannelModal = ref<boolean>(false);

    const formRef = ref<FormInstance>();

    const formState = reactive({
      algorithmName: "",
      algorithmLevel: "",
      algorithmCode: "",
      ruleName: "",
      ruleCode: "",
      intervalTime: 0,
    });

    // 校验

    const validatealgorithmName = async (_rule: Rule, value: string) => {
      const rsg = /^[\u4e00-\u9fa5a-zA-Z0-9_]{1,20}$/;

      if (value === "") {
        return Promise.reject("请填写算法名称");
      }
      if (!rsg.test(value)) {
        return Promise.reject("只包含中文、英文、数字和下划线且在20字符以内");
      }
    };

    const validatealgorithmLevel = async (_rule: Rule, value: string) => {
      if (value === "") {
        return Promise.reject("请填写算法等级");
      }
    };

    const validatealgorithmCode = async (_rule: Rule, value: string) => {
      const rsg = /^[0-9]{1,9}$/;
      if (value === "") {
        return Promise.reject("请填写算法code");
      }
      if (!rsg.test(value)) {
        return Promise.reject("算法code只能为正整数且不能超过9位");
      }
    };

    const validateruleName = async (_rule: Rule, value: string) => {
      const rsg = /^[\u4e00-\u9fa5a-zA-Z0-9_]{1,20}$/;
      if (value === "") {
        return Promise.reject("请填写规则名称");
      }

      if (!rsg.test(value)) {
        return Promise.reject("只包含中文、英文、数字和下划线且在20字符以内");
      }
    };
    const validateruleCode = async (_rule: Rule, value: string) => {
      const rsg = /^[0-9]{1,9}$/;
      if (value === "") {
        return Promise.reject("请填写规则code");
      }
      if (!rsg.test(value)) {
        return Promise.reject("规则code只能为正整数且不能超过9位");
      }
    };

    const rules: Record<string, Rule[]> = {
      algorithmName: [
        {
          required: true,
          validator: validatealgorithmName,
          trigger: "change",
        },
      ],
      algorithmLevel: [
        {
          required: true,
          validator: validatealgorithmLevel,
          trigger: "change",
        },
      ],
      algorithmCode: [
        {
          required: true,
          validator: validatealgorithmCode,
          trigger: "change",
        },
      ],
      ruleName: [
        {
          required: true,
          validator: validateruleName,
          trigger: "change",
        },
      ],
      ruleCode: [
        {
          required: true,
          validator: validateruleCode,
          trigger: "change",
        },
      ],
      intervalTime: [
        {
          required: true,
          trigger: "change",
        },
      ],
    };
    const addAl = () => {
      showChannelModal.value = true;
    };

    const addAlHandle = () => {
      formRef.value.validateFields().then(() => {
        addHikAl(formState).then((res) => {
          if (res.code === 0) {
            message.success("添加成功");
            formRef.value.resetFields();
            showChannelModal.value = false;
            getHikAlList();
          }
        });
      });
    };

    const cancelHandle = () => {
      formRef.value.resetFields();
    };

    //======删除算法==========
    const deleteAl = () => {
      if (selectedKey.value.length !== 0) {
        Modal.confirm({
          title: "删除",
          content: "是否删除该规则",
          onOk() {
            deleteHikAl(selectedKey.value).then((res) => {
              if (res.code === 0) {
                message.success("删除成功");
                hisListParams.current = 1;
                getHikAlList();
              }
            });
          },
        });
      } else {
        message.warn("请选择一条规则", 2);
      }
    };

    //=============重置数据==========
    const resetData = () => {
      console.log("qingchukey");
      selectedKey.value = [];
      selectedRow.value = [];
      hisListParams.current = 1;
    };

    onMounted(() => {
      getHikAlList();
    });
    return {
      dataSource,
      columns,
      rowConfig,
      hisPgination,
      hisPageChange,
      editableData,
      edit,
      save,
      cancel,
      addAl,
      showChannelModal,
      deleteAl,
      addAlHandle,
      cancelHandle,
      formState,
      formRef,
      rules,
      resetData,
      getHikAlList,
    };
  },
});
</script>
<style scoped>
.editable-row-operations a {
  margin-right: 8px;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 0px 16px !important;
}
</style>
