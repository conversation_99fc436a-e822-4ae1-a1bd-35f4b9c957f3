<template>
  <div>
    <a-modal v-model:visible="showChannelModal" :title="titleName" :maskClosable="false" @cancel="cancel" @ok="ok">
      <a-form
        ref="formRef"
        :model="formState"
        :labelCol="{ span: 6 }"
        :wrapperCol="{ span: 14 }"
        :rules="rules"
      >
        <a-form-item label="算法名称" name="algorithmName">
          <a-select v-model:value="formState.algorithmName">
            <a-select-option v-for="(item, index) in alList" :key="index" :value="item.id">{{ item.algorithmName }}</a-select-option>
          </a-select>
        </a-form-item>
        <!-- <a-form-item label="命名空间" name="namespace">
          <a-input v-model:value="formState.namespace"></a-input>
        </a-form-item>
        <a-form-item label="点名" name="tag">
          <a-input v-model:value="formState.tag"></a-input>
        </a-form-item>
        <a-form-item label="点项" name="item">
          <a-input v-model:value="formState.item"></a-input>
        </a-form-item> -->
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts">
import { message } from 'ant-design-vue';
import {  ref, defineComponent, reactive } from 'vue';
import { getHikAl , addHikChannelAl} from '@/api/project';
import type { FormInstance } from 'ant-design-vue';
import type { Rule } from 'ant-design-vue/es/form';

export default defineComponent({
  setup(props, {emit}){
    const showChannelModal = ref<Boolean>(false); // 控制对话框
    const titleName = ref<string>();
    const selectedKeys = ref<string>();
    const openModal = (data)=>{
        const pageData = {
          size:500,
          current:1
        }
        getHikAl(pageData).then((res)=>{
          if(res.code === 0){
            console.log(res.data)
            alList.value = res.data.records
          }
        })
        showChannelModal.value = true;
        selectedKeys.value = data[0].srcIndex;
        titleName.value = `添加通道算法(${data[0].name})`;
        console.log(data);


    }

    const cancel = ()=>{
      formRef.value.resetFields();
      showChannelModal.value = false;
    }

    const ok = ()=>{
      const data = {
        srcIndex: selectedKeys.value,
        logicId: formState.algorithmName,
        // namespace: formState.namespace,
        // tag: formState.tag,
        // item: formState.item,
      }
      formRef.value.validateFields().then(()=>{
        addHikChannelAl(data).then(res=>{
          if(res.code === 0){
            message.success('添加成功')
            emit('addSuccess',null)
            formRef.value.resetFields();
            showChannelModal.value = false;
          }
        })
      })

    }
    // ==================算法列表===================
    const alList = ref([]);
    // ==================表单===================
    const formRef = ref<FormInstance>();
    const formState = reactive({
      algorithmName:'',
      // namespace:'',
      // tag:'',
      // item:'',
    })

    const validatealgorithmName = async (_rule: Rule, value: string) => {
      if (!value) {
        return Promise.reject('请选择算法');
      }
    };

    const validatenamespace = async (_rule: Rule, value: string) => {
      const rsg =  /^[a-zA-Z0-9_]{1,50}$/;
      if (!value) {
        return Promise.reject('请填写命名空间');
      }

      if(!rsg.test(value)){
        return Promise.reject('只包含英文、数字和下划线且在50字符以内');
      }

    };
    const validatetag = async (_rule: Rule, value: string) => {
      const rsg =  /^[a-zA-Z0-9_]{1,50}$/;
      if (!value) {
        return Promise.reject('请填写点名');
      }

      if(!rsg.test(value)){
        return Promise.reject('只包含英文、数字和下划线且在50字符以内');
      }

    };
    const validateitem = async (_rule: Rule, value: string) => {
      const rsg =  /^[a-zA-Z0-9_]{1,50}$/;
      if (!value) {
        return Promise.reject('请填写点项');
      }

      if(!rsg.test(value)){
        return Promise.reject('只包含英文、数字和下划线且在50字符以内');
      }

    };

    const rules: Record<string, Rule[]> = {
      algorithmName: [{ required: true, validator: validatealgorithmName, trigger: 'change' }],
      // namespace: [{ required: true, validator: validatenamespace, trigger: 'change' }],
      // tag: [{ required: true, validator: validatetag, trigger: 'change' }],
      // item: [{ required: true, validator: validateitem, trigger: 'change' }],
    };
    return{
      showChannelModal,
      titleName,
      openModal,
      formState,
      alList,
      formRef,
      rules,
      cancel,
      ok
    }
  }
})


</script>
