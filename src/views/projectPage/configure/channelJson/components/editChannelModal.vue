<template>
  <div>
    <a-modal v-model:visible="showEditModal" :title="titleName" :maskClosable="false" @cancel="cancel" @ok="ok" destroyOnClose>
      <div style="margin-left: 35px;">
        <label><span style="color: red;">*</span>工程通道ID：<a-input v-model:value="recordData.channelId" style="width: 60%;" @change="showErrHandle"></a-input></label>
        <p v-if="showErr" style="height: 1px;color: red;margin-left: 85px;">工程通道ID必填</p>
        <!-- <span>工程id：</span> -->
      </div>
      <div class="alBox">
        <div v-for="(item,index) in recordData.logics" :key="index" style="margin-top: 20px;border: 1px solid #ddd;padding: 10px;">
          <a-form
            ref="formRef"
            :model="item"
            :labelCol="{ span: 6 }"
            :wrapperCol="{ span: 14 }"
          >
            <a-form-item label="算法名称" name="logicId">
              <a-select v-model:value="item.logicId">
                <a-select-option v-for="element in alList" :value="element.id">{{ element.algorithmName }}</a-select-option>
              </a-select>
            </a-form-item>
            <!-- <a-form-item label="命名空间" name="namespace">
              <a-input v-model:value="item.namespace"></a-input>
            </a-form-item>
            <a-form-item label="点名" name="tag">
              <a-input v-model:value="item.tag"></a-input>
            </a-form-item>
            <a-form-item label="点项" name="item">
              <a-input v-model:value="item.item"></a-input>
            </a-form-item> -->
          </a-form>
        </div>
      </div>



    </a-modal>
  </div>

</template>

<script lang="ts">
import { defineComponent, ref, reactive } from 'vue';
import { getHikAl, editHikChannelAlgorithm} from '@/api/project';
import { JsonParseFun, JsonStrFun } from '@/common/utils';
import { message } from 'ant-design-vue';

export default defineComponent({
  setup(props,{emit}){
    const showEditModal = ref<Boolean>(false);
    const titleName = ref<string>('');

    const recordData = ref<any>(null);

    const alList = ref([]);
    const openEditModal = (record)=>{
      const pageData = {
        size:500,
        current:1
      }
      getHikAl(pageData).then((res)=>{
        if(res.code === 0){
          alList.value = res.data.records
          showEditModal.value = true;
        }
      })
      recordData.value =  JsonParseFun(JsonStrFun(record)) ;
      titleName.value = `编辑${record.name}算法`

    }

    const showErr = ref<boolean>(false);

    const showErrHandle = ()=>{
      if(!recordData.value.channelId || recordData.value.channelId === ''){
        showErr.value = true;
      }else{
        showErr.value = false;
      }
    }



    const ok = ()=>{
      if(!recordData.value.channelId){
        showErr.value = true;
        return;
      }

      recordData.value.logics.forEach((item)=>{
        item.algorithmName = alList.value.find((element)=> element.id === item.logicId).algorithmName
      })

      editHikChannelAlgorithm(recordData.value).then((res)=>{
        if(res.code === 0){
          message.success('编辑成功');
          showEditModal.value = false;
          emit('editSuc',null)
        }
      })
    }
    const cancel = ()=>{
      showErr.value = false;
      showEditModal.value = false;
    }
    return{
      showEditModal,
      titleName,
      openEditModal,
      recordData,
      alList,
      ok,
      cancel,
      showErr,
      showErrHandle
    }
  }
})
</script>

<style scoped>
.alBox{
  max-height: 550px;
  overflow-y: auto;
}

</style>
