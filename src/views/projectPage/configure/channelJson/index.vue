<template>
  <a-tabs @change="changeTabs">
    <a-tab-pane key="1" tab="通道配置">
      <div style="display: flex; margin-bottom: 10px; gap: 8px">
        <a-button @click="addChannelHandle">增加通道算法</a-button>
        <a-button @click="delChannelHandle">删除</a-button>
        <div style="margin-left: auto; gap: 8px; display: flex">
          <a-button @click="syncChannel" type="primary">同步</a-button>
          <a-button @click="openSubModal" type="primary">订阅</a-button>
        </div>
      </div>

      <a-table
        :columns="col"
        :dataSource="channelData"
        bordered
        :rowSelection="rowConfig"
        rowKey="srcIndex"
        :pagination="hisPgination"
        @change="hisPageChange"
        :scroll="{ y: 500 }"
      >
        <template #bodyCell="{ column, record, index }">
          <template v-if="column.dataIndex === 'indexNumber'">
            <div>
              {{ index + 1 }}
            </div>
          </template>
          <template v-if="column.dataIndex === 'algorithmName'">
            <div style="height: 100%; display: flex; flex-direction: column">
              <div
                v-for="(item, index) in record.logics"
                :class="{ branchStyle: index + 1 !== record.logics.length }"
                class="allBranchStyle"
              >
                <a-tooltip>
                  <template #title>{{ item.algorithmName }}</template>
                  {{
                    item.algorithmName.length > 8
                      ? item.algorithmName.slice(0, 8) + "..."
                      : item.algorithmName
                  }}
                </a-tooltip>
              </div>
            </div>
          </template>
          <!-- <template v-if="column.dataIndex === 'namespace'">
          <div style="height: 100%;display: flex;flex-direction: column;">
            <div v-for="(item,index) in record.logics"  :class="{'branchStyle':index+1 !== record.logics.length}" class="allBranchStyle">
              <a-tooltip>
                <template #title>{{item.namespace}}</template>
                {{ item.namespace.length > 8? item.namespace.slice(0,8)+'...':item.namespace}}
              </a-tooltip>

            </div>
          </div>

        </template>
        <template v-if="column.dataIndex === 'tag'">
          <div style="height: 100%;display: flex;flex-direction: column;">
            <div v-for="(item,index) in record.logics"  :class="{'branchStyle':index+1 !== record.logics.length}" class="allBranchStyle">
              <a-tooltip>
                <template #title>{{item.tag}}</template>
                {{ item.tag.length > 12? item.tag.slice(0,12)+'...':item.tag}}
              </a-tooltip>
            </div>
          </div>

        </template>
        <template v-if="column.dataIndex === 'item'">
          <div style="height: 100%;display: flex;flex-direction: column;">
            <div v-for="(item,index) in record.logics"  :class="{'branchStyle':index+1 !== record.logics.length}" class="allBranchStyle">
              <a-tooltip>
                <template #title>{{item.item}}</template>
                {{ item.item.length > 12? item.item.slice(0,12)+'...':item.item}}
              </a-tooltip>
            </div>
          </div>

        </template> -->
          <template v-if="column.dataIndex === 'subscribe'">
            <div style="height: 100%; display: flex; flex-direction: column">
              <div
                v-for="(item, index) in record.logics"
                :class="{ branchStyle: index + 1 !== record.logics.length }"
                class="allBranchStyle"
              >
                <a-checkbox v-model:checked="item.subscribe" disabled></a-checkbox>
              </div>
            </div>
          </template>

          <template v-if="column.dataIndex === 'pointConfig'">
            <div style="height: 100%; display: flex; flex-direction: column">
              <div
                v-for="(item, index) in record.logics"
                :class="{ branchStyle: index + 1 !== record.logics.length }"
                class="allBranchStyle"
              >
                <a-button
                  type="link"
                  :disabled="record.logics.length === 0"
                  @click="openPointModal(record, item)"
                  >点项配置</a-button
                >
              </div>
            </div>
          </template>
          <template v-if="column.dataIndex === 'operation'">
            <div style="display: flex; width: 100%; height: 100%">
              <div
                style="
                  border-right: 1px solid #f0f0f0;
                  width: 50%;
                  display: flex;
                  justify-content: center;
                  flex-direction: column;
                "
              >
                <!-- <p style="align-items: center;text-align: center;"> -->
                <a-button
                  type="link"
                  @click="openEditModal(record)"
                  :disabled="record.logics.length === 0"
                  >编辑</a-button
                >
                <!-- <a @click="openEditModal(record)" :disabled="record.logics.length === 0">编辑{{ record.logics.length }}</a> -->
                <!-- </p> -->
              </div>
              <div style="width: 50%">
                <div
                  v-for="(item, index) in record.logics"
                  :class="{ branchStyle: index + 1 !== record.logics.length }"
                  class="allBranchStyle"
                >
                  <!-- <a @click="deleteChannelAlgorithm(item, record)">删除</a> -->
                  <a-button type="text" @click="deleteChannelAlgorithm(item, record)" danger
                    >删除</a-button
                  >
                </div>
              </div>
            </div>
          </template>
        </template>
      </a-table>
    </a-tab-pane>
    <a-tab-pane key="2" tab="算法规则配置" force-render>
      <algorithmRulesList ref="algorithmList"></algorithmRulesList>
    </a-tab-pane>
  </a-tabs>

  <!-- 点项编辑 -->

  <!-- 添加弹窗 -->

  <addChannelModal ref="addChannalModal" @addSuccess="addSuccess"></addChannelModal>

  <!-- 编辑弹窗 -->
  <editChannelModal ref="editChannelModal" @editSuc="editSuc"></editChannelModal>

  <!-- 点项配置弹窗 -->
  <a-modal
    v-model:visible="showPointModal"
    :title="pointConfTitle"
    :maskClosable="false"
    @ok="subPointConf()"
    @cancel="cancelPointConf"
  >
    <div>
      报警后上报ICS：<a-checkbox
        v-model:checked="pointStatusAlarm.enable"
        style="margin-bottom: 10px"
        @change="alarmStatus"
      ></a-checkbox>
      <div class="NTIBox">
        <div>报警状态</div>
        <a-form ref="pointStatusformRef" :model="pointStatusAlarm.icsAlarmModel" :rules="Rules">
          <a-form-item label="点域" name="namespace">
            <a-input
              v-model:value="pointStatusAlarm.icsAlarmModel.namespace"
              style="width: 80%"
              :disabled="!pointStatusAlarm.enable"
            ></a-input>
          </a-form-item>
          <a-form-item label="点名" name="tag">
            <a-input
              v-model:value="pointStatusAlarm.icsAlarmModel.tag"
              style="width: 80%"
              :disabled="!pointStatusAlarm.enable"
            ></a-input>
          </a-form-item>
          <a-form-item label="点项" name="item">
            <a-input
              v-model:value="pointStatusAlarm.icsAlarmModel.item"
              style="width: 80%"
              :disabled="!pointStatusAlarm.enable"
            ></a-input>
          </a-form-item>
        </a-form>
      </div>
    </div>
    <div style="margin-top: 10px">
      点项上报ICS：<a-checkbox
        v-model:checked="pointResultAlarm.enable"
        style="margin-bottom: 10px"
        @change="reportStatus"
      ></a-checkbox>
      <div class="NTIBox">
        <div>识别结果</div>
        <a-form ref="pointResultformRef" :model="pointResultAlarm.icsAlarmModel" :rules="Rules">
          <a-form-item label="点域" name="namespace">
            <a-input
              v-model:value="pointResultAlarm.icsAlarmModel.namespace"
              style="width: 80%"
              :disabled="!pointResultAlarm.enable"
            ></a-input>
          </a-form-item>
          <a-form-item label="点名" name="tag">
            <a-input
              v-model:value="pointResultAlarm.icsAlarmModel.tag"
              style="width: 80%"
              :disabled="!pointResultAlarm.enable"
            ></a-input>
          </a-form-item>
          <a-form-item label="点项" name="item">
            <a-input
              v-model:value="pointResultAlarm.icsAlarmModel.item"
              style="width: 80%"
              :disabled="!pointResultAlarm.enable"
            ></a-input>
          </a-form-item>
        </a-form>
      </div>
    </div>
  </a-modal>

  <!-- 订阅 -->
  <a-modal
    v-model:visible="showSubscribeModal"
    title="订阅"
    :maskClosable="false"
    @ok="subOk"
    destroyOnClose
  >
    <a-transfer
      v-model:target-keys="targetRightKeys"
      :data-source="subscribeData"
      :rowKey="(record) => record.algorithmCode"
      :titles="['未订阅', '已订阅']"
      style="margin-left: 40px"
    >
      <template #render="item">
        <a-tooltip placement="left">
          <template #title>{{ item.algorithmName }}</template>
          {{ item.algorithmName }}
        </a-tooltip>
      </template>
    </a-transfer>
  </a-modal>
</template>

<script lang="ts">
import { defineComponent, nextTick, ref, onMounted, reactive, computed } from "vue";
import {
  getHikChannelList,
  deleteHikChannel,
  syncHik,
  hikSubscribeList,
  hikSubscribe,
  deleteHikChannelAlgorithm,
  setPointConf,
} from "@/api/project";
import addChannelModal from "./components/addChannelModal.vue";
import editChannelModal from "./components/editChannelModal.vue";
import algorithmRulesList from "./components/algorithmRulesList.vue";
import { Modal, message } from "ant-design-vue";
import type { FormInstance } from "ant-design-vue";
import { validateNameSpace, validateItem, validateTag } from "@/common/rules";


import { cloneDeep } from "lodash";

export default defineComponent({
  components: {
    addChannelModal,
    editChannelModal,
    algorithmRulesList,
  },
  setup() {
    // ==================通道表格=======================
    const col = [
      {
        title: "序列",
        dataIndex: "indexNumber",
        align: "center",
      },
      {
        title: "通道名",
        dataIndex: "name",
        align: "center",
      },
      {
        title: "IP地址",
        dataIndex: "ip",
        align: "center",
      },
      {
        title: "资源ID",
        dataIndex: "srcIndex",
        align: "center",
      },
      {
        title: "工程通道ID",
        dataIndex: "channelId",
        align: "center",
      },
      {
        title: "工程通道名",
        dataIndex: "channelName",
        align: "center",
      },
      {
        title: "算法名称",
        dataIndex: "algorithmName",
        align: "center",
      },
      // {
      //   title:'命名空间',
      //   dataIndex:'namespace',
      //   align:'center'
      // },
      // {
      //   title:'点名',
      //   dataIndex:'tag',
      //   align:'center'
      // },
      // {
      //   title:'点项',
      //   dataIndex:'item',
      //   align:'center'
      // },
      {
        title: "是否订阅",
        dataIndex: "subscribe",
        align: "center",
      },
      {
        title: "点项配置",
        dataIndex: "pointConfig",
        align: "center",
      },
      {
        title: "操作",
        dataIndex: "operation",
        align: "center",
        width: "200px",
      },
    ];

    const channelData = ref([]);

    const hisListParams = reactive({
      size: 10,
      current: 1,
      total: 0,
    });

    const hisPgination = computed(() => ({
      total: hisListParams.total,
      current: hisListParams.current, //
      pageSize: hisListParams.size,
      showTotal: () => `共 ${hisListParams.total} 条`,
      showLessItems: true,
      defaultPageSize: hisListParams.size,
      showSizeChanger: false,
    }));
    const hisPageChange = ({ current, total }) => {
      hisListParams.current = current;
      gethikList();
      // setCurrentPage(current);
    };

    const gethikList = () => {
      const sizeData = {
        size: hisPgination.value.pageSize,
        current: hisPgination.value.current,
      };
      getHikChannelList(sizeData).then((res) => {
        if (res.code === 0) {
          channelData.value = res.data.records;
          hisListParams.total = res.data.total;
        }
      });
    };

    //==========================列表选择======================================

    const selectedKey = ref<string[]>([]);
    const selectedRow = ref<any[]>([]);
    const rowConfig = reactive({
      type: "radio",
      selectedRowKeys: selectedKey,
      onChange: (keys, rows) => {
        selectedKey.value = keys;
        selectedRow.value = rows;
      },
    });

    // =========================添加弹窗======================================

    const addChannalModal = ref(null);

    const addChannelHandle = () => {
      if (selectedRow.value.length === 0) {
        message.warn("请选择一个通道以进行添加", 2);
        return;
      }
      addChannalModal.value.openModal(selectedRow.value);
    };

    const addSuccess = (val) => {
      gethikList();
    };

    // =========================删除通道==================================

    const delChannelHandle = () => {
      if (selectedKey.value.length !== 0) {
        Modal.confirm({
          title: "删除",
          content: `确定删除【${selectedRow.value[0].name}】通道吗？`,
          onOk: () => {
            deleteHikChannel(selectedKey.value).then((res) => {
              if (res.code === 0) {
                message.success("删除成功");
                gethikList();
                selectedKey.value = null;
                selectedRow.value = null;
              }
            });
          },
        });
      } else {
        message.warn("请选择一个通道", 2);
      }
    };

    //=================编辑通道中的算法=================

    const editChannelModal = ref(null);

    const openEditModal = (record) => {
      editChannelModal.value.openEditModal(record);
    };

    const editSuc = () => {
      gethikList();
    };

    //=================删除通道中的算法=================

    const deleteChannelAlgorithm = (item, record) => {
      if (item.algorithmName !== null) {
        Modal.confirm({
          title: "删除",
          content: `确定删除【${record.name}】的【${item.algorithmName}】算法吗？`,
          onOk: () => {
            const params = {
              id: item.id,
              srcIndex: record.srcIndex,
            };
            deleteHikChannelAlgorithm(params).then((res) => {
              if (res.code === 0) {
                message.success(`删除成功`);
                gethikList();
              }
            });
          },
        });
      } else {
        message.warn("该通道未配置算法");
      }
    };

    //==================点项配置======================
    const showPointModal = ref<boolean>(false);
    const pointConfTitle = ref<string>();

    const pointsId = ref<string>();
    const pointStatusAlarm = ref({
      enable: false,
      icsAlarmModel: {
        namespace: null,
        item: null,
        tag: null,
      },
    });
    const pointResultAlarm = ref({
      enable: false,
      icsAlarmModel: {
        namespace: null,
        item: null,
        tag: null,
      },
    });
    const openPointModal = (record, item) => {
      showPointModal.value = true;
      pointsId.value = item.id;
      pointConfTitle.value = `${record.name}_${item.algorithmName}_点项配置`;
      pointStatusAlarm.value = cloneDeep(item.pointStatusAlarm);
      pointResultAlarm.value = cloneDeep(item.pointResultAlarm);
    };

    const alarmStatus = () => {
      if (!pointStatusAlarm.value.enable) {
        pointStatusformRef.value.clearValidate();
      }
    };

    const reportStatus = () => {
      if (!pointResultAlarm.value.enable) {
        pointResultformRef.value.clearValidate();
      }
    };

    const subPointConf = async () => {
      if (pointResultAlarm.value.enable) {
        await pointResultformRef.value.validateFields();
      }
      if (pointStatusAlarm.value.enable) {
        await pointStatusformRef.value.validateFields();
      }

      const data = {
        id: pointsId.value,
        pointResultAlarm: pointResultAlarm.value,
        pointStatusAlarm: pointStatusAlarm.value,
      };
      setPointConf(data).then((res) => {
        if (res.code === 0) {
          message.success("更新成功");
          gethikList();
          showPointModal.value = false;
          cancelPointConf();
        }
      });
    };
    const cancelPointConf = () => {
      pointStatusformRef.value.clearValidate();
      pointResultformRef.value.clearValidate();
      pointStatusAlarm.value = {
        enable: false,
        icsAlarmModel: {
          namespace: null,
          item: null,
          tag: null,
        },
      };
      pointResultAlarm.value = {
        enable: false,
        icsAlarmModel: {
          namespace: null,
          item: null,
          tag: null,
        },
      };
    };
    // 表单的ref
    const pointStatusformRef = ref<FormInstance>(null);
    const pointResultformRef = ref<FormInstance>(null);
    // 校验规则
    const Rules = {
      namespace: [{ validator: validateNameSpace, trigger: "change" }],
      tag: [{ validator: validateTag, trigger: "change" }],
      item: [{ validator: validateItem, trigger: "change" }],
    };

    //==================同步==========================
    const syncChannel = () => {
      syncHik().then((res) => {
        if (res.code === 0) {
          message.success("更新成功");
          gethikList();
        }
      });
    };

    //=================订阅===========================
    const showSubscribeModal = ref<boolean>(false); // 订阅modal控制

    const targetRightKeys = ref(); // 已订阅
    const subscribeData = ref<any[]>([]); // 全部订阅

    const openSubModal = () => {
      hikSubscribeList().then((res) => {
        if (res.code === 0) {
          if (res.data.subscribes !== null && res.data.notSubscribes !== null) {
            showSubscribeModal.value = true;
            subscribeData.value = res.data.subscribes.concat(res.data.notSubscribes);
            targetRightKeys.value = res.data.subscribes.map((item) => {
              return item.algorithmCode;
            });
          }
        }
      });
    };

    const subOk = () => {
      hikSubscribe(targetRightKeys.value).then((res) => {
        if (res.code === 0) {
          message.success("操作成功");
          showSubscribeModal.value = false;
          gethikList();
        }
      });
    };
    //============切换页签===============
    const algorithmList = ref(null);
    const changeTabs = (key) => {
      if (key === "1") {
        gethikList();
        algorithmList.value.resetData();
      }
      if (key === "2") {
        selectedKey.value = [];
        selectedRow.value = [];
        algorithmList.value.getHikAlList();
      }
    };

    onMounted(() => {
      gethikList();
    });

    return {
      col,
      channelData,
      hisPgination,
      hisPageChange,
      rowConfig,
      addChannalModal,
      addChannelHandle,
      addSuccess,
      delChannelHandle,
      editChannelModal,
      editSuc,
      openEditModal,
      deleteChannelAlgorithm,
      syncChannel,
      showSubscribeModal,
      targetRightKeys,
      openSubModal,
      subscribeData,
      subOk,
      algorithmList,
      changeTabs,
      // 点项配置
      showPointModal,
      openPointModal,
      pointStatusAlarm,
      pointResultAlarm,
      pointConfTitle,
      subPointConf,
      cancelPointConf,
      Rules,
      pointStatusformRef,
      pointResultformRef,
      alarmStatus,
      reportStatus,
    };
  },
});
</script>

<style scoped lang="scss">
.branchStyle {
  border-bottom: 1px solid #f0f0f0;
}

.allBranchStyle {
  padding: 10px;
  // min-height: 44px;
  flex: 1;
  align-items: center;
}

:deep(.ant-table-tbody > tr > td) {
  padding: 0px 0px !important;
}

.NTIBox {
  padding: 10px;
  border: 1px solid #ddd;
  display: flex;
  flex-direction: column;
  gap: 10px;
}
</style>
