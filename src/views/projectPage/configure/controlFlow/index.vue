<template>
  <div>
    <iframe class="frameStyle" :src="url" @load="onLoad" title="ctl-flow-container"></iframe>
  </div>
</template>
<script lang="ts">
import { defineComponent, onMounted, ref } from "vue";
import { useStore } from "vuex";

export default defineComponent({
  setup() {
    const url = ref((window as any).g.nodered_url);
    const store = useStore();
    // store.commit('onLoading', '加载中...');
    const onLoad = () => {
      store.commit("disLoading");
    };

    // const getUrl = ()=>{
    //   getControlFlow().then(res=>{
    //     if(res.code === 0){
    //       url.value = res.data
    //     }
    //   })
    // }
    onMounted(() => {
      // getUrl();
    });
    return {
      url,
      onLoad,
    };
  },
});
</script>
<style lang="less" scoped>
.frameStyle {
  width: 100%;
  height: calc(100vh - 178px);
  border: none;
}
</style>
