<template>
  <div class="box">
    <a-spin :spinning="uploadingSpin" :tip="uploadingTips">
      <a-row :gutter="16">
        <a-col :span="4">
          <a-card class="leftTree" title="分类列表" :bodyStyle="{ padding: '0px' }">
            <a-list size="small" :dataSource="classifyList">
              <template #renderItem="{ item }">
                <a-list-item
                  :style="{
                    background: item.id === currentClassifyId ? '#cddcef' : '',
                    cursor: 'pointer',
                  }"
                  @click="getCurrentClassify(item)"
                >
                  <div v-if="item.id !== 'end'" class="endClassify">
                    {{ item.name }}({{ item.label }})
                  </div>
                  <div v-else>{{ item.name }}{{ item.label }}</div>
                  <div style="justify-content: flex-end">
                    <a-button
                      size="small"
                      shape="circle"
                      v-if="item.id === 'end'"
                      @click="addClassify"
                    >
                      <PlusOutlined></PlusOutlined>
                    </a-button>
                    <a-button size="small" shape="circle" v-else>
                      <MinusOutlined @click.stop="deleteClassify(item)"></MinusOutlined>
                    </a-button>
                  </div>
                </a-list-item>
              </template>
            </a-list>
            <addClassify ref="addClassifyModal" @addClassify="addClassifyCallBack"></addClassify>
          </a-card>
        </a-col>
        <a-col :span="20">
          <a-card
            class="rightTable"
            :title="currentClassifyName"
            :bodyStyle="{ 'padding-top': '10px' }"
          >
            <div class="imageBox" v-if="classifyList.length !== 1">
              <a-image
                v-for="(item, index) in imageList"
                :src="item.displayPics"
                style="padding: 10px 10px; width: 320px !important; height: 180px !important"
              >
                <template #previewMask>
                  <a style="font-size: 16px; font-weight: 600" v-if="!item.isError">查看</a>
                  <div style="width: 20px; text-align: center" v-if="!item.isError">|</div>
                  <!-- <div v-if="item.isError" style="font-size:16px;">错误图片请<a style="color:red;font-weight: 600;"
                      @click.stop="delectPic(item)"><u>删除</u></a></div> -->
                  <a
                    style="color: red; font-size: 16px; font-weight: 600"
                    @click.stop="delectPic(item)"
                    v-if="!item.isError"
                    >删除</a
                  >
                </template>
              </a-image>
              <div style="display: inline-block; margin-left: 10px; vertical-align: bottom">
                <a-upload
                  :beforeUpload="uploadImg.beforeUpload"
                  list-type="picture-card"
                  :maxCount="10"
                  :multiple="true"
                  :showUploadList="false"
                >
                  <div>
                    <plus-outlined></plus-outlined>
                    <div class="ant-upload-text">上传</div>
                  </div>
                </a-upload>
              </div>
            </div>

            <div v-else>
              <a-Empty></a-Empty>
            </div>
          </a-card>
        </a-col>
      </a-row>
    </a-spin>
  </div>
</template>

<script lang="ts">
import { computed, defineComponent, onMounted, reactive, ref, watch } from "vue";
import { Modal, message } from "ant-design-vue";
import { QuestionCircleOutlined, PlusOutlined, MinusOutlined } from "@ant-design/icons-vue";
import addClassify from "./components/addClassify.vue";
import {
  getAllClassifyList,
  addClassifyList,
  deleteClassifyList,
  uploadImgToClassify,
  deleteImage,
} from "@/api/project";
import { errorImg } from "@/common/utils";

interface ClassifyType {
  id: string;
  name: string;
  label: string;
  pics: any[];
}
export default defineComponent({
  components: {
    PlusOutlined,
    MinusOutlined,
    addClassify,
  },
  setup(props) {
    // 任务列表
    const classifyList = ref<ClassifyType[]>([]);

    const currentClassifyId = ref<string>("");
    const currentClassifyName = ref<string>("");

    const getAllClassify = (currentId) => {
      uploadingSpin.value = true;
      getAllClassifyList().then((res) => {
        uploadingSpin.value = false;
        if (res.code === 0 && res.data.length !== 0) {
          classifyList.value = res.data.map((item) => {
            return {
              id: item.id,
              name: item.name,
              pics: item.items,
              label: item.label,
            };
          });
          classifyList.value.push({
            id: "end",
            name: "",
            label: "",
            pics: [""],
          });

          if (!currentId) {
            currentClassifyId.value = classifyList.value[0].id;
            currentClassifyName.value = `${classifyList.value[0].name} (${classifyList.value[0].pics.length})`;
            // imageList.value = classifyList.value[0].pics;
            errorPicProcess(classifyList.value[0].pics);
          } else {
            currentClassifyId.value = currentId;
            const picList = classifyList.value.find(
              (item) => item.id === currentClassifyId.value
            ).pics;
            const classifyName = classifyList.value.find(
              (item) => item.id === currentClassifyId.value
            ).name;
            currentClassifyName.value = `${classifyName} (${picList.length})`;
            errorPicProcess(picList);
          }
        }
        if (res.code === 0 && res.data.length === 0) {
          classifyList.value = [
            {
              id: "end",
              name: "",
              label: "",
              pics: [],
            },
          ];
          currentClassifyId.value = null;
          currentClassifyName.value = "";
        }
      });
    };
    // 处理容错图片
    const errorPicProcess = (picsList) => {
      const promises = [];
      picsList.forEach((item) => {
        promises.push(
          new Promise((resolve, reject) => {
            const img = new Image();
            img.src = item;
            img.onload = () => {
              resolve({
                isError: false,
                realPics: item,
                displayPics: item,
              });
            };
            img.onerror = () => {
              reject({
                isError: true,
                realPics: item,
                displayPics: errorImg,
              });
            };
          })
        );
      });
      // console.log(promises)
      Promise.allSettled(promises).then((res) => {
        imageList.value = res.map((item) => {
          if (item.status === "fulfilled") {
            return item.value;
          }
          if (item.status === "rejected") {
            return item.reason;
          }
        });
      });
    };

    // 选择分类
    const getCurrentClassify = (classifyItem) => {
      if (classifyItem.id === "end") return;
      currentClassifyId.value = classifyItem.id;
      currentClassifyName.value = `${classifyItem.name} (${classifyItem.pics.length})`;
      errorPicProcess(classifyItem.pics);
    };

    // 添加分类
    const addClassifyModal = ref(null);
    const addClassify = () => {
      addClassifyModal.value.addModal();
    };
    const addClassifyCallBack = (res) => {
      getAllClassify(res.id);
    };
    // 删除分类
    const deleteClassify = (item) => {
      Modal.confirm({
        title: "删除分类",
        content: "确认删除该分类吗？",
        onOk() {
          uploadingTips.value = "分类正在删除中，请稍候";
          uploadingSpin.value = true;
          deleteClassifyList(item.id).then((res) => {
            uploadingSpin.value = false;
            if (res.code === 0) {
              message.success("删除成功");
              if (item.id === currentClassifyId.value) {
                getAllClassify(null);
              } else {
                getAllClassify(currentClassifyId.value);
              }

              // currentClassifyId.value = classifyList.value[0].id
            }
          });
        },
      });
    };

    // 当前分类的图片列表
    const imageList = ref<any[]>([]);
    // 上传图片
    const imgUploadList = ref<any[]>();

    // 上传loading
    const uploadingSpin = ref<boolean>(false);

    const uploadingTips = ref<string>("");

    const uploadImg = {
      beforeUpload(file, fileList) {
        if (fileList.length > 64) {
          message.warn("上传文件数量最多为64");
          return false;
        }
        const fileSuffix = fileList.map((item) => item.type);
        const fileSize = fileList.map((item) => item.size);
        if (fileSize.findIndex((item) => item > 1024 * 1024) >= 0) {
          message.warn("文件上传大小不能超过1M", 2);
          return false;
        }
        if (
          fileSuffix.findIndex(
            (item) => !["image/png", "image/jpeg", "image/bmp"].includes(item)
          ) >= 0
        ) {
          message.warn("文件上传类型仅支持 png,jpg,jpeg,bmp", 2);
          return false;
        }
        imgUploadList.value = fileList;
        return false;
      },
    };

    function upload(fileArray: File[]) {
      const formData = new FormData();
      fileArray.forEach((item) => {
        formData.append("images", item);
      });
      formData.append("classifyConfigId", currentClassifyId.value);
      uploadImgToClassify(formData).then((res) => {
        if (res.code === 0) {
          message.success("添加成功");
          getAllClassify(currentClassifyId.value);
        }
        uploadingSpin.value = false;
        imgUploadList.value = [];
      });
    }

    watch(
      () => imgUploadList.value,
      (nv) => {
        if (!nv.length) return;
        uploadingTips.value = "图片正在上传和特征提取中，请稍后";
        uploadingSpin.value = true;
        upload(nv);
      }
    );

    const delectPic = (item) => {
      if (item.displayPics.includes(errorImg)) {
        const data = {
          id: currentClassifyId.value,
          url: item.realPics,
        };
        deleteImage(data).then((res) => {
          if (res.code === 0) {
            message.success("删除成功");
            getAllClassify(currentClassifyId.value);
          }
        });
        return;
      }

      Modal.confirm({
        title: "删除图片",
        content: "确认删除该图片吗？",
        onOk() {
          const data = {
            id: currentClassifyId.value,
            url: item.realPics,
          };
          uploadingTips.value = "图片正在删除中，请稍候";
          uploadingSpin.value = true;
          deleteImage(data).then((res) => {
            uploadingSpin.value = false;
            if (res.code === 0) {
              message.success("删除成功");
              getAllClassify(currentClassifyId.value);
            }
          });
        },
      });
    };

    onMounted(() => {
      getAllClassify(null);
    });
    return {
      classifyList,
      currentClassifyId,
      currentClassifyName,
      delectPic,
      imageList,
      imgUploadList,
      uploadImg,
      addClassifyModal,
      addClassify,
      addClassifyCallBack,
      errorImg,
      getCurrentClassify,
      deleteClassify,
      uploadingSpin,
      uploadingTips,
    };
  },
});
</script>

<style scoped>
.box {
  width: 98%;
  margin-left: 1%;
}

.leftTree {
  width: 100%;
  height: 790px;
  overflow-y: auto;
}

.rightTable {
  width: 100%;
  height: 790px;
}

.imageBox {
  max-height: 700px;
  overflow: auto;
}

.imageBox::-webkit-scrollbar {
  width: 5px;
}

.imageBox::-webkit-scrollbar-thumb {
  border-radius: 10px;
  background: rgba(0, 0, 0, 0.2);
}

.imageBox::-webkit-scrollbar-track {
  border-radius: 0;
  background: rgba(0, 0, 0, 0.1);
}

.picUpload {
  display: inline;
  width: 300px;
  height: 150px;
  text-align: center;
  border: 1px dashed #ccc;
}

.endClassify {
  overflow-wrap: break-word;
  width: 200px;
}
</style>
