<template>
  <div>
    <a-modal
      v-model:visible="modal"
      title="添加自定义分类"
      :maskClosable="false"
      @ok="handleOk"
      :afterClose="afterClose"
    >
      <div>
        <label><span style="color:red">*</span>分类名称：</label>
        <a-input style="width:60%" v-model:value="classifyName" @change="verificationName('name')"></a-input>
        <div v-if="showError" class="message">
          <span>名称不能为空，不能包含特殊字符，20个字符以内</span>
        </div>
      </div>
      <div style="margin-top: 10px;">
        <label><span style="color:red">*</span>分类标签：</label>
        <a-input style="width:60%" v-model:value="classifyLabel" @change="verificationName('label')"></a-input>
        <div v-if="showLabelError" class="message">
          <span>标签只包含大小写字母、下划线、中划线、数字，20个字符以内</span>
        </div>
      </div>
      <!-- <div style="margin-top: 10px;">
        <label><span style="color:#fff">*</span>分类图片：</label>
        <a-upload
          v-model:file-list="fileList"
          :max-count="10"
          :beforeUpload="beforeUpload"
          accept=".jpg, .jpeg, .png"
          @remove="remove"
          :multiple="true"
        >
          <a-button>
            <UploadOutlined></UploadOutlined>
            上传
          </a-button>
        </a-upload>
      </div> -->
    </a-modal>
  </div>
</template>

<script lang="ts">
import { message } from 'ant-design-vue';
import { ExclamationCircleOutlined, UploadOutlined } from '@ant-design/icons-vue';
import { defineComponent,ref } from 'vue';
import { addClassifyList } from '@/api/project';
import { validateName } from '@/common/rules';

  export default defineComponent({
    components:{
      UploadOutlined
    },
    setup(props,{emit}){
      const modal = ref<boolean>(false)
      const addModal = ()=>{
        modal.value = true
      }

      // name
      const classifyName = ref<string>('');
      // label
      const classifyLabel = ref<string>('');

      const showError = ref<boolean>(false);

      const showLabelError = ref<boolean>(false);

      const verificationName = (type) => {
        const myregName = /^(?![!@#$%^&*()+=\[\]{}\\|;:'",.<>?`~])[\w\u4e00-\u9fa5]{1,20}$/;
        const myregLabel = /^[a-zA-Z0-9\-_]{1,20}$/
        if(type === 'name'){
          if (
            classifyName.value === ''
            || classifyName.value === null
            || !myregName.test(classifyName.value)
          ) {
            showError.value = true;
            return true;
          } else {
            showError.value = false;
            return false;
          }
        }
        if(type === 'label'){
          if (
            classifyLabel.value === ''
            || classifyLabel.value === null
            || !myregLabel.test(classifyLabel.value)
          ) {
            showLabelError.value = true;
            return true;
          } else {
            showLabelError.value = false;
            return false;
          }
        }
        
      };

      // 上传
      const fileList = ref<File[]>([]);

      const beforeUpload = (img,imglist)=>{
        const fileSuffix = img.name.split('.').pop()
        if(!['png','jpg','jpeg','bmp'].includes(fileSuffix)){
          message.warn('文件上传类型仅支持 png,jpg,jpeg,bmp',2);
          return false;
        }
        fileList.value = imglist;
        return false;
      }

      const remove = (img)=>{

        fileList.value = fileList.value.filter((item)=> img.lastModified !== item.lastModified)
        console.log('img',img, 'fileList',fileList.value)
      }


      const afterClose = ()=>{
        fileList.value = [];
        classifyName.value = '';
        classifyLabel.value = '';
        showError.value = false;
        showLabelError.value = false;
      }


      const handleOk = ()=>{
        // if(showError.value || showLabelError.value) return;
        if(verificationName('name')){
          return
        }
        if(verificationName('label')){
          return
        }
        const data = {
          name:classifyName.value,
          label:classifyLabel.value
        }
        addClassifyList(data).then(res=>{
          if(res.code === 0){
            message.success('添加成功');
            emit('addClassify', res.data);
            modal.value = false;
          }
        })

      }



      return{
        addModal,
        modal,
        fileList,
        beforeUpload,
        remove,
        classifyName,
        classifyLabel,
        showError,
        showLabelError,
        verificationName,
        handleOk,
        afterClose,
      }
    }
  })
</script>

<style scoped>
.message {
  margin-left: 70px;
  margin-top: 5px;
  color: #ff4d4f;
  font-size: 14px;
}
</style>
