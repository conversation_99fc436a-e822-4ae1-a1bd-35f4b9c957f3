<template>
  <el-config-provider :locale="zhCn">
    <div style="display: flex">
      <div class="timeBox">
        <a-form :model="timeFormState" autocomplete="off">
          <fieldset>
            <legend>系统时间配置</legend>
            <a-form-item label="系统时间" name="systemTime">
              <a-input
                v-model:value="timeFormState.systemTime"
                readOnly
                style="width: 200px"
              ></a-input>
            </a-form-item>
            <div style="display: flex">
              <a-form-item label="设置时间" name="systemTime">
                <!-- <a-date-picker
                  v-model:value="timeFormState.settingTime"
                  show-time
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                ></a-date-picker> -->
                <el-date-picker
                  v-model="timeFormState.settingTime"
                  type="datetime"
                  format="YYYY-MM-DD HH:mm:ss"
                  value-format="YYYY-MM-DD HH:mm:ss"
                  placeholder="请选择日期"
                  size="small"
                >
                </el-date-picker>
              </a-form-item>
              <a-button @click="getComputerTime" style="margin-left: 10px; margin-top: 4px"
                >获取计算机时间</a-button
              >
              <a-button
                @click="handleSetting"
                style="margin-left: 10px; margin-top: 4px"
                type="primary"
                >设置</a-button
              >
            </div>
          </fieldset>
        </a-form>
        <a-form
          :model="NTPFormState"
          autocomplete="off"
          ref="NPTFormRef"
          :rules="NPTrules"
          :label-col="{ style: { width: '100px' } }"
        >
          <fieldset>
            <legend>NTP校时</legend>
            <a-form-item label="使能开关" name="available">
              <a-switch v-model:checked="NTPFormState.available"></a-switch>
            </a-form-item>
            <a-form-item label="服务地址" name="ntpServerHost">
              <a-input
                v-model:value="NTPFormState.ntpServerHost"
                style="width: 200px"
                placeholder="请输入ip"
                :readonly="!NTPFormState.available"
              ></a-input>
            </a-form-item>
            <a-form-item label="NTP端口" name="ntpServerPort">
              <a-input
                v-model:value="NTPFormState.ntpServerPort"
                style="width: 200px"
                placeholder="123"
                :readonly="!NTPFormState.available"
              ></a-input>
            </a-form-item>
            <a-form-item label="校时时间间隔" name="interval">
              <a-input-number
                v-model:value="NTPFormState.interval"
                style="width: 200px"
                defaultValue="10"
                addon-after="分钟"
                :min="1"
                :max="59"
                :precision="0"
                :readonly="!NTPFormState.available"
              ></a-input-number>
            </a-form-item>
            <a-form-item v-if="route.name !== 'systemPreset'" :wrapper-col="buttonItemLayout.wrapperCol">
              <a-button type="primary" @click="setNTPtiming">提交</a-button>
            </a-form-item>
          </fieldset>
        </a-form>
      </div>
    </div>
  </el-config-provider>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, ref, computed, defineExpose, watch } from "vue";
import type { Rule } from "ant-design-vue/es/form";
import type { FormInstance } from "ant-design-vue";
import { message } from "ant-design-vue";
import { getSystemTime, setComputerTime, getNPT, setNPT } from "@/api/project";
import { zhCn } from "element-plus/es/locale";
import { useRoute } from "vue-router";
import { ElConfigProvider, ElDatePicker } from "element-plus";

interface TimeFormType {
  systemTime: string;
  settingTime: string;
}
interface NTPFormType {
  id: string;
  available: boolean;
  ntpServerHost: string;
  ntpServerPort: string;
  interval: number;
  layout: "horizontal";
}
export default defineComponent({
  components: {
    ElConfigProvider,
    ElDatePicker,
  },
  setup() {
    const route = useRoute();
    const timeFormState = reactive<TimeFormType>({
      systemTime: "",
      settingTime: "",
    });
    
    // 获取计算机时间
    const getComputerTime = () => {
      // getSystemTime().then((ress) => {
      //   if (ress.code === 0) {
      //     timeFormState.settingTime = ress.data;
      //   }
      // });

      // const currentDateTime = dayjs();
      // const formattedDateTime = currentDateTime.format("YYYY-MM-DD HH:mm:ss");
      const now = new Date();

      const year = now.getFullYear();
      const month = String(now.getMonth() + 1).padStart(2, "0"); // 月份补零
      const day = String(now.getDate()).padStart(2, "0"); // 日期补零
      const hours = String(now.getHours()).padStart(2, "0"); // 小时补零
      const minutes = String(now.getMinutes()).padStart(2, "0"); // 分钟补零
      const seconds = String(now.getSeconds()).padStart(2, "0"); // 秒数补零

      const formattedTime = `${year}-${month}-${day} ${hours}:${minutes}:${seconds}`;
      timeFormState.settingTime = formattedTime;
    };
    // 设置系统时间
    const handleSetting = () => {
      if (timeFormState.settingTime) {
        setComputerTime({ dateStr: timeFormState.settingTime }).then((res) => {
          if (res.code === 0) {
            message.success("操作成功");
            getSystemTime().then((ress) => {
              if (ress.code === 0) {
                timeFormState.systemTime = ress.data;
              }
            });
          }
        });
      }
    };
    // NTP
    const NPTFormRef = ref<FormInstance>();
    const NTPFormState = reactive<NTPFormType>({
      id: "",
      available: false,
      ntpServerHost: "",
      ntpServerPort: "",
      interval: null,
      layout: "horizontal",
    });

    watch(
        () => NTPFormState.available,
        (val) => {
            if (!val) {
                NPTFormRef.value.resetFields() // 重置表单
            }
        }
    );
    const setNTPtiming = () => {
      NPTFormRef.value.validateFields().then(() => {
        setNPT(NTPFormState).then((res) => {
          if (res.code === 0) {
            message.success("操作成功");
          }
        });
      });
    };

    const buttonItemLayout = computed(() => {
      const { layout } = NTPFormState;
      return layout === "horizontal"
        ? {
            wrapperCol: { span: 1 },
          }
        : {};
    });
    // NPT校验
    const validateHost = async (_rule: Rule, value: string) => {
      const ipReg =
        /^(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])(\.(25[0-5]|2[0-4][0-9]|1[0-9]{2}|[1-9]?[0-9])){3}$/;
        // 使能开关关闭则跳过校验
        if (!NTPFormState.available) {
          return Promise.resolve();
        }
      if (value === "") {
        // eslint-disable-next-line prefer-promise-reject-errors
        return Promise.reject("服务地址不能为空");
      }
      if (!ipReg.test(value)) {
        // eslint-disable-next-line prefer-promise-reject-errors
        return Promise.reject("请输入正确的服务地址");
      }
      return Promise.resolve();
    };
    const validatePort = async (_rule: Rule, value: string) => {
      const portRegex =
        /^(6553[0-5]|655[0-2][0-9]|66[0-4][0-9]{2}|65[0-5][0-9]{3}|6[0-4][0-9]{4}|[1-5]?[0-9]{1,4})$/;
        // 使能开关关闭则跳过校验
        if (!NTPFormState.available) {
          return Promise.resolve();
        }
      if (value === "") {
        // eslint-disable-next-line prefer-promise-reject-errors
        return Promise.reject("NTP端口不能为空");
      }

      if (!portRegex.test(value)) {
        // eslint-disable-next-line prefer-promise-reject-errors
        return Promise.reject("请输入正确的端口号");
      }
      return Promise.resolve();
    };

    const NPTrules = computed(() => {
        const required = NTPFormState.available;
        return {
            ntpServerHost: [{ required, validator: validateHost, trigger: "change" }],
            ntpServerPort: [{ required, validator: validatePort, trigger: "change" }],
            interval: [{ required, trigger: "change" }],
        };
    });

    // 时间初始化
    const getTimeData = () => {
      getSystemTime().then((res) => {
        if (res.code === 0) {
          timeFormState.systemTime = res.data;
        }
      });
      getNPT().then((res) => {
        if (res.code === 0) {
          NTPFormState.id = res.data.id;
          NTPFormState.available = res.data.available;
          NTPFormState.ntpServerHost = res.data.ntpServerHost;
          NTPFormState.ntpServerPort = res.data.ntpServerPort;
          NTPFormState.interval = res.data.interval;
        }
      });
    };

    
    // 暴露表单校验validate方法
    const validate = () => {
      return NPTFormRef.value.validateFields();
    };
    defineExpose({ validate, NTPFormState });

    onMounted(() => {
      getTimeData();
    });

    return {
      zhCn,
      handleSetting,
      timeFormState,
      getComputerTime,
      NTPFormState,
      NPTFormRef,
      setNTPtiming,
      NPTrules,
      buttonItemLayout,
      validate,
      route
    };
  },
});
</script>

<style scoped>
.titleStyle {
  font-size: 16px;
  font-weight: 600;
}

.timeBox {
  margin-left: auto;
  margin-right: auto;
  border: 1px solid #ddd;
  padding: 20px;
}
</style>
