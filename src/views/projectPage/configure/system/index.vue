<template>
    <div>
        <a-tabs>
            <a-tab-pane key="service" tab="数据底座配置">
                <databaseConfig />
            </a-tab-pane>
            <a-tab-pane key="backup" tab="备份策略配置">
                <backupConfig />
            </a-tab-pane>
            <a-tab-pane key="time" tab="时间配置">
                <TimeConfig></TimeConfig>
            </a-tab-pane>
            <a-tab-pane key="screenshot" >
                <template #tab>
                    <span>
                        截图配置
                        <a-tooltip placement="bottom">
                            <template #title>
                            <span>用于配置通道定时、定点、定量截图任务</span>
                            </template>
                            <question-circle-outlined/>
                        </a-tooltip> 
                    </span>
   
                </template>
                <ScreenshotConfig></ScreenshotConfig>
            </a-tab-pane>
            <!-- <a-tab-pane key="notice" tab="通知配置">
                <NoticeConfig></NoticeConfig>
            </a-tab-pane> -->

            <a-tab-pane key="overall" tab="系统参数配置">
                <OverallSys></OverallSys>
            </a-tab-pane>
        </a-tabs>
    </div>
</template>

<script lang="ts">
import { defineComponent , ref} from 'vue';
import TimeConfig from './time/timeConf.vue';
import ScreenshotConfig from './PTZ/PTZConf.vue';
import NoticeConfig from './notice/noticeConf.vue';
import databaseConfig from './database/databaseConfig.vue';
import backupConfig from './backup/backupConfig.vue';
import { QuestionCircleOutlined} from '@ant-design/icons-vue';
import OverallSys from './overallSystem/overallSys.vue';


export default defineComponent({
    components:{
    TimeConfig,
    ScreenshotConfig,
    NoticeConfig,
    QuestionCircleOutlined,
    OverallSys,
    databaseConfig,
    backupConfig
},
    setup(){
        return{

        }
    }
})
</script>

<style scoped>

</style>