<template>
  <el-config-provider>
    <div style="display: flex">
      <div class="box">
        <a-form
          :model="backupFormState"
          autocomplete="off"
          ref="backupFormRef"
          :rules="rules"
          :label-col="{ style: { width: '110px' } }"
        >
            <a-form-item label="最大备份存储" name="maxBackupSize">
              <a-input-number
                v-model:value="backupFormState.maxBackupSize"
                style="width: 200px"
                addon-after="GB"
                :controls="false"
                :min="256"
                :max="1024"
                :precision="1"
              ></a-input-number>
            </a-form-item>

          <fieldset>
            <legend>执行记录</legend>
            <a-form-item label="最大备份条数" name="executeRecordOutOfSize">
              <a-input-number
                v-model:value="backupFormState.executeRecordOutOfSize"
                style="width: 200px"
                :min="5000"
                :max="10000"
                :precision="0"
                :controls="false"
              ></a-input-number>
            </a-form-item>
            <a-form-item label="单页备份条数" name="executeRecordBackupItemSize">
              <a-input-number
                v-model:value="backupFormState.executeRecordBackupItemSize"
                style="width: 200px"
                :min="5000"
                :max="10000"
                :precision="0"
                :controls="false"
              ></a-input-number>
            </a-form-item>

            <legend>操作日志</legend>
            <a-form-item label="最大备份条数" name="operationLogRecordOutOfSize">
              <a-input-number
                v-model:value="backupFormState.operationLogRecordOutOfSize"
                style="width: 200px"
                :min="25000"
                :max="50000"
                :precision="0"
                :controls="false"
              ></a-input-number>
            </a-form-item>
            <a-form-item label="单页备份条数" name="operationLogRecordBackupItemSize">
              <a-input-number
                v-model:value="backupFormState.operationLogRecordBackupItemSize"
                style="width: 200px"
                :min="5000"
                :max="10000"
                :precision="0"
                :controls="false"
              ></a-input-number>
            </a-form-item>

            <legend>报警记录</legend>
            <a-form-item label="最大备份条数" name="alarmRecordOutOfSize">
              <a-input-number
                v-model:value="backupFormState.alarmRecordOutOfSize"
                style="width: 200px"
                :min="25000"
                :max="50000"
                :precision="0"
                :controls="false"
              ></a-input-number>
            </a-form-item>
            <a-form-item label="单页备份条数" name="alarmRecordBackupItemSize">
              <a-input-number
                v-model:value="backupFormState.alarmRecordBackupItemSize"
                style="width: 200px"
                :min="5000"
                :max="10000"
                :precision="0"
                :controls="false"
              ></a-input-number>
            </a-form-item>
            <a-form-item v-if="route.name !== 'systemPreset'">
              <a-button type="primary" @click="setBackupConfig">提交</a-button>
            </a-form-item>
          </fieldset>
        </a-form>
      </div>
    </div>
  </el-config-provider>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, ref, defineExpose } from "vue";
import type { FormInstance } from "ant-design-vue";
import { message } from "ant-design-vue";
import { getOverConf, saveOverConf } from "@/api/project";
import { ElConfigProvider } from "element-plus";
import { useRoute } from "vue-router";
export default defineComponent({
  components: {
    ElConfigProvider,
  },
  setup() {
    const route = useRoute();
    const backupFormState = reactive({
      maxBackupSize: 0, // 最大备份文件大小
      executeRecordOutOfSize: 0, // 执行记录最大备份条数
      executeRecordBackupItemSize: 0, // 执行记录备份单文件条数
      operationLogRecordOutOfSize: 0, // 操作日志最大备份条数
      operationLogRecordBackupItemSize: 0, // 操作日志备份单文件条数
      alarmRecordOutOfSize: 0, // 报警记录最大备份条数
      alarmRecordBackupItemSize: 0, // 报警记录备份单文件条数
    });

    const backupFormRef = ref<FormInstance>();
    const setBackupConfig = () => {
      backupFormRef.value.validateFields().then(() => {
        const data = getBackupSaveParmas();
        saveOverConf(data).then((res) => {
          if (res.code === 0) {
            message.success("更新成功");
          }
        });   
      });
    }

    // 构建保存参数
    const getBackupSaveParmas = () => {
      return {
        systemBackupConfig: {
          maxBackupSize: backupFormState.maxBackupSize,
          executeRecord: {
            outOfSize: backupFormState.executeRecordOutOfSize,
            backupItemSize: backupFormState.executeRecordBackupItemSize,
          },
          operationLogRecord: {
            outOfSize: backupFormState.operationLogRecordOutOfSize,
            backupItemSize: backupFormState.operationLogRecordBackupItemSize,
          },
          alarmRecord: {
            outOfSize: backupFormState.alarmRecordOutOfSize,
            backupItemSize: backupFormState.alarmRecordBackupItemSize,
          },
        },
      };
    };

    const rules = {
      maxBackupSize: [{ required: true, message: "最大备份存储不能为空", trigger: "change" }],
      executeRecordOutOfSize: [{ required: true, message: "最大备份条数不能为空", trigger: "change" }],
      executeRecordBackupItemSize: [{ required: true, message: "单页备份条数不能为空", trigger: "change" }],
      operationLogRecordOutOfSize: [{ required: true, message: "最大备份条数不能为空", trigger: "change" }],
      operationLogRecordBackupItemSize: [{ required: true, message: "单页备份条数不能为空", trigger: "change" }],
      alarmRecordOutOfSize: [{ required: true, message: "最大备份条数不能为空", trigger: "change" }],
      alarmRecordBackupItemSize: [{ required: true, message: "单页备份条数不能为空", trigger: "change" }],
    };

    // 暴露表单校验validate方法
    const validate = () => {
      return backupFormRef.value.validateFields();
    };

    defineExpose({ validate, getBackupSaveParmas });

    onMounted(() => {
        getOverConf().then((res) => {
            if (res.code === 0) {
            const data = res.data.systemBackupConfig;
            backupFormState.maxBackupSize = data.maxBackupSize;
            backupFormState.executeRecordOutOfSize = data.executeRecord.outOfSize;
            backupFormState.executeRecordBackupItemSize = data.executeRecord.backupItemSize;
            backupFormState.operationLogRecordOutOfSize = data.operationLogRecord.outOfSize;
            backupFormState.operationLogRecordBackupItemSize = data.operationLogRecord.backupItemSize;
            backupFormState.alarmRecordOutOfSize = data.alarmRecord.outOfSize;
            backupFormState.alarmRecordBackupItemSize = data.alarmRecord.backupItemSize;
            }
        });
    });

    return {
      backupFormRef,
      backupFormState,
      setBackupConfig,
      rules,
      validate,
      getBackupSaveParmas,
      route
    };
  },
});
</script>

<style scoped>


.box {
  margin-left: auto;
  margin-right: auto;
  border: 1px solid #ddd;
  padding: 20px;
}
</style>
