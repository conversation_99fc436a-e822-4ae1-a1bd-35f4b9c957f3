<template>
  <div class="flexBox">
    <div class="configBox">
      <a-form :model="processFormState" autocomplete="off" ref="processForm" :rules="processRule" >
        <a-row :gutter="24">
          <a-col :span="14">
            <fieldset>
              <legend>执行过程配置</legend>
              <!-- <a-form-item label="日志打印" name="logPrint">
                <a-radio-group v-model:value="processFormState.logPrint">
                  <a-radio :value="true">开启</a-radio>
                  <a-radio :value="false">关闭</a-radio>
                </a-radio-group>
              </a-form-item>
              <a-form-item label="临时图片删除" name="TempImageDel">
                <a-radio-group v-model:value="processFormState.TempImageDel">
                  <a-radio :value="true">开启</a-radio>
                  <a-radio :value="false">关闭</a-radio>
                </a-radio-group>
              </a-form-item> -->
              <a-form-item label="展示执行记录" name="isRecordShow">
                <a-radio-group v-model:value="processFormState.isRecordShow">
                  <a-radio :value="true">开启</a-radio>
                  <a-radio :value="false">关闭</a-radio>
                </a-radio-group>
              </a-form-item>
            </fieldset>
            <fieldset>
              <legend>DCS报警列表配置</legend>
              <a-form-item label="已确认状态颜色" name="confirmColor">
                <ElColorPicker
                  v-model="processFormState.confirmColor"
                  color-format="rgb"
                ></ElColorPicker>
              </a-form-item>
              <a-form-item label="未确认状态颜色1" name="unconfirmColor1">
                <ElColorPicker
                  v-model="processFormState.unconfirmColor1"
                  color-format="rgb"
                ></ElColorPicker>
              </a-form-item>
              <a-form-item label="未确认状态颜色2" name="unconfirmColor2">
                <ElColorPicker
                  v-model="processFormState.unconfirmColor2"
                  color-format="rgb"
                ></ElColorPicker>
              </a-form-item>
            </fieldset>
            <legend>日志配置</legend>
            <a-form-item label="日志级别" name="loggingLevel">
               <a-select
                v-model:value="processFormState.loggingLevel"
                style="width: 130px"
                placeholder="请选择日志级别"
              >
                <a-select-option v-for="level in logLevelOptions" :key="level" :value="level">
                  {{ level }}
                </a-select-option>
              </a-select>
            </a-form-item>
          </a-col>
          <a-col :span="10">
            <fieldset>
              <legend>OSD配置</legend>
              <a-form-item label="报警OSD颜色" name="colorWarn">
                <ElColorPicker
                  v-model="processFormState.colorWarn"
                  color-format="rgb"
                ></ElColorPicker>
              </a-form-item>
              <a-form-item label="正常OSD颜色" name="colorNormal">
                <ElColorPicker
                  v-model="processFormState.colorNormal"
                  color-format="rgb"
                ></ElColorPicker>
              </a-form-item>
              <a-form-item label="字体颜色" name="fontColor">
                <ElColorPicker
                  v-model="processFormState.fontColor"
                  color-format="rgb"
                ></ElColorPicker>
              </a-form-item>
              <a-form-item label="是否开启字体背景" name="isfontBgColor">
                <a-switch
                  v-model:checked="processFormState.isfontBgColor"
                  checked-children="开启"
                  un-checked-children="关闭"
                ></a-switch>
              </a-form-item>
              <a-form-item label="字体背景颜色" name="fontBgColor">
                <ElColorPicker
                  v-model="processFormState.fontBgColor"
                  :disabled="!processFormState.isfontBgColor"
                  color-format="rgb"
                ></ElColorPicker>
              </a-form-item>
              <a-form-item label="字体大小" name="fontSize">
                <a-input-number
                  v-model:value="processFormState.fontSize"
                  :min="1"
                  :max="50"
                  :precision="0"
                  :controls="false"
                ></a-input-number>
              </a-form-item>

              <a-form-item label="字体粗细" name="fontThickness">
                <a-input-number
                  v-model:value="processFormState.fontThickness"
                  :min="1"
                  :max="10"
                  :precision="0"
                  :controls="false"
                ></a-input-number>
              </a-form-item>
              <a-form-item label="OSD持续时长" name="fontThickness">
                <a-input-number
                  v-model:value="processFormState.webOsdTimeout"
                  :min="0"
                  :precision="0"
                  style="width: 80px"
                  :controls="false"
                >
                  <template #addonAfter>秒</template>
                </a-input-number>
              </a-form-item>
            </fieldset>
          </a-col>
        </a-row>
        <a-row>
          <a-form-item v-if="route.name !== 'systemPreset'" :wrapper-col="buttonItemLayout.wrapperCol">
            <a-button type="primary" @click="submitConf">提交</a-button>
          </a-form-item>
        </a-row>
      </a-form>
    </div>
  </div>
</template>

<script lang="ts">
import { defineComponent, ref, reactive, computed, onMounted, defineExpose } from "vue";
import { getOverConf, saveOverConf } from "@/api/project";
import { FormInstance } from "ant-design-vue";
import { ElColorPicker } from "element-plus";
import { message } from "ant-design-vue";
import { validateColor } from "@/common/rules";
import { useRoute } from "vue-router";
interface ProcessFormType {
  logPrint: boolean;
  TempImageDel: boolean;
  isRecordShow:boolean;
  confirmColor: string;
  unconfirmColor1: string;
  unconfirmColor2: string;
  colorWarn: string;
  colorNormal: string;
  fontColor: string;
  isfontBgColor: boolean;
  fontBgColor: string;
  fontSize: string | number;
  fontThickness: string | number;
  webOsdTimeout: string | number;
  loggingLevel: string;
  layout: "horizontal";
}

interface DCSColor {
  confirmColor: string;
  unconfirmColor1: string;
  unconfirmColor2: string;
}

export default defineComponent({
  components: {
    ElColorPicker,
  },
  setup() {
    const route = useRoute();
    function rgbStringToArray(rgbString) {
      // 去掉rgb字符串中的括号和rgb前缀
      let cleanString = rgbString.replace(/[^\d,]/g, "");
      // 将字符串按逗号分割，并转换为数字数组
      let rgbArray = cleanString.split(",").map(Number);
      return rgbArray;
    }

    //============================================执行过程配置================================================
    const processForm = ref<FormInstance>();
    const processFormState = reactive<ProcessFormType>({
      logPrint: true,
      TempImageDel: true,
      isRecordShow:false,
      confirmColor: "",
      unconfirmColor1: "",
      unconfirmColor2: "",
      colorWarn: "",
      colorNormal: null,
      fontColor: null,
      isfontBgColor: false,
      fontBgColor: null,
      fontSize: 30,
      fontThickness: 3,
      webOsdTimeout: 10,
      loggingLevel: "INFO",
      layout: "horizontal",
    });

    const processRule = {
      confirmColor: [{ required: true, validator: validateColor, trigger: "change" }],
      unconfirmColor1: [{ required: true, validator: validateColor, trigger: "change" }],
      unconfirmColor2: [{ required: true, validator: validateColor, trigger: "change" }],
      colorWarn: [{ required: true, validator: validateColor, trigger: "change" }],
      colorNormal: [{ required: true, validator: validateColor, trigger: "change" }],
      fontColor: [{ required: true, validator: validateColor, trigger: "change" }],
      fontBgColor: [{ required: true, validator: validateColor, trigger: "change" }],
    };

    const buttonItemLayout = computed(() => {
      const { layout } = processFormState;
      return layout === "horizontal"
        ? {
            wrapperCol: { span: 1 },
          }
        : {};
    });

    const submitConf = async () => {
      const validator = await processForm.value?.validateFields();
      if(!validator) return;
      const data = getOverallSaveParmas();
      saveOverConf(data).then((res) => {
        if (res.code === 0) {
          message.success("更新成功");
        }
      });
    };

    const logLevelOptions = ref([
      "ERROR",
      "WARN",
      "INFO",
      "DEBUG",
    ]);

    // 构建保存参数
    const getOverallSaveParmas = () => {
      return  {
        executeLogEnable: processFormState.logPrint,
        executeImgDelEnable: processFormState.TempImageDel,
        executeRecordShowable:processFormState.isRecordShow,
        fontBgEnable: processFormState.isfontBgColor,
        loggingLevel: processFormState.loggingLevel,
        dcsAlarmStateColor: {
          confirmed: rgbStringToArray(processFormState.confirmColor),
          unconfirmed1: rgbStringToArray(processFormState.unconfirmColor1),
          unconfirmed2: rgbStringToArray(processFormState.unconfirmColor2),
        },
        osdConfig: {
          colorWarn: rgbStringToArray(processFormState.colorWarn),
          colorNormal: rgbStringToArray(processFormState.colorNormal),
          fontColor: rgbStringToArray(processFormState.fontColor),
          fontBgColor: rgbStringToArray(processFormState.fontBgColor),
          fontSize: processFormState.fontSize,
          fontThickness: processFormState.fontThickness,
          webOsdTimeout: processFormState.webOsdTimeout,
        },
      }
    }

    // 暴露表单校验validate方法
    const validate = () => {
      return processForm.value.validateFields();
    };
    defineExpose({ validate, getOverallSaveParmas });

    onMounted(() => {
      getOverConf().then((res) => {
        if (res.code === 0) {
          const osdConfig = res.data.osdConfig;
          const dcsAlarmStateColor = res.data.dcsAlarmStateColor;
          processFormState.logPrint = res.data.executeLogEnable;
          processFormState.isRecordShow = res.data.executeRecordShowable;
          processFormState.TempImageDel = res.data.executeImgDelEnable;
          processFormState.confirmColor = `rgb(${dcsAlarmStateColor.confirmed[0]}, ${dcsAlarmStateColor.confirmed[1]}, ${dcsAlarmStateColor.confirmed[2]})`;
          processFormState.unconfirmColor1 = `rgb(${dcsAlarmStateColor.unconfirmed1[0]}, ${dcsAlarmStateColor.unconfirmed1[1]}, ${dcsAlarmStateColor.unconfirmed1[2]})`;
          processFormState.unconfirmColor2 = `rgb(${dcsAlarmStateColor.unconfirmed2[0]}, ${dcsAlarmStateColor.unconfirmed2[1]}, ${dcsAlarmStateColor.unconfirmed2[2]})`;
          processFormState.colorWarn = `rgb(${osdConfig.colorWarn[0]}, ${osdConfig.colorWarn[1]}, ${osdConfig.colorWarn[2]})`;
          processFormState.colorNormal = `rgb(${osdConfig.colorNormal[0]}, ${osdConfig.colorNormal[1]}, ${osdConfig.colorNormal[2]})`;
          processFormState.fontColor = `rgb(${osdConfig.fontColor[0]}, ${osdConfig.fontColor[1]}, ${osdConfig.fontColor[2]})`;
          processFormState.fontBgColor = `rgb(${osdConfig.fontBgColor[0]}, ${osdConfig.fontBgColor[1]}, ${osdConfig.fontBgColor[2]})`;
          processFormState.fontSize = osdConfig.fontSize;
          processFormState.fontBgColor = `rgb(${osdConfig.fontBgColor[0]}, ${osdConfig.fontBgColor[1]}, ${osdConfig.fontBgColor[2]})`;
          processFormState.fontSize = res.data.osdConfig.fontSize;
          processFormState.fontThickness = res.data.osdConfig.fontThickness;
          processFormState.webOsdTimeout = res.data.osdConfig.webOsdTimeout;
          processFormState.isfontBgColor = res.data.fontBgEnable;
          processFormState.loggingLevel = res.data.loggingLevel;
        }
      });
    });

    return {
      processFormState,
      buttonItemLayout,
      submitConf,
      processRule,
      processForm,
      validate,
      getOverallSaveParmas,
      route,
      logLevelOptions
    };
  },
});
</script>

<style scoped>
.flexBox {
  display: flex;
}

.configBox {
  margin-left: auto;
  margin-right: auto;
  border: 1px solid #ddd;
  padding: 20px;
}

.titleStyle {
  font-size: 16px;
  font-weight: 600;
}
</style>
