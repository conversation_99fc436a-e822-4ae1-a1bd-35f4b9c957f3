<template>
  <div>
    <!-- -----------------------------------------搜索条件----------------------------------- -->
    <div class="btnBox">
      <a-space>
        <a-button style="align-self: flex-start" @click="addVariable">添加</a-button>
        <a-input
          v-model:value="searchConfig.valName"
          style="width: 250px"
          placeholder="中文名/变量名/关联点项名"
          allow-clear
          size="small"
        ></a-input>
        <a-button type="primary" @click="searchTable">
          <template #icon>
            <SearchOutlined />
          </template>
          搜索
        </a-button>
        <a-select
          v-model:value="searchConfig.variableType"
          style="width: 100px"
          @change="changeVariableType"
        >
          <a-select-option value="">全部</a-select-option>
          <a-select-option value="READ">读点</a-select-option>
          <a-select-option value="WRITE">写点</a-select-option>
        </a-select>
      </a-space>
    </div>
    <!-- -----------------------------------------变量列表--------------------------------- -->
    <a-table
      :columns="columns"
      :dataSource="dataSource"
      bordered
      :pagination="pagination"
      @change="pageChange"
      rowKey="id"
      :scroll="{ y: 620 }"
    >
      <template #bodyCell="{ column, record }">
        <!-- ----------------------------------变量类型-------------------------------------- -->
        <template v-if="column.dataIndex === 'valType'">
          <a-tag :color="record.valType === 'WRITE' ? 'yellow' : 'blue'">{{
            record.valType === "WRITE" ? "写点" : "读点"
          }}</a-tag>
        </template>
        <!-- ---------------------------------- 数据类型 ------------------------------------- -->
        <template v-if="column.dataIndex === 'dataType'">
          <span>{{ dataTypeList[record.dataType] }}</span>
        </template>

        <!-- ---------------------------------- 操作 -------------------------------------------->
        <template v-if="column.dataIndex === 'operation'">
          <a-space>
            <a-button type="primary" @click="handleEdit(record)">变量配置</a-button>
            <a-button
              type="primary"
              @click="handleOutputConfig(record)"
              :disabled="record.valType === 'READ'"
              >输出配置</a-button
            >
            <a-button @click="handleDel(record)" type="primary" danger>删除</a-button>
          </a-space>
        </template>

        <!-- ----------------------------------质量位----------------------------------------- -->

        <template v-if="column.dataIndex === 'quality'">
          <span v-if="isEmpty(record.quality)"></span>
          <a-tag v-else :color="getColor(record.quality)">{{ record.quality }}</a-tag>
        </template>
        <!-- ----------------------------------列表描述--------------------------------------- -->
        <template v-if="column.dataIndex === 'description'">
          <a-tooltip>
            <template #title>{{ record.description }}</template>
            <span v-if="record.description === null"></span>
            <span v-else>{{ record.description }}</span>
          </a-tooltip>
        </template>
      </template>
    </a-table>
    <!-- -----------------------------------添加变量弹窗---------------------------- -->
    <AddVariableDialog ref="addVariableDialog" @add-success="addfresh"></AddVariableDialog>
    <!-- -----------------------------------输出配置弹窗---------------------------- -->
    <outputConfigModal ref="outputConfigModal" @updateOutputConfig="addfresh"></outputConfigModal>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, ref, computed, onBeforeUnmount } from "vue";
import { Modal, message } from "ant-design-vue";
import type { ColumnsType } from "ant-design-vue/es/table/interface";
import { SearchOutlined } from "@ant-design/icons-vue";
import { changeVariable, deleteVariable, pointList, unBinding } from "@/api/project";
import AddVariableDialog from "./components/AddVariableDialog.vue";
import outputConfigModal from "./components/outputConfigModal.vue";
import { isEmpty } from "@/common/utils";

import SetInterval from "@/common/SetInterval";

export default defineComponent({
  components: {
    SearchOutlined,
    AddVariableDialog,
    outputConfigModal,
  },

  setup() {
    // ----------------------------------------------------------表格数据------------------------------------------
    const columns: ColumnsType = [
      {
        title: "中文名",
        dataIndex: "nickName",
        width: 180,
      },
      {
        title: "变量名",
        dataIndex: "valName",
        width: 150,
      },

      {
        title: "描述",
        dataIndex: "description",
        width: 200,
      },
      {
        title: "单位",
        dataIndex: "unit",
        width: 100,
      },
      {
        title: "数据类型",
        dataIndex: "dataType",
        width: 100,
      },
      {
        title: "变量类型",
        dataIndex: "valType",
        width: 100,
      },
      {
        title: "绑定算法输出名",
        dataIndex: "bindOutputName",
        width: 200,
      },
      {
        title: "关联点项名",
        dataIndex: "pointName",
        width: 150,
      },
      {
        title: "值更新时间",
        dataIndex: "variableTime",
        width: 200,
      },
      {
        title: "变量值",
        dataIndex: "value",
        width: 100,
      },
      {
        title: "质量位",
        dataIndex: "quality",
        width: 150,
      },
      {
        title: "操作",
        dataIndex: "operation",
        width: 250,
      },
    ];

    const dataSource = ref([]);
    // -------------------------------------------------------------- 分页 ------------------------------------------
    const tableData = reactive({
      current: 1,
      pageSize: 10,
      total: 0,
      currentPageSize: 0, // 当前页面有几条数据
      tableList: [],
    });
    const pagination = computed(() => ({
      total: tableData.total,
      current: tableData.current, //
      pageSize: 10,
      showTotal: () => `共 ${tableData.total} 条`,
      showLessItems: true,
      defaultPageSize: tableData.pageSize,
      showSizeChanger: false,
    }));
    const pageChange = ({ current, total }) => {
      tableData.current = current;
      getList();
      // setCurrentPage(current);
    };

    // 添加刷新
    const addfresh = (val) => {
      getList();
    };

    // -------------------------------------------------变量配置----------------------------------------------
    const handleEdit = (record) => {
      addVariableDialog.value.showModal(record);
    };
    const handleDel = (record) => {
      Modal.confirm({
        title: "提示",
        content: "是否确认删除该项？",
        onOk() {
          deleteVariable(record.id).then((res) => {
            if (res.code === 0) {
              message.success("删除成功");
              getList();
            }
          });
        },
      });
    };

    // ----------------------------------------------------获取列表------------------------------------------
    const getList = async () => {
      const pageConfig = {
        size: pagination.value.pageSize,
        current: pagination.value.current,
      };
      const searchdata = {
        searchStr: encodeURIComponent(searchConfig.lastSearchVal),
        variableType: searchConfig.variableType,
      };
      const data = Object.assign(pageConfig, searchdata);

      const res = await pointList(data); // 获取列表数据
      if (res.code !== 0) return;
      const newTotalPages = Math.ceil(res.data.total / pagination.value.pageSize);
      if (pagination.value.current > newTotalPages && newTotalPages !== 0) {
        pagination.value.current = Math.max(newTotalPages, 1);
        getList();
        return;
      }
      dataSource.value = res.data.records;
      tableData.total = res.data.total;
      tableData.current = pageConfig.current;
    };
    // -----------------------------------------------搜索---------------------------------------------
    const searchConfig = reactive({
      variableType: "",
      valName: "",
      lastSearchVal: "",
    });
    // 条件搜索
    const searchTable = () => {
      searchConfig.lastSearchVal = searchConfig.valName;
      tableData.current = 1;
      getList();
    };

    const changeVariableType = () => {
      tableData.current = 1;
      getList();
    };

    // -----------------------------------------------添加变量-----------------------------------------------
    const addVariableDialog = ref(null);
    const addVariable = () => {
      addVariableDialog.value.showModal();
    };

    // -----------------------------------------------输出配置-------------------------------------------------
    const outputConfigModal = ref(null);
    const handleOutputConfig = (record) => {
      outputConfigModal.value.showModal(record);
    };

    //------------------------------------------------数据类型--------------------------------------------------
    const dataTypeList = reactive({
      INTEGER: "整数",
      FLOAT: "浮点数",
      STRING: "字符串",
      BOOLEAN: "布尔值",
    });

    // -----------------------------------------------质量位颜色-----------------------------------------------
    const getColor = (quality) => {
      switch (quality) {
        case "Good":
          return "green";
        case "Bad_Timeout":
          return "red";
        case "Bad_OutOfService":
          return "red";
        case "Bad":
          return "red";
        case "Uncertain":
          return "grey";
        default:
          return "";
      }
    };
    // -------------------------------------------------定时刷新---------------------------------------------
    const addTimer = async () => {
      await SetInterval.add("valTimer", getList, 3000, true);
      await SetInterval.run("valTimer");
    };

    onMounted(() => {
      addTimer();
    });

    onBeforeUnmount(() => {
      SetInterval.close("valTimer");
    });

    return {
      columns,
      dataSource,
      pagination,
      pageChange,
      searchConfig,
      addfresh,
      handleEdit,
      handleDel,
      searchTable,
      changeVariableType,
      addVariableDialog,
      addVariable,
      outputConfigModal,
      handleOutputConfig,
      dataTypeList,
      getColor,
      isEmpty,
    };
  },
});
</script>

<style lang="less" scoped>
.btnBox {
  align-items: center;
  margin-bottom: 10px;
}
</style>
