<template>
  <a-modal
    title="输出配置"
    :visible="visible"
    :width="600"
    :mask-closable="false"
    :destroy-on-close="true"
    @ok="handleOk"
    @cancel="handleCancel"
  >
    <div class="treeBox">
      <a-dropdown :trigger="['click']">
        <a-input-search
          v-model:value="searchValue"
          style="margin-bottom: 8px"
          placeholder="输出节点"
          @change="searchTree"
        ></a-input-search>
        <template #overlay>
          <a-menu v-if="searchList.length">
            <a-menu-item v-for="item in searchList" :key="item.key" @click="handleSelect(item)">
              {{ item.label }}
            </a-menu-item>
          </a-menu>
          <a-empty :imageStyle="{ 'margin-left': '180px' }" description="" v-else></a-empty>
        </template>
      </a-dropdown>

      <a-skeleton :loading="treeData.length === 0 && skeletonLoading" active></a-skeleton>
      <a-empty v-if="treeData.length === 0 && !skeletonLoading"></a-empty>
      <div class="treeContainer">
        <a-tree
          style="text-align: left; padding-bottom: 50px"
          :tree-data="treeData"
          block-node
          defaultExpandAll
          v-model:checkedKeys="checkedKey"
          @check="handleCheck"
          checkable
          :checkStrictly="true"
          class="projectTree"
          :fieldNames="{ key: 'key', title: 'label' }"
          v-if="treeData.length"
          :selectable="false"
        >
          <template #title="{ data, key, label }">
            <a-tooltip placement="right">
              <template #title>
                {{ data.label }}
              </template>
              <span :ref="(el) => (nodeRefs[key] = el)" :class="{ highlight: searchKey === key }">
                <IconFont :type="IconList[data.type]" />{{
                  data.isBind && data.id !== null ? `${data.label}(已绑定)` : data.label
                }}</span
              >
            </a-tooltip>
          </template>
        </a-tree>
      </div>
    </div>
  </a-modal>
</template>

<script setup lang="ts">
import { ref, onMounted, nextTick } from "vue";
import { getBindTree, bindOutputParam, unbindOutputParam } from "@/api/project";
import { message } from "ant-design-vue";
import { isEmpty } from "@/common/utils";
import IconFont from "@/components/c-iconfont";
import { IconList } from "@/components/datatypeComponent";

// -------------------------------------弹窗控制-----------------------------------

const selectedRecord = ref<any>({}); // 选中的记录

const visible = ref(false); // 弹窗是否可见

const handleCancel = () => {
  checkedKey.value = [];
  tempCheckedKeys.value = [];
  selectedRecord.value = {};
  searchValue.value = "";
  searchList.value = [];
  searchKey.value = "";
  nodeRefs.value = {};
  visible.value = false;
};

const handleOk = async () => {
  const data = {
    id: selectedRecord.value.id,
    outputId: checkedKey.value.length > 0 ? checkedKey.value[0].split("-")[1] : "",
    algorithmInstanceId: checkedKey.value.length > 0 ? checkedKey.value[0].split("-")[0] : "",
  };
  const res = isEmpty(data.outputId) ? await unbindOutputParam(data) : await bindOutputParam(data);
  if (res.code === 0) {
    message.success("操作成功");
    emit("updateOutputConfig");
    visible.value = false;
    checkedKey.value = [];
    tempCheckedKeys.value = [];
    selectedRecord.value = {};
  }
};

const showModal = (record: any) => {
  visible.value = true;
  selectedRecord.value = record;
  getTreeData();
};

defineExpose({
  showModal,
});

const emit = defineEmits(["updateOutputConfig"]);
// -------------------------------------算法树-----------------------------

const treeData = ref<any[]>([]); // 树结构数据

const checkedKey = ref<string[]>([]); // 选中的节点

const tempCheckedKeys = ref<string[]>([]); // 临时选中的节点

// 勾选节点
const handleCheck = (checkedKeys, { checked, checkedNodes, node, event }) => {
  if (node.type !== "output-param") {
    checkedKey.value = tempCheckedKeys.value;
    return;
  }
  if (checkedKeys.checked.length === 0) {
    checkedKey.value = [];
    tempCheckedKeys.value = [];
    return;
  }
  checkedKey.value = [checkedKeys.checked[checkedKeys.checked.length - 1]];
  tempCheckedKeys.value = checkedKey.value;
};

const searchValue = ref<string>(""); // 搜索框的值

const skeletonLoading = ref(true); // 骨架屏loading

// 获取树结构数据

function addSelectableProperty(nodes) {
  nodes.forEach((node) => {
    if (node.type !== "output-param") {
      node.disabled = true;
    }
    if (node.type === "output-param" && node.id && node.parentId) {
      node.key = `${node.parentId}-${node.id}`;
    }
    if (node.isBind) {
      // node.disabled = true;

      if (
        node.key !== `${selectedRecord.value.algorithmInstanceId}-${selectedRecord.value.outputId}`
      ) {
        node.disabled = true;
      } else {
        checkedKey.value.push(
          `${selectedRecord.value.algorithmInstanceId}-${selectedRecord.value.outputId}`
        );
      }
    }

    if (node.children && node.children.length > 0) {
      addSelectableProperty(node.children);
    }
  });
}
const getTreeData = () => {
  skeletonLoading.value = true;
  treeData.value = [];
  getBindTree().then((res) => {
    skeletonLoading.value = false;
    if (res.code === 0 && res.data.length) {
      treeData.value = res.data;
      addSelectableProperty(treeData.value);
    }
  });
};

// ----------------------------------------------搜索树------------------------------------------------
function searchOutputParams(tree, keyword) {
  const results = [];

  if (keyword === "") {
    return [];
  }

  function traverse(node, parent, grandparent) {
    // 匹配output-param节点且包含关键字
    if (node.type === "output-param" && node.label.toUpperCase().includes(keyword.toUpperCase())) {
      // 构建层级标签
      const labels = [];
      if (grandparent) labels.push(grandparent.label);
      if (parent) labels.push(parent.label);
      labels.push(node.label);

      // 生成格式化结果
      results.push({
        key: node.key,
        label: labels.join("-"),
      });
    }

    // 递归遍历子节点，维护父级关系
    if (node.children) {
      node.children.forEach((child) => {
        // 当前节点成为父级，原父级成为祖父级
        traverse(child, node, parent);
      });
    }
  }

  // 从根节点开始遍历
  tree.forEach((root) => traverse(root, null, null));

  return results;
}

const searchList = ref<any[]>([]); // 搜索结果列表
// 搜索树
const searchTree = () => {
  searchList.value = searchOutputParams(treeData.value, searchValue.value);
  searchKey.value = "";
};

// 滚动到选中节点
const scrollToNode = () => {
  if (searchKey.value && nodeRefs.value[searchKey.value]) {
    const node = nodeRefs.value[searchKey.value];
    node.scrollIntoView({ behavior: "smooth", block: "center" });
  }
};
// 选中搜索结果
const handleSelect = async (item) => {
  searchKey.value = item.key;
  await nextTick();
  scrollToNode();
};

const nodeRefs = ref({}); // 节点引用

const searchKey = ref(""); // 搜索关键字
</script>

<style scoped lang="less">
.treeContainer {
  height: 580px;
  overflow-y: auto;
}
.treeBox {
  width: 100%;
  border: 1px solid #d9d9d9;
  padding: 5px;
  height: 620px;
  overflow-y: hidden;
}

.highlight {
  background-color: #fffb8f;
  box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
  border-radius: 3px;
}
</style>
