<template>
  <div>
    <a-modal
      :title="modalType ? '编辑变量' : '新增变量'"
      :visible="visible"
      @ok="handleOk"
      @cancel="handleCancel"
      :maskClosable="false"
    >
      <a-form
        :model="variable"
        :rules="rules"
        ref="variableFormRef"
        :label-col="labelCol"
        :wrapper-col="wrapperCol"
      >
        <a-form-item label="中文名" name="nickName">
          <a-input
            v-model:value="variable.nickName"
            placeholder="请输入中文名称"
            @change="handleChineseNameChange"
          />
        </a-form-item>
        <a-form-item label="变量名称" name="valName">
          <a-input v-model:value="variable.valName" placeholder="请输入变量名称" />
        </a-form-item>
        <a-form-item label="变量描述" name="description">
          <a-textarea
            v-model:value="variable.description"
            maxlength="255"
            placeholder="请输入变量描述"
            showCount
            :autoSize="{ minRows: 2, maxRows: 5 }"
          />
        </a-form-item>
        <a-form-item label="单位" name="unit">
          <a-input v-model:value="variable.unit" placeholder="请输入单位" :maxlength="10" />
        </a-form-item>
        <a-form-item label="数据类型" name="dataType">
          <a-select v-model:value="variable.dataType">
            <a-select-option value="INTEGER">整型</a-select-option>
            <a-select-option value="FLOAT">浮点型</a-select-option>
            <a-select-option value="STRING">字符串</a-select-option>
            <a-select-option value="BOOLEAN">布尔型</a-select-option>
          </a-select>
        </a-form-item>
        <a-form-item label="变量类型" name="varType">
          <a-select v-model:value="variable.valType" :disabled="modalType">
            <a-select-option value="READ">读点</a-select-option>
            <a-select-option value="WRITE">写点</a-select-option>
          </a-select>
        </a-form-item>
        <div class="freshTimeClass" v-if="variable.valType === 'READ'">
          <a-form-item label="值刷新方式" name="valueReadType" :label-col="{ span: 58 }">
            <a-select v-model:value="variable.valueReadType">
              <a-select-option value="REAL_TIME_QUERY">实时</a-select-option>
              <a-select-option value="LOOP_INTERVAL_QUERY">定时</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item
            name="valueReadInterval"
            :label-col="{ span: 58 }"
            v-if="variable.valueReadType === 'LOOP_INTERVAL_QUERY'"
          >
            <a-select v-model:value="variable.valueReadInterval" style="width: 70px">
              <a-select-option value="1">1秒</a-select-option>
              <a-select-option value="3">3秒</a-select-option>
              <a-select-option value="5">5秒</a-select-option>
              <a-select-option value="10">10秒</a-select-option>
            </a-select>
          </a-form-item>
        </div>
        <a-form-item label="关联点项名" name="pointName">
          <a-input v-model:value="variable.pointName" placeholder="请输入关联点项名" />
        </a-form-item>

        <div class="freshTimeClass" style="margin-left: 5px">
          <a-form-item label="质量位刷新方式" name="qualitySetType" :label-col="{ span: 10.5 }">
            <a-select v-model:value="variable.qualitySetType" style="width: 150px">
              <a-select-option value="SET_BY_VALUE_REFRESH">随点项/值刷新</a-select-option>
              <a-select-option value="TIMEOUT_SET_BAD">超时自动置坏</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item name="qualityTimeout" v-if="variable.qualitySetType === 'TIMEOUT_SET_BAD'">
            <a-input-number
              v-model:value="variable.qualityTimeout"
              style="width: 159px"
              placeholder="超时时间"
              :min="1"
              :max="60"
              :precision="0"
            >
              <template #addonAfter>
                <a-select v-model:value="variable.qualityTimeoutUnit" style="width: 60px">
                  <a-select-option value="SECONDS">秒</a-select-option>
                  <a-select-option value="MINUTES">分</a-select-option>
                  <a-select-option value="HOURS">时</a-select-option>
                </a-select>
              </template>
            </a-input-number>
          </a-form-item>
        </div>
        <a-form-item label="质量位关联点项名" name="qualityPointName">
          <a-input v-model:value="variable.qualityPointName" placeholder="请输入质量位关联点项名" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>

<script lang="ts" setup>
interface FormRules {
  id?: number | string;
  nickName?: string;
  valName?: string;
  description?: string;
  unit?: string;
  dataType?: "INTEGER" | "STRING" | "FLOAT" | "BOOLEAN"; // 假设 dataType 是这些类型之一
  valType?: "READ" | "WRITE"; // 假设 valType 是这些类型之一
  pointName?: string;
  valueReadType?: "REAL_TIME_QUERY" | "LOOP_INTERVAL_QUERY";
  valueReadInterval?: string;
  qualitySetType?: "SET_BY_VALUE_REFRESH" | "TIMEOUT_SET_BAD";
  qualityTimeout?: string;
  qualityPointName?: string;
  qualityTimeoutUnit: string;
}

import { defineComponent, ref } from "vue";
import { message, type FormInstance } from "ant-design-vue";
import { validateChinese, validateDomainTagItem, validateValName } from "@/common/rules";
import { pinyin } from "pinyin-pro";
import { isEmpty } from "@/common/utils";
import { changeVariable } from "@/api/project";

// ------------------------------------------弹窗控制--------------------------------

const visible = ref(false); // 弹窗是否可见

const modalType = ref<boolean>(true); // 弹窗标题

const emit = defineEmits(["addSuccess"]); // 触发关闭事件

const handleCancel = () => {
  visible.value = false;
  variableFormRef.value?.resetFields();
};

const handleOk = async () => {
  const valid = await variableFormRef.value?.validateFields();

  if (!valid) return;
  // 表单校验通过，执行提交逻辑
  const submitData = {
    ...variable.value,
  };
  if (isEmpty(submitData.id)) {
    delete submitData.id;
  }
  // 判断是否是新增还是修改
  const res = isEmpty(submitData.id)
    ? await changeVariable(submitData, "post")
    : await changeVariable(submitData, "put");
  // const res = await addVariable(submitData);
  if (res.code !== 0) return;
  message.success("操作成功");
  emit("addSuccess");
  visible.value = false;
  variableFormRef.value?.resetFields();
};

const showModal = (record: Record<string, any> = {}) => {
  // 如果是编辑,显示弹窗并填充表单数据
  variable.value = {
    id: record.id || "",
    nickName: record.nickName || "",
    valName: record.valName || "",
    description: record.description || "",
    unit: record.unit || "",
    dataType: record.dataType || "INTEGER",
    valType: record.valType || "READ",
    pointName: record.pointName || "",
    valueReadType: record.valueReadType || "REAL_TIME_QUERY",
    valueReadInterval: String(record.valueReadInterval ? record.valueReadInterval : 1),
    qualitySetType: record.qualitySetType || "SET_BY_VALUE_REFRESH",
    qualityTimeout: String(record.qualityTimeout ? record.qualityTimeout : 10),
    qualityPointName: record.qualityPointName || "",
    qualityTimeoutUnit: record.qualityTimeoutUnit || "MINUTES",
  };
  if (Object.keys(record).length > 0) {
    modalType.value = true;
  } else {
    modalType.value = false;
  }
  visible.value = true;
};

defineExpose({ showModal });
// ------------------------------------------表单控制--------------------------------

const variableFormRef = ref<FormInstance>(); // 表单实例

const labelCol = { span: 6 };
const wrapperCol = { span: 16 };

// 表单变量字段
const variable = ref<FormRules>({
  id: "",
  nickName: "",
  valName: "",
  description: "",
  unit: "",
  dataType: "INTEGER",
  valType: "READ",
  pointName: "",
  valueReadType: "REAL_TIME_QUERY",
  valueReadInterval: "1",
  qualitySetType: "SET_BY_VALUE_REFRESH",
  qualityTimeout: "10",
  qualityPointName: "",
  qualityTimeoutUnit: "MINUTES",
});

// 表单校验规则
const rules = {
  nickName: [{ required: true, validator: validateChinese, trigger: "change" }],
  valName: [{ required: true, validator: validateValName, trigger: "change" }],
  pointName: [{ required: false, validator: validateDomainTagItem, trigger: "change" }],
  qualityPointName: [{ required: false, validator: validateDomainTagItem, trigger: "change" }],
};

// ---------------------------------------------------汉字转拼音--------------------------------------

const getChinesePinyinInitials = (chinese: string) => {
  // 使用 pinyin-pro 库将中文转换为拼音，并提取每个拼音的首字母
  const pinyins = pinyin(chinese, { toneType: "none", type: "array" });
  return pinyins.map((pinyin) => pinyin[0].toUpperCase()).join("");
};

const handleChineseNameChange = () => {
  // 监听中文名输入框的变化，自动生成拼音简称
  const pinyinInitials = getChinesePinyinInitials(variable.value.nickName);
  variable.value.valName = pinyinInitials;
  variableFormRef.value?.validate("valName");
};
</script>

<style lang="less" scoped>
.freshTimeClass {
  display: flex;
  gap: 8px;
  margin-left: 34px;
}
</style>
