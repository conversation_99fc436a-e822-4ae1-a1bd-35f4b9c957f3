.treeOpsBox {
  width: 100%;
  height: 100%;
  // 导入导出样式
  .importExportBox {
    display: flex;
    gap: 11px;
    margin-top: 10px;
    margin-bottom: 10px;
    .btnStyle {
      width: 108px;
      height: 32px;
    }
  }
  // 树结构样式
  .projectTreeBox {
    height: 90%;
    #treeBox {
      height: calc(100% - 100px);
      overflow-y: auto;
      overflow-x: hidden;

      .custom-tree-node {
        // .ant-dropdown-trigger {
        //   display: flex;
        //   justify-items: center;
        // }
        flex: 1;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding-right: 13px;
        width: 100%;
        .channelSuccess {
          display: inline-block; /* 保证是行内元素但可以设置宽高 */
          width: 8px;
          height: 8px;
          background-color: var(--successMsgColor); /* 圆点颜色 */
          border-radius: 50%; /* 变成圆形 */
          margin-right: 5px;

        }
        .channelFail {
          display: inline-block; /* 保证是行内元素但可以设置宽高 */
          width: 8px;
          height: 8px;
          background-color: var(--errorMsgColor); /* 圆点颜色 */
          border-radius: 50%; /* 变成圆形 */
          margin-right: 5px;
        }
        .iconClass {
          margin-right: 6px;
          color: var(--iconColor);
        }
        .runBtnStyle {
          margin-left: 40px;
          vertical-align: bottom;
          text-align: center;
          color: var(--iconColor);
        }
        .area {
          color: var(--iconColor);
        }
        .searchBack {
          background-color: #fffb8f;
          box-shadow: 0 0 3px rgba(0, 0, 0, 0.2);
          border-radius: 3px;
        }
        .nodeTitleText {
  
          width: 100px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }
        .nodeExpand {
          color: #b4b4b4;
          transform: rotate(-90deg);
        }
        .nodeClose {
          color: #b4b4b4;
          transform: rotate(-180deg);
        }
      }
    }
  }
  // 操作按钮样式
  .opsBox {
    display: flex;
    flex-direction: column;
    border-top: 1px solid #e8e8e8;
    .opsBoxItem {
      cursor: pointer;
      display: flex;
      min-height: 38px;
      align-items: center;
      padding: 0px 10px;
      gap: 10px;
      .iconOps {
        width: 20px;
        height: 20px;
        background: url("../../../../../assets/操作员.png");
        background-repeat: no-repeat;
        background-position: top center;
        background-size: 100% 100%;
      }
      .iconFlow {
        width: 20px;
        height: 20px;
        background: url("../../../../../assets/控制流.png");
        background-repeat: no-repeat;
        background-position: top center;
        background-size: 100% 100%;
      }
      .iconRecord {
        width: 20px;
        height: 20px;
        background: url("../../../../../assets/视频回放.png");
        background-repeat: no-repeat;
        background-position: top center;
        background-size: 100% 100%;
      }
      .arrow {
        width: 14px;
        height: 14px;
        background: url("../../../../../assets/箭头.png");
        background-repeat: no-repeat;
        background-position: top center;
        background-size: 100% 100%;
        margin-left: auto;
      }
    }
    .opsBoxItem:hover {
      background: var(--areaColor1);
    }
  }
}
@nodeColor: linear-gradient(0deg, #4b84ffb7 0%, #5c89ff38 100%);
// 弹窗样式
.modelContent {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
}

.message {
  margin-left: 70px;
  margin-top: 5px;
  color: #ff4d4f;
  font-size: 14px;
}

.disabledRepeat {
  cursor: not-allowed !important;
}

// 覆盖工程树样式

:deep(.ant-tree-switcher) {
  line-height: 42px;
  width: 0px;
}

// 树节点
:deep(.ant-tree .ant-tree-node-content-wrapper) {
  min-height: 42px;
  line-height: 42px;
  padding: 0px;
}

:deep(.ant-tree-treenode) {
  padding: 0px;
}

:deep(.ant-tree-indent-unit) {
  width: 10px !important;
}

:deep(.ant-tree .ant-tree-node-content-wrapper .ant-tree-iconEle) {
  vertical-align: baseline;
}
// 节点选中样式
:deep(.ant-tree-treenode-selected) {
  background: var(--areaColor2);
  border-radius: 20px 0px 0px 20px;
}

// 节点hover样式重置
:deep(.ant-tree .ant-tree-node-content-wrapper:hover) {
  background: none;
}
:deep(.ant-tree-treenode) {
  border: 1px solid rgba(0, 0, 0, 0);
}
:deep(.ant-tree-treenode:hover) {
  border-radius: 20px 0px 0px 20px;
  background: var(--areaColor2);
}
// 节点选中样式重置
:deep(.ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected) {
  background: none;
}
