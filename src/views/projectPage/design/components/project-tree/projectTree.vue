<template>
  <div class="treeOpsBox">
    <!-- <a-input placeholder="请输入节点名称" v-model:value="searchValue" @change="onSearchNode">
      <template #prefix>
        <IconFont type="icon-chaxun" />
      </template>
    </a-input> -->
    <a-dropdown :trigger="['click']">
      <a-input-search
        v-model:value="searchValue"
        style="margin-bottom: 8px"
        placeholder="输出节点"
        @change="searchTree"
      ></a-input-search>
      <template #overlay>
        <a-menu v-if="searchList.length">
          <a-menu-item v-for="item in searchList" :key="item.key" @click="handleSelect(item)">
            {{ item.label }}
          </a-menu-item>
        </a-menu>
        <a-empty description="" v-else></a-empty>
      </template>
    </a-dropdown>
    <div class="importExportBox">
      <a-button @click="importChannelFun" class="btnStyle">
        <template #icon><IconFont type="icon-daoru" /></template>导入通道</a-button
      >
      <a-button @click="exportChannelFun" class="btnStyle">
        <template #icon><IconFont type="icon-daochu" /></template>导出通道</a-button
      >
    </div>
    <a-skeleton active :loading="store.state.design.loading" v-for="item in 3" />
    <div v-if="!store.state.design.loading" class="projectTreeBox">
      <div v-if="!treeData.length">
        <a-empty description="请打开工程"></a-empty>
      </div>
      <div id="treeBox" ref="treeBoxRef">
        <div v-show="treeData.length">
          <a-tree
            :tree-data="treeData"
            v-model:selectedKeys="selectedkeys"
            block-node
            v-model:expanded-keys="expandedKeys"
            @select="selectTree"
            :fieldNames="{ key: 'id', title: 'label' }"
            ref="projectTreeRef"
          >
            <template #switcherIcon="{ switcherCls }"></template>
            <template #title="{ data, id }">
              <span class="custom-tree-node">
                <a-dropdown :trigger="['contextmenu']" :getPopupContainer="(trigger) => treeBoxRef">
                  <div :id="'tree-node_' + data.id" class="nodeTitleBox">
                    <IconFont
                      :type="IconList[data.type]"
                      v-if="data.type !== 'channel'"
                      class="iconClass"
                    />
                    <IconFont
                      type="icon-a-Property1qiuxing"
                      v-if="data.type === 'channel' && data.deviceType.includes('PTZ')"
                      class="iconClass"
                    />
                    <IconFont
                      type="icon-a-Property1qiangji"
                      v-if="data.type === 'channel' && !data.deviceType.includes('PTZ')"
                      class="iconClass"
                    />
                    <div
                      v-if="data.type === 'channel'"
                      :class="data.status === 'CONNECT_SUCCESS' ? 'channelSuccess' : 'channelFail'"
                    ></div>
                    <a-tooltip placement="topLeft">
                      <template #title>
                        {{ !data.des ? data.label : data.des }}
                      </template>

                      <span
                        :ref="(el) => (nodeRefs[id] = el)"
                        :class="{ searchBack: searchKey === id }"
                        class="nodeTitleText"
                        >{{
                          data.label.length > 7 ? data.label.slice(0, 7) + "..." : data.label
                        }}</span
                      >
                    </a-tooltip>
                    <span
                      v-if="
                        (data.type === 'directory' || data.type === 'business-root') &&
                        getTotalChannelCount(data) !== 0
                      "
                    >
                      (<span class="area">{{ getOnlineChannelCount(data) }}</span> /
                      {{ getTotalChannelCount(data) }})
                    </span>
                  </div>
                  <template #overlay>
                    <a-menu
                      v-if="data.type === 'business-root'"
                      @click="({ key: menuKey }) => onContextMenuClick(data.id, menuKey, data)"
                    >
                      <a-menu-item key="addDirectory">新建区域</a-menu-item>
                      <a-menu-item key="batchAddDir">批量新建区域</a-menu-item>
                      <a-menu-item key="addChannel">新建通道</a-menu-item>
                      <a-menu-item key="copyAlarmList">复制报警列表URL</a-menu-item>
                    </a-menu>
                    <a-menu
                      v-else-if="data.type === 'ens'"
                      @click="({ key: menuKey }) => onContextMenuClick(data.id, menuKey, data)"
                    >
                      <a-menu-item key="ensURl">编辑页面URL</a-menu-item>
                    </a-menu>

                    <a-menu
                      v-else-if="data.type === 'directory'"
                      @click="({ key: menuKey }) => onContextMenuClick(data.id, menuKey, data)"
                    >
                      <a-menu-item key="addDirectory">新建区域</a-menu-item>
                      <a-menu-item key="batchAddDir">批量新建区域</a-menu-item>
                      <a-menu-item key="addChannel">新建通道</a-menu-item>
                      <a-menu-item key="delDirectory">删除区域</a-menu-item>
                    </a-menu>
                    <a-menu
                      v-else-if="data.type === 'channel'"
                      @click="({ key: menuKey }) => onContextMenuClick(data.id, menuKey, data)"
                    >
                      <a-menu-item key="addInstance">新建预置点</a-menu-item>
                      <a-menu-item key="delChannel">删除通道</a-menu-item>

                      <a-menu-item key="copyUrl">复制嵌入URL</a-menu-item>
                    </a-menu>
                    <a-menu
                      v-else-if="data.type === 'preset'"
                      @click="({ key: menuKey }) => onContextMenuClick(data.id, menuKey, data)"
                    >
                      <a-menu-item key="delInstance">删除预置点</a-menu-item>
                      <a-menu-item key="copyUrlForPreset">复制嵌入URL</a-menu-item>
                    </a-menu>
                  </template>
                </a-dropdown>

                <a-tooltip placement="top">
                  <template #title>执行算法</template>
                  <IconFont
                    v-if="data.type === 'preset'"
                    @click.stop="start(data.id)"
                    type="icon-bofang"
                    class="runBtnStyle"
                    :class="{ disabledRepeat: notRepeatIds.includes(data.id) }"
                  >
                  </IconFont>
                </a-tooltip>
                <IconFont
                  v-if="data.children.length !== 0 && expandedKeys.includes(data.id)"
                  type="icon-yuanpan-zuo"
                  class="nodeExpand"
                  @click.stop="closeNode(data.id)"
                ></IconFont>
                <IconFont
                  v-if="data.children.length !== 0 && !expandedKeys.includes(data.id)"
                  type="icon-yuanpan-zuo"
                  class="nodeClose"
                  @click.stop="expandNode(data.id)"
                ></IconFont>
              </span>
            </template>
          </a-tree>
        </div>
      </div>
      <div class="opsBox">
        <div class="opsBoxItem" @click="opsBtn('ops')">
          <div class="iconOps"></div>
          <span>操作员页</span>
          <div class="arrow"></div>
        </div>
        <div class="opsBoxItem" @click="opsBtn('ens')">
          <div class="iconFlow"></div>
          <span>控制流</span>
          <div class="arrow"></div>
        </div>
        <div class="opsBoxItem" @click="opsBtn('ens')">
          <div class="iconRecord"></div>
          <span>视频回放</span>
          <div class="arrow"></div>
        </div>
      </div>
    </div>
  </div>

  <a-modal
    v-model:open="visible"
    title="添加"
    @ok="handleOk"
    okText="确定"
    cancelText="取消"
    :confirmLoading="confirmLoading"
  >
    <div class="modelContent">
      <span><span style="color: red">*</span>{{ currentNode.modalName }}： </span>
      <a-input
        style="width: 70%"
        v-model:value="currentNode.modalInputName"
        @change="verificationProjectName"
      ></a-input>
    </div>
    <div v-if="showError" class="message">
      <span>名称不能为空，不能包含特殊字符，20个字符以内</span>
    </div>
  </a-modal>

  <a-modal
    v-model:open="visibleEns"
    title="编辑URL"
    @ok="handleOkEns"
    okText="确定"
    cancelText="取消"
  >
    <div class="modelContent">
      <span><span style="color: red">*</span>URL： </span>
      <a-input
        style="width: 70%"
        v-model:value="ensOrFlowUrl"
        @change="verificationProjectName"
      ></a-input>
    </div>
  </a-modal>
  <BatchAddDir ref="batchAddDirRef" @add-dir="addBatchSuccess" />
  <BatchAddChannel ref="batchAddChannelRef" @add-channel-batch="addBatchSuccess"></BatchAddChannel>
  <uploadModal ref="uploadModal" :uploadFunc="importChannel"></uploadModal>
</template>
<script lang="ts">
import {
  defineComponent,
  ref,
  onMounted,
  reactive,
  computed,
  nextTick,
  onBeforeUnmount,
} from "vue";
import { useRouter } from "vue-router";
import { useStore } from "vuex";
import { message, Modal, TreeProps } from "ant-design-vue";
import IconFont from "@/components/c-iconfont";
import { IconList } from "@/components/datatypeComponent";
import {
  getOnlineChannelCount,
  getTotalChannelCount,
  isEmpty,
  JsonParseFun,
  JsonStrFun,
} from "@/common/utils";
import {
  getProjectTree,
  clickTreeNode,
  getModelConf,
  addTreeNode,
  delTreeNode,
  treeNodeConf,
  importChannel,
  exportChannel,
} from "@/api/design";
import uEmitter from "@/common/u-event-bus/index";
import SetInterval from "@/common/SetInterval";
import BatchAddDir from "../batchAddDir.vue";
import BatchAddChannel from "@/components/BatchAddChannel.vue";
import { DownOutlined } from "@ant-design/icons-vue";
import uploadModal from "@/components/uploadModal/uploadModal.vue";
import { downloadByParseBlob } from "@/common/u-http";

export default defineComponent({
  components: {
    IconFont,
    BatchAddDir,
    DownOutlined,
    uploadModal,
    BatchAddChannel,
  },
  props: {
    randomStr: {
      type: String,
    },
    treeResize: {
      type: Number,
      default: 164,
    },
  },
  setup(props, ctx) {
    const router = useRouter();
    const store = useStore();
    const selectedkeys = computed(() => [store.state.design.activeTreeKey]);
    const expandedKeys = ref<(string | number)[]>([]);
    const projectTreeRef = ref();
    const treeData = computed(() => store.state.design.allTreeData);
    const dataList = ref([]);

    const activeKey = computed(() => store.state.design.activeTreeKey);
    // const projectBaseInfo = computed(() => store.state.design.projectBaseInfo);
    const channelBaseInfo = computed(() => store.state.design.channelBaseInfo);
    const directoryBaseInfo = computed(() => store.state.design.directoryBaseInfo);
    const channelConfigInfo = computed(() => store.state.design.channelConfigInfo);
    const presetBaseInfo = computed(() => store.state.design.presetBaseInfo);
    const scheduleConfigModel = computed(() => store.state.design.scheduleConfigModel);
    const allList = computed(() => [
      ...channelBaseInfo.value,
      ...channelConfigInfo.value,
      ...presetBaseInfo.value,
      ...scheduleConfigModel.value,
      ...directoryBaseInfo.value,
    ]);

    const srcollTop = ref<number>(null); // 滚动距离

    const treeBoxRef = ref(null);

    // --------------------------------------------导入通道------------------------------

    const uploadModal = ref(null); // 上传弹窗ref

    const importChannelFun = () => {
      uploadModal.value.handleOpenModal();
    };

    // --------------------------------------------导出通道------------------------------

    const exportChannelFun = async () => {
      try {
        const resData = await exportChannel();
        downloadByParseBlob(resData, (jsonObj) => {
          message.error(jsonObj.message);
        });
      } catch (err) {
        console.log(err);
      }
    };

    const generateList = (data) => {
      for (let i = 0; i < data.length; i++) {
        const node = data[i];
        const key = node.id;
        dataList.value.push({
          key,
          title: node.label,
        });
        if (node.children) {
          generateList(node.children);
        }
      }
    };

    const getTreeData = async (addItem = null) => {
      store.state.design.loading = true;
      const res = await getProjectTree();
      if (res.code === 0 && res.data.length !== 0) {
        // expandedKeys.value = [res.data.tree[0].id].concat(expandedKeys.value);

        store.commit("setallTreeData", res.data.tree);
        ctx.emit("getOsdSchInfo", res.data.project);
        nextTick(() => {
          if (srcollTop.value && !addItem) {
            nextTick(() => {
              document.getElementById("treeBox").scrollTop = srcollTop.value;
            });
          }

          if (addItem) {
            selectTree(0, { node: addItem });
            searchTimer = setTimeout(() => {
              const scrollContainer = document.getElementById("treeBox");
              const anchorElement = document.getElementById("tree-node_" + addItem.id);
              const parentElement = findAncestor(anchorElement, ".ant-tree-treenode");

              if (scrollContainer && parentElement) {
                const elementPosition = parentElement.offsetTop - scrollContainer.offsetTop;
                scrollContainer.scrollTo({
                  top: elementPosition,
                  behavior: "smooth",
                });
              }
            }, 300);
          }
        });

        generateList(treeData.value);

        //展开所有
        expandedKeys.value = dataList.value.map((item) => item.key);
      }
      if (res.code === 500) {
        store.commit("clearProjectAllInfo");
      }
      store.state.design.loading = false;
      return Promise.resolve(res);
    };

    // 查询是否存在未保存的节点及其属性
    const seekSaveKey = (key: string | MouseEvent) => {
      return key && allList.value.some((element) => element.id === key || element.presetId === key);
    };

    const selectTree = (selectedKey, { node }) => {
      if (seekSaveKey(activeKey.value)) {
        Modal.confirm({
          title: "关闭",
          content: "当前激活节点信息尚未保存,确定继续吗？",
          onOk() {
            //清除未保存的数据
            store.commit("clearByTabKey", activeKey.value);
            doSelect({ node });
          },
        });
      } else {
        doSelect({ node });
      }
    };

    const opsBtn = (type) => {
      switch (type) {
        case "ens":
          window.open((window as any).g.nodered_url);
          break;
        case "ops":
          const url = router.resolve({
            name: "videoAlarm",
          });
          window.open(url.href);
          break;
        default:
          break;
      }
    };
    /**
     * 通过ID在树形结构中查找节点
     * @param {Array|Object} tree - 树形结构数据，可以是数组或节点对象
     * @param {string} targetId - 要查找的目标ID
     * @returns {Object} 找到的节点对象，没找到返回null
     */
    function findTreeNodeIterative(tree, targetId) {
      const stack = Array.isArray(tree) ? [...tree] : [tree];

      while (stack.length > 0) {
        const current = stack.pop();
        if (current?.id === targetId) return current;

        if (current?.children) {
          // 后进先出保证深度优先
          stack.push(...current.children.reverse());
        }
      }

      return null;
    }

    const doSelect = ({ node }) => {
      expandedKeys.value = expandedKeys.value.concat(node.id);
      const treeNode = {
        id: node.id,
        label: node.label,
        typeNode: node.type,
        des: node.des,
        url: node.url || "",
      };
      switch (node.type) {
        case "channel":
          clickTreeNode(treeNode.id).then((res) => {
            if (res.data) {
              const treeConf = { ...treeNode, ...res.data };
              store.commit("pushtreeKey", treeConf);
              store.commit("setActiveTree", treeNode.id);
            }
          });
          break;

        case "preset":
          getModelConf(treeNode.id).then((res) => {
            const treeConf = { ...treeNode, ...res.data };
            store.commit("pushtreeKey", treeConf);
            store.commit("setActiveTree", treeNode.id);
          });
          break;

        case "ens":
          window.open(treeNode.url);
          break;

        case "ops":
          const url = router.resolve({
            name: "videoAlarm",
          });
          window.open(url.href);
          break;

        case "project": // 如果需要保留注释可以取消注释
          const projectBaseInfo = {
            label: node.label,
            des: node.des,
            id: node.id,
            typeNode: node.type,
          };
          store.commit("pushtreeKey", projectBaseInfo);
          store.commit("setActiveTree", treeNode.id);
          break;

        case "control-flow":
          // window.open(treeNode.url);
          window.open((window as any).g.nodered_url);
          break;

        case "directory":
          const treeConf = treeNode;
          store.commit("pushtreeKey", treeConf);
          store.commit("setActiveTree", treeNode.id);
          break;

        default:
          // 可选：处理未定义或未知的节点类型
          break;
      }
    };

    const expandNode = (key) => {
      expandedKeys.value.push(key);
    };

    const closeNode = (key) => {
      console.log(expandedKeys.value);
      expandedKeys.value = expandedKeys.value.filter((item) => item !== key);
    };

    // 当前节点信息
    const currentNode = reactive({
      modalName: "名称",
      modalType: "directory",
      modalId: "",
      modalInputName: "",
      isErrorShow: false,
    });
    const visible = ref<boolean>(false); // 添加弹窗
    const delVisible = ref<boolean>(false); // 删除弹窗
    /**
     * 获取当前id下的子id
     * @param currentId 当前需要删除的ID
     */
    const getNodeChildId = (data) => {
      const ids = [];

      if (data.id) {
        ids.push(data.id);
      }
      if (data.children) {
        for (const child of data.children) {
          ids.push(...getNodeChildId(child));
        }
      }
      return ids;
    };
    // ---------------------------------------- 批量添加区域--------------------------------------
    const batchAddDirRef = ref();
    const addBatchSuccess = (data) => {
      getTreeData();
    };

    //----------------------------------------- 批量添加通道 -------------------------------------
    const batchAddChannelRef = ref();

    // -----------------------------------------右键菜单------------------------------------------
    const onContextMenuClick = async (treeId: string, menuKey: string, data?) => {
      srcollTop.value = document.getElementById("treeBox").scrollTop;

      const showModal = (modalName: string, modalType: string) => {
        currentNode.modalName = modalName;
        currentNode.modalType = modalType;
        currentNode.modalId = treeId;
        currentNode.modalInputName = "";
        showError.value = false;
        visible.value = true;
      };

      switch (menuKey) {
        case "addDirectory":
          showModal("区域名称", "directory");
          break;
        case "batchAddDir":
          const batchTree = [
            {
              title: data.label,
              key: "0-0",
              children: [],
              type: data.type,
            },
          ];
          batchAddDirRef.value.showModal({ ...data, batchTree });
          break;

        case "ensURl":
        case "control-flowURl":
          ensOrFlowUrl.value = data.url;
          ensOrFlow = data;
          visibleEns.value = true;
          break;

        case "addChannel":
          // showModal("通道名称", "channel");
          batchAddChannelRef.value.showModal(data);
          break;

        case "delChannel":
          currentNode.modalName = "";
          currentNode.modalType = "";
          currentNode.modalId = treeId;
          currentNode.modalInputName = "";
          const childIds1 = getNodeChildId(data).slice(1);
          const activeIds1 = computed(() => store.state.design.TreeKeyAggregate).value.map(
            (item) => {
              return item.id;
            }
          );
          const allIds1 = childIds1.concat(activeIds1);
          if (new Set(allIds1).size !== allIds1.length) {
            message.warn("请关闭删除节点的子节点");
            return;
          }
          Modal.confirm({
            title: "删除通道",
            content: "确定删除通道【" + data.label + "】吗?",
            onOk() {
              delTreeNode([currentNode.modalId]).then((res) => {
                if (res.code === 0) {
                  getTreeData();
                  uEmitter.emit("deleteNode", currentNode.modalId);
                  message.success("删除成功");
                }
              });
            },
          });
          break;
        case "delDirectory":
          currentNode.modalName = "";
          currentNode.modalType = "";
          currentNode.modalId = treeId;
          currentNode.modalInputName = "";

          const childIds = getNodeChildId(data).slice(1);
          const activeIds = computed(() => store.state.design.TreeKeyAggregate).value.map(
            (item) => {
              return item.id;
            }
          );
          const allIds = childIds.concat(activeIds);
          if (new Set(allIds).size !== allIds.length) {
            message.warn("请关闭删除节点的子节点");
            return;
          }

          Modal.confirm({
            title: "删除区域",
            content: "确定删除区域【" + data.label + "】吗?",
            onOk() {
              delTreeNode([currentNode.modalId]).then((res) => {
                if (res.code === 0) {
                  getTreeData();
                  uEmitter.emit("deleteNode", currentNode.modalId);
                  message.success("删除成功");
                }
              });
            },
          });

          break;

        case "addInstance":
          showModal("预置点名称", "preset");
          break;

        case "delInstance":
          currentNode.modalName = "";
          currentNode.modalType = "";
          currentNode.modalId = treeId;
          currentNode.modalInputName = "";
          Modal.confirm({
            title: "删除预置点",
            content: "确定删除预置点【" + data.label + "】吗?",
            onOk() {
              delTreeNode([currentNode.modalId]).then((res) => {
                if (res.code === 0) {
                  getTreeData();
                  uEmitter.emit("deleteNode", currentNode.modalId);
                  message.success("删除成功");
                }
              });
            },
          });
          break;

        case "copyId":
          copyToClipboard(treeId);
          break;

        case "copyUrl":
          const url = `${window.location.origin}/DCSChannelView?channelId=${treeId}`;
          copyToClipboard(url);
          break;

        case "copyUrlForPreset":
          const url1 = `${window.location.origin}/DCSChannelView?channelId=${data.parentId}&presetId=${data.id}`;
          copyToClipboard(url1);
          break;

        case "copyAlarmList":
          const apiUrl = `${window.location.origin}/DCSvideoAlarm`;
          copyToClipboard(apiUrl);
          break;
        case "importChannel":
          async function uploadFile(file) {
            const formData = new FormData();
            formData.append("file", file);
            formData.append("id", data.id);

            try {
              // 替换为你的上传接口URL
              importChannel(formData).then((res) => {
                if (res.code === 0) {
                  message.success("操作成功");
                  getTreeData();
                }
                input.remove();
              });
            } catch (error) {
              console.error("上传错误:", error);
            }
          }
          const input = document.createElement("input");
          input.type = "file";
          input.onchange = (e) => {
            const file = (e.target as HTMLInputElement).files[0];
            if (!file) return;
            const allowedTypes = [
              "application/vnd.ms-excel",
              "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ];
            if (!allowedTypes.includes(file.type)) {
              message.warn("请上传excel文件");
              return;
            }

            const maxSize = 5 * 1024 * 1024; // 5MB
            if (file.size > maxSize) {
              message.warn("文件大小不能超过5MB");
              return;
            }
            // 执行上传
            uploadFile(file);
          };
          document.body.appendChild(input);
          input.click();
          break;

        default:
          break;
      }
    };

    // -----------------------------------------复制到粘贴板--------------------------------------
    const copyToClipboard = (text: string) => {
      const textArea = document.createElement("textarea");
      textArea.value = text;
      document.body.appendChild(textArea);
      textArea.select();

      return new Promise<void>((resolve, reject) => {
        const isCopied = document.execCommand("copy");
        textArea.remove(); // 移除文本框
        isCopied ? resolve() : reject(new Error("出错了"));
      })
        .then(() => {
          message.success("操作成功");
        })
        .catch(() => {
          message.error("操作成功");
        });
    };

    // -----------------------------------------------预置点/区域添加----------------------------------------
    // 名称校验
    const showError = ref<boolean>(false);
    // 校验规则
    const verificationProjectName = () => {
      const myreg = /^(?![!@#$%^&*()+=\[\]{}\\|;:'",.<>?`~])[\w\u4e00-\u9fa5]{1,20}$/;
      if (
        currentNode.modalInputName === "" ||
        currentNode.modalInputName === null ||
        !myreg.test(currentNode.modalInputName)
      ) {
        showError.value = true;
        return true;
      } else {
        showError.value = false;
        return false;
      }
    };

    const confirmLoading = ref<boolean>(false);
    const handleOk = () => {
      if (verificationProjectName()) {
        return;
      }
      const data = {
        label: currentNode.modalInputName,
        parentId: currentNode.modalId,
        type: currentNode.modalType,
      };
      confirmLoading.value = true;
      addTreeNode(data).then((res) => {
        if (res.code === 0) {
          visible.value = false;
          getTreeData(res.data);
          message.success("操作成功");
        }
        setTimeout(() => {
          confirmLoading.value = false;
        }, 100);
      });
    };

    const visibleEns = ref<boolean>(false);
    const ensOrFlowUrl = ref<string>(""); // 当前编辑的url
    let ensOrFlow = null; // 当前编辑的label
    const handleOkEns = () => {
      const data = {
        id: ensOrFlow.id,
        label: ensOrFlow.label,
        url: ensOrFlowUrl.value,
      };
      treeNodeConf(data).then((res) => {
        if (res.code === 0) {
          message.success("编辑成功");
          ensOrFlow = null;
          visibleEns.value = false;
          getTreeData();
        }
      });
    };

    const freshTreeStatus = () => {
      getProjectTree().then((res) => {
        if (res.code === 0) {
          if (res.data.length !== 0) {
            // expandedKeys.value = [res.data.tree[0].id].concat(expandedKeys.value);
            store.commit("setallTreeData", res.data.tree);
          }
        }
        if (res.code === 500) {
          store.commit("clearProjectAllInfo");
        }
      });
    };

    function openTreeTimer() {
      SetInterval.add("TreeTimer", freshTreeStatus, 10000);
      SetInterval.run("TreeTimer");
    }
    function closeTreeTimer() {
      SetInterval.close("TreeTimer");
      // console.log("关闭TreeTimer定时器");
    }

    // -------------------------------------------------------------------------树节点搜索---------------------------------------------------------
    const searchList = ref<any[]>([]); // 搜索结果列表

    const searchValue = ref<string>(""); // 当前搜索值

    const nodeRefs = ref({}); // 节点引用

    const searchKey = ref(""); // 搜索关键字

    let searchTimer = null; // 滚动延时器

    // 找到所有父节点的ID
    function findParentIdsFromTree(treeArray, targetId) {
      const path = [];

      function dfs(node, ancestors) {
        if (node.id === targetId) {
          path.push(...ancestors);
          return true;
        }

        if (node.children && node.children.length > 0) {
          for (const child of node.children) {
            if (dfs(child, [...ancestors, node.id])) {
              return true;
            }
          }
        }

        return false;
      }

      for (const root of treeArray) {
        if (dfs(root, [])) {
          break;
        }
      }

      return path;
    }

    function searchOutputParams(tree, keyword) {
      const results = [];

      if (keyword === "") {
        return [];
      }
      function traverse(node, parent, grandparent) {
        // 匹配output-param节点且包含关键字
        if (node.label.toUpperCase().includes(keyword.toUpperCase())) {
          // 构建层级标签
          const labels = [];
          if (grandparent) labels.push(grandparent.label);
          if (parent) labels.push(parent.label);
          labels.push(node.label);

          // 生成格式化结果
          results.push({
            key: node.id,
            label: labels.join("-"),
          });
        }

        // 递归遍历子节点，维护父级关系
        if (node.children) {
          node.children.forEach((child) => {
            // 当前节点成为父级，原父级成为祖父级
            traverse(child, node, parent);
          });
        }
      }

      // 从根节点开始遍历
      tree.forEach((root) => traverse(root, null, null));

      return results;
    }

    // 搜索树
    const searchTree = () => {
      // debugger;
      searchList.value = searchOutputParams(treeData.value, searchValue.value);
      searchKey.value = "";
    };

    // 滚动到选中节点
    const scrollToNode = () => {
      if (searchKey.value && nodeRefs.value[searchKey.value]) {
        const node = nodeRefs.value[searchKey.value];
        expandedKeys.value.push();
        node.scrollIntoView({ behavior: "smooth", block: "center" });
      }
    };
    // 选中搜索结果
    const handleSelect = async (item) => {
      searchKey.value = item.key;
      await nextTick();
      console.log(treeData.value, item.key);
      console.log(findParentIdsFromTree(treeData.value, item.key));
      findParentIdsFromTree(treeData.value, item.key).forEach((item) => {
        expandedKeys.value.push(item);
      });
      scrollToNode();
    };

    const findAncestor = (el, selector) => {
      while (el && el !== document) {
        if (el.matches(selector)) {
          return el;
        }
        el = el.parentElement;
      }
      return null;
    };
    // const onSearchNode = (val) => {
    //   if (searchTimer) {
    //     clearTimeout(searchTimer);
    //   }
    //   searchTimer = setTimeout(() => {
    //     const scrollContainer = document.getElementById("treeBox");
    //     const anchorElement = document.getElementById(searchValue.value);
    //     const parentElement = findAncestor(anchorElement, ".ant-tree-treenode");

    //     if (scrollContainer && parentElement) {
    //       const elementPosition = parentElement.offsetTop - scrollContainer.offsetTop;
    //       console.log(elementPosition, parentElement, scrollContainer, "elementPosition");
    //       scrollContainer.scrollTo({
    //         top: elementPosition,
    //         behavior: "smooth",
    //       });
    //     }
    //   }, 300);
    // };

    // ---------------------------------------预置点执行----------------------------------------

    const notRepeatIds = ref<string[]>([]);
    const start = (instanceId) => {
      // console.log(instanceId);

      if (notRepeatIds.value.includes(instanceId)) {
        message.warn("该预置点识别任务正在执行中，请勿重复操作");
        return;
      }
      notRepeatIds.value.push(instanceId);

      uEmitter.emit("startInstanceToExecute", instanceId);
    };
    uEmitter.on("endInstance", (instanceId) => {
      notRepeatIds.value = notRepeatIds.value.filter((item) => item !== instanceId);
      // console.log(notRepeatIds.value);
    });

    onMounted(() => {
      getTreeData();
      // openTreeTimer();

      // 接收上传通道
      uEmitter.on("refreshProjectTreebyUpload", async () => {
        await getTreeData();
        if (isEmpty(activeKey.value)) {
          return;
        }

        const treeTemp = JsonParseFun(JsonStrFun(treeData.value));
        const node = findTreeNodeIterative(treeTemp, activeKey.value);
        doSelect({ node });
      });
    });
    onBeforeUnmount(() => {
      closeTreeTimer();
      clearTimeout(searchTimer);
      uEmitter.off("refreshProjectTreebyUpload");
    });

    return {
      store,
      treeBoxRef,
      uploadModal,
      importChannelFun,
      exportChannelFun,
      importChannel,
      notRepeatIds,
      start,
      treeData,
      expandedKeys,
      searchValue,
      // onSearchNode,
      projectTreeRef,
      closeNode,
      opsBtn,
      selectTree,
      selectedkeys,
      expandNode,
      currentNode,
      visible,
      delVisible,
      batchAddDirRef,
      batchAddChannelRef,
      addBatchSuccess,
      onContextMenuClick,
      confirmLoading,
      handleOk,
      IconList,
      showError,
      verificationProjectName,
      visibleEns,
      handleOkEns,
      ensOrFlowUrl,
      searchList,
      nodeRefs,
      searchKey,
      searchTree,
      handleSelect,
      getTotalChannelCount,
      getOnlineChannelCount,
    };
  },
});
</script>
<style scoped lang="less">
@import "./projectTree.less";
</style>
