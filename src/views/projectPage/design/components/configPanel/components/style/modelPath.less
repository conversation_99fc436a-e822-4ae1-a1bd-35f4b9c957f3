.scrollBox {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  overflow-x: hidden;
  padding: 0px 5px 600px 5px;
  clear: both;
}

.title {
  height: 35px;
  line-height: 35px;
  background: #f1f2f4;
  padding: 7px 5px;
  font-weight: bold;
}

.confTrigger {
  float: left;
  font-size: 14px;
  margin-top: 4px;
  cursor: pointer;
}

.PTZBox {
  display: flex;
  flex-direction: column;
}

.PTZbtn {
  width: 80%;
  margin-left: 18px;
}

.PTZControlBox {
  /* float: right; */
  width: 95%;
  margin-left: 5%;
}

.PTZinput {
  width: 80%;
  margin-bottom: 10px;
  margin-left: 10px;
}
.ptzBtnBox {
  display: flex;
  gap: 5px;
  float: right;
  margin-right: 5px;
}

.table-item {
  width: 80%;
  table-layout: fixed;
}

.table-item > tr {
  height: 50px;
}

.table-item > tr > td {
  text-align: center;
}

.table-PTZ {
  width: 80%;
  table-layout: fixed;
}

.table-PTZ > tr {
  height: 40px;
}

.table-PTZ > tr > td {
  text-align: center;
}

.PTZicon {
  font-size: 30px;
  cursor: pointer;
}
.controlBox {
  margin-bottom: 10px;
}

.isPTZ_CAMERA {
  font-size: 30px;
  cursor: not-allowed;
  /* pointer-events: none; */
}

// -----------------------------------------------------------------场景配置样式-------------------------------------------
.sceneItemBox {
  margin-top: 10px;
  padding: 8px;
  border: 1px solid #ddd;
  display: flex;
  gap: 8px;
  position: relative;
  height: 69px;
  align-items: center;
  .sceneImg {
    width: 78px;
    height: 50px;
  }
  .sceneIconConf {
    width: 22px;
    height: 22px;
    background: #e5f0f8;
    color: var(--textColor1);
    font-size: 16px;
    padding: 2px 3px;
    margin-left: auto;
    cursor: pointer;
  }
  .sceneIconDel {
    width: 22px;
    height: 22px;
    background: #ffede8;
    color: #ff5824;
    font-size: 16px;
    padding: 2px 3px;
    cursor: pointer;
  }
}
.sceneBtnItem {
  margin-bottom: 10px;
  user-select: none;
  position: relative;
  min-width: 65px;
}
.sceneBtnNameInput {
  height: 16px;
  width: 50px;
  border: 0.5px solid #ccc;
}
.sceneBtnNameInput:focus-visible {
  outline: none;
}
.closeIcon {
  position: absolute;
  top: 1px;
  right: 0px;
  font-size: 10px;
  margin-left: 5px;
}
.closeIcon:hover {
  color: red;
}

// -----------------------------------------------------------------------算法配置样式--------------------------------------------

.addAlBtn {
  width: 100%;
  height: 32px;
  color: var(--textColor1);
}
.alItemBox {
  width: 100%;
}

.alBox {
  margin-top: 10px;
  padding: 8px;
  border: 1px solid #ddd;
  display: flex;
  gap: 8px;
  position: relative;
  height: 69px;
  align-items: center;
}

.alIndex {
  font-weight: 500;
  margin-inline-end: 0px;
}

.alConfStatus {
  width: 57px;
  height: 24px;
  background: #ffe3ce;
  border-radius: 54px;
  color: #e46700;
}

.alSelectItem {
  width: 70%;
}

.IconBoxConf {
  width: 22px;
  height: 22px;
  background: #e5f0f8;
  color: var(--textColor1);
  font-size: 16px;
  padding: 2px 3px;
  margin-left: auto;
}

.IconBoxDel {
  width: 22px;
  height: 22px;
  background: #ffede8;
  color: #ff5824;
  font-size: 16px;
  padding: 2px 3px;
}

.dispatch {
  width: 100%;
  table-layout: fixed;
  margin-top: 10px;
}

.dispatch > tr {
  height: 40px;
}

.dispatch > tr > td {
  text-align: center;
}

.outputTable {
  margin-top: 10px;
  width: 100%;
  border: 1px solid black;
}

.outputTable > tr {
  border: 1px solid black;
}

.outputTable > tr > td {
  border: 1px solid black;
}

::v-deep .ant-form-item-label > label {
  font-size: 14px !important;
}

:deep(.ant-form-item) {
  margin-bottom: 5px !important;
}

.tabTitle {
  writing-mode: vertical-lr;
  letter-spacing: 2px;
}

.AlgorithmBox {
  height: auto;
  overflow-y: auto;
}
