<template>
  <div style="display: flex; height: 100%; flex-direction: column">
    <a-menu
      v-model:selectedKeys="contorlActiveKey"
      @click="toNode"
      mode="horizontal"
      style="width: 100%; height: 40px"
    >
      <a-menu-item key="attribute">
        <span>属性</span>
      </a-menu-item>
      <a-menu-item key="channelConf">
        <span>通道配置</span>
      </a-menu-item>
      <a-menu-item key="PTZControl">
        <span>云台控制</span>
      </a-menu-item>
    </a-menu>
    <div class="scrollBox" id="propScrollBox" ref="propScrollBox">
      <div class="propsBox" id="attribute">
        <a-divider orientation="left">属性</a-divider>
        <a-form
          :model="propsFrom"
          :label-col="{ span: 4 }"
          :wrapper-col="{ span: 18 }"
          autocomplete="off"
          :rules="rules"
          ref="propsRef"
        >
          <a-form-item label="名称" name="propsName"
            ><a-input
              v-model:value="propsFrom.propsName"
              size="small"
              style="width: 100%"
              @change="addBaseInfo"
            ></a-input
          ></a-form-item>
          <a-form-item label="描述" name="propsDescription"
            ><a-textarea
              v-model:value="propsFrom.propsDescription"
              :maxlength="255"
              style="width: 100%"
              @change="addBaseInfo"
              :auto-size="{ minRows: 2, maxRows: 5 }"
            ></a-textarea
          ></a-form-item>
        </a-form>
        <a-button size="small" @click="saveNodeConf" type="primary" style="margin-left: 78.5%"
          >保存</a-button
        >
      </div>
      <div class="propsBox" id="channelConf">
        <a-divider orientation="left">通道配置</a-divider>
        <a-form
          :model="confFrom"
          :label-col="{ span: 6 }"
          :wrapper-col="{ span: 16 }"
          autocomplete="off"
        >
          <!-- ----------------------------------视频协议-------------------------------- -->
          <!--          <a-form-item label="视频协议" name="videoProtocolType">-->
          <!--            <a-select-->
          <!--              v-model:value="confFrom.videoProtocolType"-->
          <!--              style="width: 100%"-->
          <!--              size="small"-->
          <!--              @change="addConfigInfo"-->
          <!--              :getPopupContainer="() => propScrollBox"-->
          <!--            >-->
          <!--              <a-select-option value="RTSP">RTSP</a-select-option>-->
          <!--              <a-select-option value="GB28181">GB/T28181-2016</a-select-option>-->
          <!--            </a-select>-->
          <!--          </a-form-item>-->
          <!-- ----------------------------------接入协议-------------------------------- -->

          <a-form-item label="接入协议" name="cameraCtrlProtocol">
            <a-select
              v-model:value="confFrom.cameraCtrlProtocol"
              style="width: 100%"
              size="small"
              @change="changeManufacturer"
              :getPopupContainer="() => propScrollBox"
            >
              <a-select-option value="ONVIF">ONVIF</a-select-option>
              <a-select-option value="SDK_HIK">海康控制协议</a-select-option>
              <a-select-option value="SDK_DH">大华控制协议</a-select-option>
              <a-select-option value="SDK_YS">宇视控制协议</a-select-option>
              <!--              <a-select-option value="VIDEO_RTSP">RTSP视频流</a-select-option>-->
            </a-select>
          </a-form-item>

          <a-form-item label="类型" name="confCameraType">
            <a-select
              v-model:value="confFrom.confCameraType"
              style="width: 100%"
              size="small"
              @change="addConfigInfo"
              :getPopupContainer="() => propScrollBox"
            >
              <a-select-option value="PTZ_CAMERA">球机</a-select-option>
              <a-select-option value="TUBE_CAMERA">筒机</a-select-option>
              <a-select-option value="GUN_CAMERA">枪机</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="通道地址" name="linkIp">
            <a-input
              v-model:value="confFrom.linkIp"
              style="width: 100%"
              size="small"
              @change="addConfigInfo"
            ></a-input>
          </a-form-item>
          <a-form-item label="用户名" name="username">
            <a-input
              v-model:value="confFrom.username"
              style="width: 100%"
              size="small"
              @change="addConfigInfo"
            ></a-input>
          </a-form-item>
          <a-form-item label="密码" name="password"
            ><a-input-password
              size="small"
              v-model:value="confFrom.password"
              style="width: 100%"
              @change="addConfigInfo"
            ></a-input-password
          ></a-form-item>
          <a-form-item label="传输协议" name="rtsp" v-if="confFrom.videoProtocolType === 'RTSP'">
            <a-select
              v-model:value="confFrom.sourceProtocol"
              style="width: 100%"
              size="small"
              @change="addConfigInfo"
              :getPopupContainer="() => propScrollBox"
            >
              <a-select-option value="tcp">TCP</a-select-option>
              <a-select-option value="udp">UDP</a-select-option>
            </a-select>
          </a-form-item>
          <a-form-item label="RTSP地址" name="rtsp" v-if="confFrom.videoProtocolType === 'RTSP'">
            <a-input
              v-model:value="confFrom.rtsp"
              style="width: 65%"
              size="small"
              @change="addConfigInfo"
              readonly
            ></a-input>
            <a-button
              @click="getRtspUrl"
              style="width: 33%; margin-left: 2%"
              size="small"
              :loading="rtspParsing"
              :disabled="confFrom.cameraCtrlProtocol === 'VIDEO_RTSP' || isEmpty(confFrom.linkIp)"
              >解析
            </a-button>
          </a-form-item>
          <div v-else>
            <a-form-item label="SIP用户名" name="sipName">
              <a-input
                v-model:value="confFrom.sipName"
                style="width: 100%"
                size="small"
                @change="addConfigInfo"
              ></a-input>
            </a-form-item>
            <a-form-item label="SIP通道ID" name="videoChannelId">
              <a-input
                v-model:value="confFrom.videoChannelId"
                style="width: 100%"
                size="small"
                @change="addConfigInfo"
              ></a-input>
            </a-form-item>
          </div>
          <a-form-item label="厂商" name="manufacturer">
            <a-input
              v-model:value="confFrom.manufacturer"
              style="width: 65%"
              size="small"
              readonly
            ></a-input>
            <a-button
              @click="getManufacturer"
              style="width: 33%; margin-left: 2%"
              size="small"
              :loading="manufacturerLoading"
              >获取
            </a-button>
          </a-form-item>
        </a-form>
        <a-button
          size="small"
          @click="saveCameraConf"
          type="primary"
          style="margin-left: 78.5%"
          :loading="saveConfLoading"
          >保存</a-button
        >
      </div>
      <div class="PTZBox" id="PTZControl">
        <a-divider orientation="left">云台控制</a-divider>
        <div class="controlBox">
          <ptzControlTools :channel-id="activeNodeKey"></ptzControlTools>
        </div>
      </div>
    </div>
  </div>
  <a-modal
    v-model:open="rtspModal"
    title="选择通道"
    @ok="handleRtsp"
    @cancel="handleRtspClose"
    :maskClosable="false"
  >
    <a-table
      :columns="columns"
      :dataSource="rtspDataSource"
      bordered
      rowKey="rtspUrl"
      :scroll="{ y: 620 }"
      :row-selection="rowSelection"
      :pagination="false"
      v-if="rtspDataSource.length"
    ></a-table>
  </a-modal>
</template>

<script lang="ts">
import { useStore } from "vuex";
import { defineComponent, reactive, watch, ref, computed, onMounted } from "vue";
import { message, Modal } from "ant-design-vue";
import type { FormInstance } from "ant-design-vue";
import { PlusOutlined, QuestionCircleOutlined } from "@ant-design/icons-vue";
import { isEmpty, JsonParseFun, JsonStrFun } from "@/common/utils";
import { validateName } from "@/common/rules";
import {
  rtspUrl,
  treeNodeConf,
  videoPathConf,
  PTZContorls,
  PTZStopContorls,
  cameraManufacturer,
} from "@/api/design";
import ptzControlTools from "@/components/ptzControlTools.vue";
import type { TableColumnsType } from "ant-design-vue";

export default defineComponent({
  props: {
    ActiveTabKey: {
      type: String,
      default: "AlProps",
    },
    tagControl: {
      type: Boolean,
      default: true,
    },
  },
  components: {
    PlusOutlined,
    QuestionCircleOutlined,
    ptzControlTools,
  },
  setup(props, { emit }) {
    const store = useStore();
    const activeNodeKey = computed(() => store.state.design.activeTreeKey);
    /**
     * @description 便签控制收起
     */
    const propScrollBox = ref();

    const manufacturerLoading = ref<boolean>(false);

    const contorlActiveKey = ref<string[]>(["attribute"]);

    const toNode = ({ item, key, keyPath }) => {
      const scrollContainer = document.getElementById("propScrollBox");
      // 获取锚点元素
      const anchorElement = document.getElementById(key);
      if (scrollContainer && anchorElement) {
        const elementPosition = anchorElement.offsetTop - (scrollContainer.offsetTop - 70);
        scrollContainer.scrollTo({
          top: elementPosition,
          behavior: "smooth",
        });
      }
    };

    // 节点信息配置
    const propsRef = ref<FormInstance>();
    const propsFrom = reactive({
      propsName: "东#1球机",
      propsDescription: "这是描述",
    });
    // 校验
    const rules = {
      propsName: [{ required: true, validator: validateName, trigger: "change" }],
    };

    const saveNodeConf = () => {
      propsRef.value.validateFields().then(() => {
        const data = {
          label: propsFrom.propsName,
          des: propsFrom.propsDescription,
          id: store.state.design.activeTreeKey,
        };
        treeNodeConf(data).then((res) => {
          if (res.code === 0) {
            message.success("更新成功");
            store.commit("editTreeNode", {
              nodeData: data,
              isNode: "channel",
            });
            store.commit("editallTreeData", {
              label: propsFrom.propsName,
              des: propsFrom.propsDescription,
            });
            clearBaseInfo(data);
          }
        });
      });
    };
    // -----------------------------------------------摄像机信息配置------------------------------------
    const confFrom = reactive({
      videoProtocolType: "RTSP",
      cameraCtrlProtocol: "",
      confCameraType: "",
      sourceProtocol: "TCP",
      linkIp: "",
      rtsp: "",
      sipName: "",
      videoChannelId: "",
      username: "",
      password: "",
      confUrl: "",
      confPeriod: "",
      manufacturer: "",
      channelNum: null,
    });

    // ------------------------------------------------------解析RTSP流地址-------------------------------------

    const rtspParsing = ref<boolean>(false); // 按钮loading
    const rtspModal = ref<boolean>(false); // 弹窗显示
    const handleRtsp = () => {
      if (rowSelection.value.selectedRowKeys.length === 0) {
        message.warning("未选择RTSP地址");
        return;
      }
      confFrom.rtsp = rowSelection.value.selectedRows[0].rtspUrl;
      confFrom.channelNum = rowSelection.value.selectedRows[0].channelNum;
      rtspModal.value = false;
      clearRtsp();
    }; // 弹窗关闭
    const handleRtspClose = () => {
      clearRtsp();
      rtspModal.value = false;
    };
    const clearRtsp = () => {
      // channelSelect.value = "";
      // channelList.value = [];
      // rtspList.value = [];
      // rtspSelect.value = "";
    };
    // const channelSelect = ref<string>(""); // 弹窗选择通道
    // const channelList = ref<any[]>([]); // 通道列表
    // const rtspList = ref<any[]>([]); // RTSP流地址列表
    // const rtspSelect = ref<string>(""); // 弹窗选择RTSP流地址
    const columns: TableColumnsType = [
      {
        title: "通道号",
        dataIndex: "channelNum",
        width: 80,
      },
      {
        title: "RTSP地址",
        dataIndex: "rtspUrl",
      },
    ];

    const rtspDataSource = ref([]);

    const rowSelection = ref({
      type: "radio",
      checkStrictly: false,
      selectedRowKeys: [],
      selectedRows: [],
      onChange: (selectedRowKeys: string[], selectedRows) => {
        console.log(`selectedRowKeys: ${selectedRowKeys}`, "selectedRows: ", selectedRows);
        rowSelection.value.selectedRowKeys = selectedRowKeys;
        rowSelection.value.selectedRows = selectedRows;
        console.log(rowSelection.value);
      },
    });
    const getRtspUrl = () => {
      const data = {
        address: confFrom.linkIp,
        username: confFrom.username,
        password: confFrom.password,
        cameraCtrlProtocol: confFrom.cameraCtrlProtocol,
      };
      rtspParsing.value = true;
      rtspUrl(data).then((res) => {
        if (res.code === 0) {
          // confFrom.rtsp = res.data;
          rtspModal.value = true;
          rtspDataSource.value = Object.values(res.data).flat();
        }
        rtspParsing.value = false;
      });
    };

    // 切换控制协议时清除厂商
    const changeManufacturer = () => {
      console.log("changeManufacturer");
      confFrom.manufacturer = "";
      addConfigInfo();
    };

    watch(
      () => store.getters.getActiveKeyChannelInfo,
      (nv) => {
        // console.log('videoPath=====>', nv);
        if (nv) {
          const newInfo = JsonParseFun(JsonStrFun(nv));
          confFrom.confCameraType = newInfo.deviceType;
          confFrom.videoProtocolType = newInfo.videoProtocolType;
          confFrom.cameraCtrlProtocol = newInfo.cameraCtrlProtocol;
          confFrom.sourceProtocol = newInfo.sourceProtocol;
          confFrom.sipName = newInfo.sipName;
          confFrom.videoChannelId = newInfo.videoChannelId;
          confFrom.linkIp = newInfo.address;
          confFrom.rtsp = newInfo.rtsp;
          confFrom.username = newInfo.username;
          confFrom.password = newInfo.password;
          confFrom.confUrl = newInfo.url;
          confFrom.manufacturer = newInfo.manufacturer;
          confFrom.channelNum = newInfo.channelNum;
          propsFrom.propsName = newInfo.label;
          propsFrom.propsDescription = newInfo.des;
          rtspParsing.value = false;
          clearRtsp();
          if (propsRef.value) {
            propsRef.value.clearValidate();
          }
        }
      },
      { flush: "pre", immediate: true }
    );

    /**
     * 切换控制协议时 需先调接口查询厂商 然后在保存数据到store
     */
    const getManufacturer = () => {
      if (confFrom.linkIp === "" || confFrom.username === "" || confFrom.password === "") {
        message.warn("请先填写IP、用户名和密码");
        return;
      }
      manufacturerLoading.value = true;
      const data = {
        address: confFrom.linkIp,
        username: confFrom.username,
        password: confFrom.password,
        cameraCtrlProtocol: confFrom.cameraCtrlProtocol,
      };
      cameraManufacturer(data).then((res) => {
        if (res.code === 0) {
          confFrom.manufacturer = res.data;
        } else {
          confFrom.manufacturer = "";
        }
        manufacturerLoading.value = false;
      });
    };

    const saveConfLoading = ref<boolean>(false);
    const saveCameraConf = () => {
      const commonData = {
        videoProtocolType: confFrom.videoProtocolType,
        cameraCtrlProtocol: confFrom.cameraCtrlProtocol,
        deviceType: confFrom.confCameraType,
        id: store.state.design.activeTreeKey,
        address: confFrom.linkIp,
        username: confFrom.username,
        password: confFrom.password,
        url: confFrom.confUrl,
        manufacturer: confFrom.manufacturer,
        channelNum: confFrom.channelNum,
      };

      const data =
        confFrom.videoProtocolType === "RTSP"
          ? { ...commonData, rtsp: confFrom.rtsp, sourceProtocol: confFrom.sourceProtocol }
          : { ...commonData, sipName: confFrom.sipName, videoChannelId: confFrom.videoChannelId };

      saveConfLoading.value = true;
      videoPathConf(data).then((res) => {
        saveConfLoading.value = false;
        if (res.code === 0) {
          message.success("更新成功");
          store.commit("editTreeNode", {
            nodeData: data,
            isNode: "channelConfig",
          });
          store.commit("editChannelConfig", data);

          clearConfigInfo(data);
        }
      });
    };

    const timeSchedule = ref(null);
    const showModal = () => {
      timeSchedule.value.showModal();
    };

    // 摄像头控制
    const controlPTZ = (action) => {
      const data = {
        channelId: store.getters.getActiveKeyChannelInfo.id,
        action,
      };
      PTZContorls(data).then((res) => {
        // console.log(`%c移动${res.msg}`, "color:blue");
      });
    };
    const stopContorlPTZ = () => {
      PTZStopContorls(store.getters.getActiveKeyChannelInfo.id).then((res) => {
        if (res.code === 0) {
          // message.success(res.msg);
          // console.log(`%c停止移动${res.msg}`, "color:red");
        }
      });
    };

    // 添加修改且未保存的基础信息
    const addBaseInfo = () => {
      const channelBase = {
        id: store.state.design.activeTreeKey,
        label: propsFrom.propsName,
        des: propsFrom.propsDescription,
      };
      store.commit("addChannelBaseInfo", channelBase);
    };
    // 清除修改且未保存的基础信息
    const clearBaseInfo = (data) => {
      store.commit("deleteChannelBaseInfo", data);
    };
    // 添加修改且未保存的配置信息
    const addConfigInfo = () => {
      const channelConfig = {
        id: store.state.design.activeTreeKey,
        deviceType: confFrom.confCameraType,
        address: confFrom.linkIp,
        rtsp: confFrom.rtsp,
        username: confFrom.username,
        password: confFrom.password,
        url: confFrom.confUrl,
        manufacturer: confFrom.manufacturer,
      };
      // console.log(channelConfig, "addConfigInfo");
      store.commit("addChannelConfigInfo", channelConfig);
    };
    const clearConfigInfo = (data) => {
      store.commit("deleteChannelConfigInfo", data);
    };

    onMounted(() => {
      let controlList = ["attribute", "channelConf", "PTZControl"];

      document.getElementById("propScrollBox").addEventListener("scroll", () => {
        const scroll = document.getElementById("propScrollBox").scrollTop;
        const range = controlList.map((item, index) => {
          return document.getElementById(item).offsetTop - 50;
        });

        range.forEach((item, index) => {
          if (scroll > item) {
            contorlActiveKey.value = [controlList[index]];
          }
        });
      });
    });

    return {
      activeNodeKey,
      contorlActiveKey,
      propScrollBox,
      toNode,
      isEmpty,
      propsFrom,
      rules,
      propsRef,
      saveNodeConf,
      rtspParsing,
      getRtspUrl,
      rtspModal,
      columns,
      rtspDataSource,
      rowSelection,
      handleRtsp,
      handleRtspClose,
      confFrom,
      saveConfLoading,
      saveCameraConf,
      showModal,
      timeSchedule,
      controlPTZ,
      stopContorlPTZ,
      addBaseInfo,
      addConfigInfo,
      changeManufacturer,
      getManufacturer,
      manufacturerLoading,
    };
  },
});
</script>

<style scoped>
.title {
  height: 35px;
  line-height: 35px;
  background: #f1f2f4;
  padding: 7px 5px;
  font-weight: bold;
}
#propScrollBox {
  position: relative;
}
.confTrigger {
  float: left;
  font-size: 14px;
  margin-top: 4px;
  cursor: pointer;
}

.propsBox {
  margin-top: 10px;
}

.PTZBox {
  margin-top: 10px;
  position: relative;
}

.controlBox {
  position: relative;
}

.isPTZ_CAMERA {
  font-size: 30px;
  cursor: not-allowed;
  /* pointer-events: none; */
}

.table-item {
  width: 70%;
  table-layout: fixed;
  margin-left: 10%;
}

.table-item > tr {
  height: 40px;
}

.table-item > tr > td {
  text-align: center;
}

.table-PTZ {
  width: 70%;
  table-layout: fixed;
  margin-top: 10px;
  margin-left: 10%;
}

.table-PTZ > tr {
  height: 40px;
}

.table-PTZ > tr > td {
  text-align: center;
}

.PTZicon {
  font-size: 30px;
  cursor: pointer;
}

.CalibrationSelect {
  display: flex;
  width: 100%;
  font-size: 14px;
  margin-top: 10px;
}

.CalibrationTime {
  display: flex;
  width: 100%;
  font-size: 14px;
  margin-top: 10px;
  .timeItem {
    display: flex;
    flex-direction: column;
    gap: 10px;
  }
}

.ZeroCalibrationBox {
  margin-bottom: 20px;
}

.CalibrationImage {
  margin-top: 10px;
  display: flex;
  width: 100%;
}

.CalibrationAddBottom {
  border: 1px solid rgb(196, 196, 196);
  border-radius: 5px;
  cursor: pointer;
  margin-bottom: 5px;
  text-align: center;
}
.CalibrationAddBottom:hover {
  border-color: var(--el-menu-active-color);
}
:deep(.ant-form-item-label > label) {
  font-size: 14px !important;
}

:deep(.ant-form-item) {
  margin-bottom: 5px !important;
}

.tabTitle {
  writing-mode: vertical-lr;
  letter-spacing: 2px;
}

.scrollBox {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  padding: 0px 5px 600px 10px;
}

.scrollBox::-webkit-scrollbar {
  width: 0px;
}
.preview-mask-del {
  color: red;
  font-weight: 600;
}
.timeSelect {
  position: absolute;
  right: 200px;
}
.radioBox {
  display: block;
  margin-top: 10px;
  .radioCard {
    .radioSpan {
      display: inline-block;
      width: 400px;
    }
    .rtspRadio {
      position: absolute;
      top: auto;
      bottom: auto;
    }
  }
}
</style>
