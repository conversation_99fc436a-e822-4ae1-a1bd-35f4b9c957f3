<template>
  <div style="display: flex; height: 100%; flex-direction: column">
    <a-menu
      v-model:selectedKeys="contorlActiveKey"
      mode="horizontal"
      @click="toNode"
      style="width: 100%; height: 40px"
    >
      <a-menu-item key="attribute">
        <span>属性</span>
      </a-menu-item>
      <a-menu-item key="preSetPointConf">
        <span>预置点配置</span>
      </a-menu-item>
      <a-menu-item key="sceneConf">
        <span>场景配置</span>
      </a-menu-item>
      <a-menu-item key="AlgorithmConf">
        <span>算法配置</span>
      </a-menu-item>
      <a-menu-item key="schedulingConf">
        <span>调度配置</span>
      </a-menu-item>
    </a-menu>
    <div class="scrollBox" @scroll="blurSelect" id="propScrollBox">
      <!-- 属性 -->
      <div class="propsBox" id="attribute">
        <a-divider orientation="left">属性</a-divider>
        <a-form
          :model="propsFrom"
          :label-col="{ span: 5 }"
          :wrapper-col="{ span: 18 }"
          autocomplete="off"
          :rules="rules"
          ref="propsRef"
        >
          <a-form-item label="名称" name="propsName">
            <a-input
              v-model:value="propsFrom.propsName"
              style="width: 100%"
              size="small"
              @change="addBaseInfo"
            ></a-input>
          </a-form-item>
          <a-form-item label="描述" name="propsDescription">
            <a-textarea
              v-model:value="propsFrom.propsDescription"
              show-count
              :maxlength="255"
              style="width: 100%"
              @change="addBaseInfo"
              size="small"
              :auto-size="{ minRows: 2, maxRows: 5 }"
              placeholder="将作为提示信息在视频画面叠加显示，请谨慎填写"
            ></a-textarea>
          </a-form-item>
          <a-form-item label="使能" name="propsEnable">
            <a-switch
              v-model:checked="propsFrom.propsEnable"
              size="small"
              @change="addBaseInfo"
            ></a-switch>
          </a-form-item>
          <!-- <a-form-item label="图像源" name="propsSource">
            <a-input
              v-model:value="propsFrom.propsSource"
              style="width: 100%"
              size="small"
              :disabled="true"
            >
            </a-input>
          </a-form-item> -->
          <a-form-item>
            <a-button size="small" @click="saveNodeConf" style="margin-left: 110%">保存</a-button>
          </a-form-item>
        </a-form>
      </div>

      <!--预置点配置  -->
      <div class="PTZBox" id="preSetPointConf">
        <a-divider orientation="left">预置点配置</a-divider>
        <div class="controlBox">
          <ptzControlTools :channel-id="PTZInput.channelId" @move-over="moveStop"></ptzControlTools>
        </div>
        <div class="PTZControlBox">
          <div class="ptzBtnBox">
            <a-button size="small" @click="handelSavePTZ"
              ><a-badge color="#2db7f5" v-if="savePTZ"></a-badge>保存</a-button
            >
            <a-button size="small" @click="backToPTZ">跳转</a-button>
            <!-- <a-button size="small" @click="inputPTZ" :disabled="CameraType !== 'PTZ_CAMERA'"
              >跳转
            </a-button> -->
          </div>
        </div>
      </div>

      <!--场景配置  -->
      <div id="sceneConf">
        <a-divider orientation="left">场景配置</a-divider>

        <div style="display: flex; gap: 10px">
          <a-form-item label="场景匹配">
            <a-switch v-model:checked="matchEnable" @click="changeMatch"></a-switch>
          </a-form-item>
          <!-- <a-form-item label="透视变换">
              <a-switch v-model:checked="matchEnable" @click="changeMatch"></a-switch>
            </a-form-item> -->
        </div>
        <a-button @click="addScene" class="addAlBtn" type="dashed">
          <template #icon>
            <PlusCircleOutlined />
          </template>
          添加场景
        </a-button>

        <div class="sceneItemBox" v-for="(item, index) in scenesList">
          <a-image
            class="sceneImg"
            :src="item.tmpSrc"
            :preview="false"
            style="width: 78px; height: 50px"
            v-if="item.tmpSrc"
          ></a-image>
          <div v-else><a-empty :description="null" :image="simpleImage" /></div>
          <span>{{ item.name }}</span>
          <div class="sceneIconConf">
            <IconFont type="icon-bianji1" @click="addCalibrationModal(item)"></IconFont>
          </div>
          <div class="sceneIconDel">
            <IconFont type="icon-a-Property1shanchu1" @click="deleteScene(item.value)"></IconFont>
          </div>
        </div>
        <!-- <a-radio-group
          v-model:value="sceneVal"
          size="small"
          @change="changeSence"
          style="width: 100%"
        >
          <a-radio-button
            v-for="(item, index) in scenesList"
            :key="index"
            :value="item.value"
            @mouseenter="changeHoverIndex(item.value)"
            @mouseleave="clearHoverIndex"
            :disabled="item.disabled"
            class="sceneBtnItem"
            @dblclick="item.disabled ? null : editSceneName(item)"
          >
            <div v-if="!item.editStatus">
              {{ item.name }}
              <div>
                <CIconFont
                  type="icon-incorrect"
                  @click="deleteScene(item.value)"
                  class="closeIcon"
                ></CIconFont>
              </div>
            </div>
            <div v-else>
              <input
                type="text"
                v-model="item.name"
                class="sceneBtnNameInput"
                @blur="saveSceneName(item)"
              />
            </div>
          </a-radio-button>
        </a-radio-group>

        <div style="display: flex; flex-direction: column" v-if="scenesList.length">
          <a-button
            size="small"
            @click="addCalibrationModal"
            style="width: 100px"
            :loading="iconLoading"
            >编辑场景
          </a-button>
          <div style="width: 256px; height: 144px; margin-top: 10px">
            <a-empty v-if="imgSrc.length === 0" description="暂未选取" :image="simpleImage" />
            <a-image
              v-else
              style="width: 256px; height: 144px"
              :src="imgSrc"
              :preview="false"
            ></a-image>
          </div>
        </div> -->

        <div style="margin-top: 10px">
          <a-button
            size="small"
            @click="editPerspective"
            :disabled="scenesList.length === 0 || scenesList[0].tmpSrc === '' || iconLoading"
            type="primary"
          >
            编辑透视变换
            <CheckCircleOutlined v-if="isPerspective" style="color: #00ff00" />
          </a-button>
        </div>
      </div>

      <!--算法配置  -->
      <div class="AlgorithmBox" id="AlgorithmConf">
        <a-divider orientation="left">算法配置</a-divider>
        <a-button
          @click="addAlNum"
          class="addAlBtn"
          :disabled="scenesList.length === 0"
          type="dashed"
        >
          <template #icon>
            <PlusCircleOutlined />
          </template>
          添加算法
        </a-button>
        <div class="alItemBox">
          <div v-for="(item, index) in alNum" :key="index" class="alBox">
            <a-tag class="alIndex" color="blue"> #{{ index + 1 }}</a-tag>
            <a-button @click="editAlNum(item.currentValue, item)">
              <template #icon>
                <IconFont type="icon-bianji1"></IconFont>
              </template>
              {{ getAlNameById(item.currentValue, item.options) }}
            </a-button>

            <a-tag v-if="item.isApply" color="green" class="alIndex">已配置</a-tag>
            <a-tag v-else color="red" class="alIndex">待配置</a-tag>

            <div class="IconBoxConf">
              <IconFont
                type="icon-a-Property1peizhi"
                @click="debugAl(item.id, item.currentValue, index)"
              ></IconFont>
            </div>
            <div class="IconBoxDel">
              <IconFont type="icon-a-Property1shanchu1" @click="minusAlNum(index)"></IconFont>
            </div>

            <!-- <a-button
              size="small"
              @click="debugAl(item.id, item.currentValue, index)"
              :disabled="scenesList.length === 0"
            >
              <template #icon>
                
              </template>
            </a-button> -->
            <!-- <a-button size="small" @click="minusAlNum(index)" danger> 删除 </a-button> -->
          </div>
        </div>
      </div>

      <!--调度配置  -->
      <div style="height: auto; overflow: hidden" id="schedulingConf">
        <a-divider orientation="left">调度配置</a-divider>
        <a-row style="width: 100%" class="ZeroCalibrationBox">
          <a-col :span="24"
            ><span>前置延时：</span>
            <a-input-number
              style="width: 55%"
              v-model:value="dispatch.preExecuteDelay"
              size="small"
              addon-after="秒"
              :min="0"
              :max="50"
              :precision="0"
              @change="addscheduleConfig"
            ></a-input-number>
          </a-col>
          <a-col :span="24" style="margin-top: 10px"
            ><span>后置延时：</span>
            <a-input-number
              style="width: 55%"
              v-model:value="dispatch.postExecuteDelay"
              size="small"
              addon-after="秒"
              :min="0"
              :max="50"
              :precision="0"
              @change="addscheduleConfig"
            ></a-input-number>
          </a-col>
          <a-col :span="24" style="margin-top: 10px"
            ><span>验证次数：</span>
            <a-input-number
              style="width: 55%"
              v-model:value="dispatch.verificationsNumber"
              size="small"
              addon-after="次"
              :min="1"
              :max="10"
              :precision="0"
              @change="addscheduleConfig"
            ></a-input-number>
          </a-col>
        </a-row>
        <!-- <a-row style="width: 100%; margin-top: 10px">
          <a-col :span="24"
            ><span>失败策略：</span
            ><a-select
              style="width: 55%"
              v-model:value="dispatch.failurePolicy"
              size="small"
              @change="addscheduleConfig"
            >
              <a-select-option value="ALARM">报警</a-select-option>
              <a-select-option value="RECORD">仅记录</a-select-option>
              ></a-select
            ></a-col
          >
        </a-row> -->
        <a-row style="width: 100%; margin-top: 10px">
          <a-col :span="24">
            <a-button size="small" @click="saveDispatch">保存</a-button>
          </a-col>
        </a-row>
      </div>
    </div>
  </div>

  <OutputGlobalVar ref="globalVarModal"></OutputGlobalVar>
  <OutputAlarm ref="outputAlarm" @changeAlarm="changeAlarm"></OutputAlarm>
  <AlgorithmTiling
    ref="alTilingModal"
    @choose-algorithm="chooseAlgorithm"
    @edit-algorithm="changeAlgorithm"
  ></AlgorithmTiling>
  <CalibrationModal
    @submittingEvent="handleSubmittng"
    ref="calibrationModal"
    :channelId="PTZInput.channelId"
    @freshImg="freshImg"
    @cancelLoading="cancelLoading"
  >
  </CalibrationModal>
  <PerspectiveModal
    :isPerspective="isPerspective"
    :imgSrc="imgSrc"
    :fromSquare="fromSquare"
    @perspectiveImg="perspectiveImg"
    @savePerspective="savePerspectiveCoor"
    ref="perspectiveModal"
  >
  </PerspectiveModal>
</template>

<script lang="ts">
import { useStore } from "vuex";
import { defineComponent, reactive, watch, ref, nextTick, onMounted, computed } from "vue";
import { useRoute, useRouter } from "vue-router";
import { Modal, message } from "ant-design-vue";
import type { FormInstance } from "ant-design-vue";
import { JsonParseFun, JsonStrFun, isEmpty } from "@/common/utils";
import { Empty } from "ant-design-vue";
import { CheckCircleOutlined, PlusCircleOutlined } from "@ant-design/icons-vue";
import {
  treeNodeConf,
  getModelConf,
  savePTZApi,
  backToPTZApi,
  addAlgorithm,
  delAlgorithm,
  savePerspective,
  saveSchedule,
  editOutput,
  editSenceName,
  delSence,
  editInstance,
  changeMatchStatus,
  getAlgorithmPic,
} from "@/api/design";
import { algorithmModel } from "@/api/project";
import { validateName } from "@/common/rules";
import CalibrationModal from "@/components/CalibrationModal.vue";
import PerspectiveModal from "@/components/PerspectiveModal.vue";
import OutputGlobalVar from "@/components/OutputGlobalVar.vue";
import OutputAlarm from "@/components/OutputAlarm.vue";
import AlgorithmTiling from "@/components/AlgorithmTiling.vue";
import ptzControlTools from "@/components/ptzControlTools.vue";
import IconFont from "@/components/c-iconfont";

export default defineComponent({
  props: {
    ActiveTabKey: {
      type: String,
      default: "AlProps",
    },
    tagControl: {
      type: Boolean,
      default: true,
    },
  },
  components: {
    CalibrationModal,
    PerspectiveModal,
    ptzControlTools,
    OutputGlobalVar,
    OutputAlarm,
    AlgorithmTiling,
    CheckCircleOutlined,
    PlusCircleOutlined,
    IconFont,
  },
  setup(props, { emit }) {
    const store = useStore();

    /**
     * @description 便签控制收起
     */

    const controlList = ref<any[]>([
      {
        id: "attribute",
        label: "属性",
      },
      {
        id: "preSetPointConf",
        label: "预置点配置",
      },
      {
        id: "sceneConf",
        label: "场景配置",
      },
      {
        id: "AlgorithmConf",
        label: "算法配置",
      },
      {
        id: "schedulingConf",
        label: "调度配置",
      },
    ]);

    const contorlActiveKey = ref<string[]>(["attribute"]);
    const toNode = ({ item, key, keyPath }) => {
      // 获取滚动容器
      const scrollContainer = document.getElementById("propScrollBox");
      // 获取锚点元素
      const anchorElement = document.getElementById(key);
      if (scrollContainer && anchorElement) {
        const elementPosition = anchorElement.offsetTop - scrollContainer.offsetTop;
        scrollContainer.scrollTo({
          top: elementPosition,
          behavior: "smooth",
        });
      }
    };

    // 节点信息配置
    const propsRef = ref<FormInstance>();
    const propsFrom = reactive({
      propsName: "名称",
      propsDescription: "这是描述",
      propsEnable: true,
      propsSource: "SDK",
    });
    // 校验
    const rules = {
      propsName: [
        {
          required: true,
          validator: validateName,
          trigger: "change",
        },
      ],
    };
    const saveNodeConf = () => {
      propsRef.value.validateFields().then(() => {
        const data = {
          label: propsFrom.propsName,
          des: propsFrom.propsDescription,
          id: store.state.design.activeTreeKey,
          available: propsFrom.propsEnable,
        };
        treeNodeConf(data).then((res) => {
          if (res.code === 0) {
            message.success("更新成功");
            store.commit("editTreeNode", {
              nodeData: data,
              isNode: "preset",
            });
            store.commit("editallTreeData", {
              label: propsFrom.propsName,
              des: propsFrom.propsDescription,
            });
            clearBaseInfo(data);
          }
        });
      });
    };
    // ============================================================预置点配置================================================

    const CameraType = ref<string>("");

    const moveStop = (resData) => {
      console.log(resData, "moveStop");
      if (resData) {
        savePTZ.value = true;
      }
    };
    const PTZInput = reactive({
      channelId: "",
    });

    // 跳转ptz值
    // const inputPTZ = () => {
    //   if (isEmpty(PTZInput.Pvalue) || isEmpty(PTZInput.Tvalue) || isEmpty(PTZInput.Zvalue)) {
    //     message.warn("请设置PTZ值");
    //     return;
    //   }
    //   const data = {
    //     id: store.state.design.activeTreeKey,
    //     pan: PTZInput.Pvalue,
    //     tilt: PTZInput.Tvalue,
    //     zoom: PTZInput.Zvalue,
    //   };
    //   accuratePTZ(data).then((res) => {
    //     if (res.code === 0) {
    //       message.success(res.msg);
    //       savePTZ.value = true;
    //     }
    //   });
    // };

    // 保存ptz值
    const savePTZ = ref<boolean>(false);

    const handelSavePTZ = () => {
      const data = {
        id: store.state.design.activeTreeKey,
      };
      savePTZApi(data).then((res) => {
        if (res.code === 0) {
          message.success("操作成功");
          savePTZ.value = false;
        }
      });
    };

    // 回跳ptz值
    const backToPTZ = () => {
      backToPTZApi({ presetId: store.state.design.activeTreeKey }).then((res) => {
        if (res.code === 0) {
          message.success("操作成功");
          savePTZ.value = false;
        }
      });
    };
    //======================================================场景配置======================================================

    const matchEnable = ref<boolean>(true);
    const changeMatch = (checked: boolean, event: Event) => {
      if (checked === false && scenesList.value.length > 1) {
        // message.error('关闭模板匹配时，只能保留一个场景。请修改场景配置后重试!', 3)
        // console.log(scenesList.value,'test');
        sceneVal.value = scenesList.value[0].value;
        imgSrc.value = scenesList.value[0].tmpSrc;
        console.log(scenesList.value[0], "asda");
        scenesList.value.forEach((item, index) => {
          if (index > 0) {
            item.disabled = true;
          }
        });
        // console.log(scenesList.value)
        // matchEnable.value = true;
        // return;
      }
      if (checked) {
        scenesList.value.forEach((item) => {
          item.disabled = false;
        });
      }
      const data = {
        matchEnable: checked,
        presetId: store.state.design.activeTreeKey,
      };
      changeMatchStatus(data).then((res) => {
        if (res.code === 0) {
          message.success("修改成功");
        }
      });
    };

    const scenesList = ref([]);
    const calibrationModal = ref();

    const iconLoading = ref<boolean>(false); // 编辑场景的按钮loading

    // 打开编辑场景弹窗
    const addCalibrationModal = async (scenesItem) => {
      iconLoading.value = true;
      const pic = await getAlgorithmPic(store.state.design.activeTreeKey);
      cancelLoading();
      if (pic.code !== 0) return;
      calibrationModal.value.openCalibrationModal(scenesItem, pic);
      iconLoading.value = true;
    };

    const handleSubmittng = (data) => {
      const index = scenesList.value.findIndex((item) => item.value === data.sence.value);

      // 不能重名
      if (scenesList.value.filter((item) => item.name === data.name).length >= 1) {
        return;
      }
      scenesList.value[index].name = data.name;
    };

    const cancelLoading = () => {
      iconLoading.value = false;
    };
    const sceneVal = ref<string>("scene1"); // 当前选中场景
    const imgSrc = ref<string>(""); // 当前场景图片

    /**
     * 切换场景
     * @param param0 切换场景目标值
     */
    const changeSence = ({ target }) => {
      imgSrc.value = scenesList.value.find((item) => target.value === item.value).tmpSrc;
      // store.commit('changePerspectiveBenchImg', imgSrc.value);
    };
    const perspectiveImg = (val) => {
      imgSrc.value = val;
    };
    // 更新场景图片
    const freshImg = (val) => {
      console.log(val, "freshImg");
      if (Object.keys(val).includes("newId")) {
        scenesList.value.forEach((item) => {
          if (item.value === val.id) {
            item.tmpSrc = val.src;
            item.value = val.newId;
            imgSrc.value = val.src;
            sceneVal.value = val.newId;
            item.benchSrc = val.selectImg;
            item.tmpSquare = val.tmpSquare;
          }
        });
      } else {
        scenesList.value.forEach((item) => {
          if (item.value === val.id) {
            item.tmpSrc = val.src;
            imgSrc.value = val.src;
            item.benchSrc = val.selectImg;
            item.tmpSquare = val.tmpSquare;
          }
        });
      }
      console.log(scenesList.value, "sceneList");
    };
    // 添加场景
    const getSenceNumber = (n = 1) => {
      function getLastTwoChars(str) {
        if (str.length === 0) {
          return ""; // 空字符串的处理
        } else if (str.length === 1) {
          return str; // 如果字符串长度为1，返回整个字符串
        } else {
          return str.slice(-2); // 获取后两位
        }
      }
      if (scenesList.value.length === 0) return "01";

      if (scenesList.value.length - n < 0) return "01";

      const lastName = getLastTwoChars(scenesList.value[scenesList.value.length - n].name);
      if (!isNaN(Number(lastName))) {
        console.log(
          lastName,
          Number(lastName) + 1 < 10 ? "0" + (Number(lastName) + 1) : Number(lastName) + 1,
          "lastName"
        );
        return Number(lastName) + 1 < 10 ? "0" + (Number(lastName) + 1) : Number(lastName) + 1;
      } else {
        return getSenceNumber(n + 1);
      }
    };
    const checkRepeat = (currentName) => {
      // 不能重名
      if (scenesList.value.filter((item) => item.name === currentName).length > 0) {
        return `${currentName}1`;
      } else {
        return currentName;
      }
    };
    const addScene = () => {
      if (!matchEnable.value && scenesList.value.length !== 0) {
        message.warn("当前模板匹配至多有一个场景");
        return;
      }
      if (scenesList.value.length > 19) {
        message.warn("最多添加20个场景");
        return;
      }
      scenesList.value.push({
        name: checkRepeat(`场景${getSenceNumber()}`),
        tmpSrc: "",
        benchSrc: "",
        value: `fakeSceneId${Math.random()}`,
        tmpSquare: [],
      });

      sceneVal.value = scenesList.value[scenesList.value.length - 1].value;
      imgSrc.value = "";
    };
    // 编辑场景名称
    const tempName = ref<string>("");
    const editSceneName = (scene) => {
      const index = scenesList.value.findIndex((item) => item.value === scene.value);
      scenesList.value[index].editStatus = true;
      tempName.value = scenesList.value[index].name;
    };
    const saveSceneName = async (scene) => {
      const index = scenesList.value.findIndex((item) => item.value === scene.value);
      scenesList.value[index].editStatus = false;
      // 校验
      const regex = /^[a-zA-Z0-9\u4e00-\u9fa5_]{1,10}$/;

      if (!regex.test(scenesList.value[index].name)) {
        scenesList.value[index].name = tempName.value;
        message.warn("场景名称只允许为1-10位字母、汉字、数字、下划线组合", 2);
        return;
      }
      // 不能重名
      if (
        scenesList.value.filter((item) => item.name === scenesList.value[index].name).length > 1
      ) {
        scenesList.value[index].name = tempName.value;
        message.warn("场景名称不能重复", 2);
        return;
      }

      //
      if (!scenesList.value[index].value.includes("fake")) {
        const data = {
          id: scenesList.value[index].value,
          name: scenesList.value[index].name,
          presetId: store.state.design.activeTreeKey,
        };
        const saveRes = await editSenceName(data);
        if (saveRes.code === 0) {
          tempName.value = "";
          message.success("修改成功");
        }
      }
    };
    // 删除场景
    const hoverIndex = ref<string>(null); // 鼠标悬浮按钮的index
    const changeHoverIndex = (val) => {
      // 改变悬浮index
      hoverIndex.value = val;
    };
    const clearHoverIndex = () => {
      hoverIndex.value = null;
    };
    const deleteScene = async (value) => {
      const messageBox =
        scenesList.value.length === 1
          ? "删除全部场景会清空算法配置，是否确定删除？"
          : "确定删除该场景?";
      Modal.confirm({
        title: "删除",
        content: messageBox,
        onOk: async () => {
          if (value.includes("fake")) {
            if (!matchEnable.value) {
              if (scenesList.value.length === 1) {
                scenesList.value = scenesList.value.filter((item) => item.value !== value);
                if (scenesList.value.length === 0) {
                  alNum.value.map((item) => {
                    item.isApply = false;
                  });
                }
                return;
              }
              if (scenesList.value.length > 1) {
                scenesList.value = scenesList.value.filter((item) => item.value !== value);
                imgSrc.value = scenesList.value[0].tmpSrc;
                sceneVal.value = scenesList.value[0].value;
                scenesList.value[0].disabled = false;
                console.log(scenesList.value);
              }
              return;
            }
            scenesList.value = scenesList.value.filter((item) => item.value !== value);
            if (scenesList.value.length === 0) {
              imgSrc.value = "";
              sceneVal.value = "";
            } else {
              imgSrc.value = scenesList.value[scenesList.value.length - 1].tmpSrc;
              sceneVal.value = scenesList.value[scenesList.value.length - 1].value;
            }
            // 场景数量为0时，清空算法配置
            if (scenesList.value.length === 0) {
              alNum.value.map((item) => {
                item.isApply = false;
              });
            }
          } else {
            const delData = await delSence(value);
            if (delData.code !== 0) return;

            if (!matchEnable.value) {
              if (scenesList.value.length === 1) {
                scenesList.value = scenesList.value.filter((item) => item.value !== value);
                // 场景数量为0时，清空算法配置
                if (scenesList.value.length === 0) {
                  alNum.value.map((item) => {
                    item.isApply = false;
                  });
                }
                return;
              }
              if (scenesList.value.length > 1) {
                scenesList.value = scenesList.value.filter((item) => item.value !== value);
                imgSrc.value = scenesList.value[0].tmpSrc;
                sceneVal.value = scenesList.value[0].value;
                scenesList.value[0].disabled = false;
                console.log(scenesList.value);
              }
              return;
            }
            scenesList.value = scenesList.value.filter((item) => item.value !== value);
            if (scenesList.value.length === 0) {
              imgSrc.value = "";
              sceneVal.value = "";
            } else {
              imgSrc.value = scenesList.value[scenesList.value.length - 1].tmpSrc;
              sceneVal.value = scenesList.value[scenesList.value.length - 1].value;
            }
            // 场景数量为0时，清空算法配置
            if (scenesList.value.length === 0) {
              alNum.value.map((item) => {
                item.isApply = false;
              });
            }
          }
        },
      });
    };
    const isPerspective = ref<boolean>(false); // 透视变换开关
    const perspectiveModal = ref(null); // 透视变换ref
    const handlePerspective = (val) => {
      // store.commit('changePerspectiveActive', val);
      if (!val.target.checked) {
        const data = {
          presetId: store.state.design.activeTreeKey,
          available: val.target.checked,
        };
        savePerspective(data).then((res) => {
          if (res.code === 0) {
            message.success("关闭成功");
          }
        });
      }
    };
    // 透视变换
    const editPerspective = () => {
      const currentScenes = scenesList.value.find((item) => sceneVal.value === item.value);
      const alApply = alNum.value.findIndex((item) => item.isApply);
      perspectiveModal.value.openPerspective(
        scenesList.value,
        currentScenes,
        fromSquare,
        alApply === -1
      );
    };
    const fromSquare = reactive({
      square: null,
      ratio: 1,
    });
    /**
     * 保存透视变换
     * @param val 透视变换参数
     * @param status 透视变换开关
     * @param ratio 透视变换比例
     * @param isChangePerStatus 是否改变透视变换状态
     */
    const savePerspectiveCoor = (val, status, ratio, isChangePerStatus) => {
      fromSquare.square = val;
      fromSquare.ratio = ratio;
      isPerspective.value = status;
      if (isChangePerStatus) {
        alNum.value.forEach((item) => {
          item.isApply = false;
        });
      }
    };

    const abSelectRefs = ref([]);
    const blurSelect = (e) => {
      // 关闭下拉
      abSelectRefs.value.forEach((item, idx) => {
        if (item) {
          item.blur();
        }
      });
    };

    // --------------------------------------------------------------算法配置-----------------------------------------------
    // 算法平铺弹窗
    const alTilingModal = ref(null);

    //下拉框过滤
    const filterOption = (input, option) => {
      return option.label.toLowerCase().indexOf(input.toLowerCase()) >= 0;
    };

    const getAlNameById = (currentValue, options: any[]) => {
      return options.filter((item) => item.value === currentValue)[0].label;
    };

    const alNum = ref([]);
    const changeAlgorithm = (currentValue, al) => {
      alNum.value.forEach((item) => {
        if (al.id === item.id) {
          item.isApply = false;
          item.currentValue = currentValue;
        }
      });
    };
    const commonOptions = ref<any[]>([]);
    const addAlNum = async () => {
      // if (commonOptions.value.length === 0) {
      //   message.error("当前工程未导入算法");
      //   return;
      // }
      // alNum.value.push({
      //   options: commonOptions.value,
      //   currentValue: commonOptions.value[0].value,
      //   id: null,
      //   isApply: false,
      // });
      const res = await algorithmModel("");
      if (res.code !== 0) return;
      alTilingModal.value.openAlTiling(res.data);
    };
    // 弹窗选择完成
    const chooseAlgorithm = (id) => {
      alNum.value.push({
        options: commonOptions.value,
        currentValue: id,
        id: null,
        isApply: false,
      });
    };
    const editAlNum = async (currentValue, item) => {
      const res = await algorithmModel("");
      if (res.code !== 0) return;
      alTilingModal.value.openAlTiling(res.data, currentValue, "edit", item);
    };

    // const editAlgorithm = (currentValue, currentItem) => {
    //   console.log(currentValue, currentItem);
    //   changeAlgorithm(currentValue, currentItem);
    // };

    // 删除算法
    const minusAlNum = (index) => {
      Modal.confirm({
        title: "删除",
        content: "确定删除该算法？",
        onOk: () => {
          if (alNum.value[index].id !== null) {
            delAlgorithm(alNum.value[index].id).then((res) => {
              if (res.code === 0) {
                saveAlgorithm(store.state.design.activeTreeKey, alNum.value[index].id);
                alNum.value = alNum.value.filter((item, i) => index !== i);
                message.success("删除成功", 2);
              }
            });
          } else {
            alNum.value = alNum.value.filter((item, i) => index !== i);
            message.success("删除成功", 2);
          }
        },
      });
    };

    const algorithmInstanceId = ref<string>("");
    const router = useRouter();
    let newWindows = null;
    const debugAl = (id, value, index) => {
      // const imgSrc = scenesList.value.find((item) => item.value === sceneVal.value).tmpSrc;
      if (newWindows) {
        newWindows.close();
      }
      if (scenesList.value.length === 0 || imgSrc.value === "") {
        message.error("请先添加场景并配置场景图片");
        return;
      }
      const data = {
        algorithmId: value,
        presetId: store.state.design.activeTreeKey,
        id,
      };
      addAlgorithm(data)
        .then((res) => {
          if (res.code === 0) {
            alNum.value[index].id = res.data.id;
            algorithmInstanceId.value = res.data.id;
            // 获取预置点名称
            const presetName = computed(() => store.state.design.TreeKeyAggregate).value.filter(
              (item) => {
                return item.id === data.presetId;
              }
            )[0];
            const url = router.resolve({
              name: "designEditor",
              query: {
                algorithmId: value,
                algorithmInstanceId: res.data.id,
                presetId: data.presetId,
                presetName: presetName.label,
                channelName: presetName.presetDetailInfo.channelName,
                algorithmCode: res.data.code,
                channelId: presetName.presetDetailInfo.channelId,
                title: res.data.name,
              },
            });

            newWindows = window.open(url.href);
          }
        })
        .then(() => {
          getModelConf(store.state.design.activeTreeKey).then((res) => {
            if (res.data.algorithmSelectedList) {
              alNum.value = res.data.algorithmSelectedList.map((item, index) => ({
                options: commonOptions.value,
                currentValue: item.algorithmId,
                id: item.id,
                isApply: item.hasSaveConfig,
              }));
            }

            if (res.data.algorithmInstanceOutputList) {
              outputList.value = res.data.algorithmInstanceOutputList;
            }
          });
        });
    };
    const getMessage = new BroadcastChannel("finish_configuration");
    getMessage.onmessage = (event) => {
      console.log(event.data);
      saveAlgorithm(event.data.presetId, event.data.algorithmInstanceId);
    };
    /**
     * --------------------------------------------------------------调度配置------------------------------------------
     */
    const dispatch = reactive({
      preExecuteDelay: "",
      postExecuteDelay: "",
      verificationsNumber: "",
      failurePolicy: "",
    });
    const saveDispatch = () => {
      const data = {
        failureStrategy: dispatch.failurePolicy,
        presetId: store.state.design.activeTreeKey,
        preExecuteDelay: dispatch.preExecuteDelay,
        postExecuteDelay: dispatch.postExecuteDelay,
        validTimes: dispatch.verificationsNumber,
      };
      saveSchedule(data).then((res) => {
        if (res.code === 0) {
          message.success("更新成功");
          store.commit("editPresetSchedule", data);
          clearscheduleConfig(data);
        }
      });
    };

    /**
     * 输出配置
     */
    const outputList = ref<any[]>([]);
    const globalVarModal = ref(null); // 全局变量弹窗dom
    const outputAlarm = ref(null); // 报警弹窗
    // 保存算法更新输出列表
    const saveAlgorithm = (val, algorithmInstanceId) => {
      alNum.value.forEach((item) => {
        if (algorithmInstanceId === item.id) {
          item.isApply = true;
        }
      });
      getModelConf(val).then((res) => {
        // store.commit('pushtreeKey', treeConf);
        // store.commit('setActiveTree', treeNode.id);
        if (res.data.algorithmInstanceOutputList) {
          outputList.value = res.data.algorithmInstanceOutputList;
        }
      });
    };
    // 打开弹窗全局变量
    const openGlobalVarModal = (name, id) => {
      globalVarModal.value.openModal(name, id);
    };
    // 打开报警弹窗
    const openAlarmModal = (id) => {
      outputAlarm.value.getAlarmList(id);
    };
    // 改变报警内容
    const changeAlarm = (val, count) => {
      outputList.value.forEach((item) => {
        if (item.algorithmInstanceId === val && count) {
          item.isBindAlarm = true;
        }
        if (item.algorithmInstanceId === val && !count) {
          item.isBindAlarm = false;
        }
      });
    };
    // 保存
    const changeOutput = (id) => {
      editOutput(outputList.value).then((res) => {
        if (res.code === 0) {
          message.success("修改成功");
        }
      });
    };
    // 添加修改且未保存的基础信息
    const addBaseInfo = () => {
      const presetBase = {
        id: store.state.design.activeTreeKey,
        label: propsFrom.propsName,
        des: propsFrom.propsDescription,
      };
      store.commit("addpresetBaseInfo", presetBase);
    };
    // 清空修改且未保存的基础信息
    const clearBaseInfo = (data) => {
      store.commit("deletepresetBaseInfo", data);
    };
    const addscheduleConfig = () => {
      const scheduleConfig = {
        failureStrategy: dispatch.failurePolicy,
        presetId: store.state.design.activeTreeKey,
        preExecuteDelay: dispatch.preExecuteDelay,
        postExecuteDelay: dispatch.postExecuteDelay,
        validTimes: dispatch.verificationsNumber,
      };
      store.commit("addscheduleConfigModel", scheduleConfig);
    };
    const clearscheduleConfig = (data) => {
      store.commit("clearscheduleConfigModel", data);
    };

    /**
     * 通知配置
     */
    const noticeValue = ref<string[]>([]);
    const plainOptions = [
      {
        label: "语音报警",
        value: "0",
      },
      {
        label: "钉钉报警",
        value: "1",
      },
      // {label:'微信',value:'2'}
    ];
    const changeNotice = () => {
      handleNotice();
    };
    // 提交通知配置
    const handleNotice = () => {
      const notice = [
        {
          noticeType: 0,
          sure: noticeValue.value.includes("0"),
        },
        {
          noticeType: 1,
          sure: noticeValue.value.includes("1"),
        },
      ];
      const data = {
        id: store.state.design.activeTreeKey,
        notice: JsonStrFun(notice),
      };
      editInstance(data).then((res) => {
        if (res.code === 0) {
          message.success("更新成功");
        }
      });
    };
    // 初始化配置参数
    watch(
      () => store.getters.getActiveKeyModelModelInfo,
      (nv) => {
        if (!nv || !(nv.typeNode === "preset")) {
          return;
        }

        if (nv) {
          // ---------------------------属性初始化-----------------------------
          propsFrom.propsName = nv.label;
          propsFrom.propsDescription = nv.des;
          propsFrom.propsEnable = nv.presetDetailInfo.available;
          propsFrom.propsSource = nv.presetDetailInfo.channelName;

          // ---------------------------PTZ初始化------------------------------
          CameraType.value = nv.presetDetailInfo.cameraType;
          PTZInput.channelId = nv.presetDetailInfo.channelId;
          savePTZ.value = false;
          if (propsRef.value) {
            propsRef.value.clearValidate();
          }
          // ---------------------------场景初始化-------------------------------
          cancelLoading();
          matchEnable.value = nv.presetDetailInfo.matchEnable;
          if (nv.sceneList.length !== 0) {
            scenesList.value = nv.sceneList.map((item, index) => ({
              name: item.name,
              benchSrc: item.benchPic,
              tmpSrc: item.tmpPic,
              value: item.id,
              tmpSquare: item.tmpSquare,
              editStatus: false,
            }));
            scenesList.value.forEach((item, index) => {
              if (index === 0) {
                item.disabled = false;
              }
              if (index !== 0 && !matchEnable.value) {
                item.disabled = true;
              }
              if (index !== 0 && matchEnable.value) {
                item.disabled = false;
              }
            });
            sceneVal.value = nv.sceneList[0].id;
            imgSrc.value = nv.sceneList[0].tmpPic;
          } else {
            scenesList.value = [];
            sceneVal.value = "";
            imgSrc.value = "";
          }
          if (nv.correctParam) {
            isPerspective.value = nv.correctParam.available;
          } else {
            isPerspective.value = false;
          }

          isPerspective.value = nv.correctParam ? nv.correctParam.available : false;

          if (nv.correctParam) {
            if (nv.correctParam.available) {
              const square = nv.correctParam.fromSquare;
              fromSquare.ratio = nv.correctParam.ratio;
              fromSquare.square = square;
            }
          } else {
            fromSquare.ratio = 1;
            fromSquare.square = null;
          }

          // ---------------------------------------算法配置初始化-------------------------------------------

          commonOptions.value = nv.algorithmAllList.map((item, index) => ({
            value: item.id,
            label: item.name,
          }));
          alNum.value = nv.algorithmSelectedList.map((item, index) => ({
            options: commonOptions.value,
            currentValue: item.algorithmId,
            id: item.id,
            isApply: item.hasSaveConfig,
          }));
          // ---------------------------------------调度配置初始化--------------------------------------------

          if (nv.scheduleConfigInfo) {
            dispatch.preExecuteDelay = nv.scheduleConfigInfo.preExecuteDelay;
            dispatch.postExecuteDelay = nv.scheduleConfigInfo.postExecuteDelay;
            dispatch.verificationsNumber = nv.scheduleConfigInfo.validTimes;
            dispatch.failurePolicy = nv.scheduleConfigInfo.failureStrategy;
          }
          // 输出配置
          if (nv.algorithmInstanceOutputList) {
            outputList.value = nv.algorithmInstanceOutputList;
          }
          // 通知配置
          if (nv.notice) {
            noticeValue.value = [];
            nv.notice.forEach((item) => {
              if (item.sure) {
                noticeValue.value.push(String(item.noticeType));
              }
            });
          }
        }
      },
      {
        flush: "pre",
        immediate: true,
      }
    );

    // 监听属性栏滚动
    onMounted(() => {
      document.getElementById("propScrollBox").addEventListener("scroll", () => {
        const scroll = document.getElementById("propScrollBox").scrollTop;
        const range = controlList.value.map((item, index) => {
          return document.getElementById(item.id).offsetTop - 40;
        });

        range.forEach((item, index) => {
          if (scroll > item) {
            contorlActiveKey.value = [controlList.value[index].id];
          }
        });
      });
    });

    return {
      // 标签控制
      controlList,
      contorlActiveKey,
      toNode,
      // 属性表单
      propsRef,
      propsFrom,
      saveNodeConf,
      rules,
      // PTZ
      CameraType,
      moveStop,
      PTZInput,
      // inputPTZ,
      savePTZ,
      backToPTZ,
      handelSavePTZ,
      // 选取模板
      matchEnable,
      changeMatch,
      addCalibrationModal,
      handleSubmittng,
      calibrationModal,
      iconLoading,
      cancelLoading,
      scenesList,
      sceneVal,
      freshImg,
      changeSence,
      addScene,
      editSceneName,
      saveSceneName,
      hoverIndex,
      changeHoverIndex,
      clearHoverIndex,
      deleteScene,
      imgSrc,
      simpleImage: Empty.PRESENTED_IMAGE_SIMPLE,
      isPerspective,
      perspectiveModal,
      perspectiveImg,
      handlePerspective,
      editPerspective,
      fromSquare,
      savePerspectiveCoor,
      // 算法
      alTilingModal,
      chooseAlgorithm,
      // editAlgorithm,
      alNum,
      filterOption,
      getAlNameById,
      changeAlgorithm,
      abSelectRefs,
      blurSelect,
      addAlNum,
      editAlNum,
      minusAlNum,
      debugAl,
      algorithmInstanceId,
      // 调度配置
      dispatch,
      saveDispatch,
      // 输出配置
      outputList,
      globalVarModal,
      outputAlarm,
      saveAlgorithm,
      openGlobalVarModal,
      openAlarmModal,
      changeAlarm,
      changeOutput,
      // 一键保存
      addBaseInfo,
      addscheduleConfig,
      // 通知
      noticeValue,
      plainOptions,
      changeNotice,
      handleNotice,
    };
  },
});
</script>

<style scoped lang="less">
@import "./style/modelPath.less";
</style>
