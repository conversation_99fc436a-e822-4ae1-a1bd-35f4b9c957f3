<template>
  <div ref="moreContainer" class="box">
    <a-row :gutter="16">
      <a-col :span="8">
        <a-row style="margin-top: 10px">
          <a-col :span="8">
            <a-input
              v-model:value="tableConf.searchInfo"
              allowClear
              placeholder="名称"
              maxLength="16"
              size="small"
            />
          </a-col>
          <a-col :span="12">
            <a-button @click="searchClick" style="margin-left: 5px" type="primary"
              ><template #icon> <SearchOutlined /> </template>搜索</a-button
            >
          </a-col>
          <a-col :span="4">
            <a-button @click="openModal('directory', 'create')" style="margin-left: 40px"
              >添加</a-button
            >
          </a-col>
        </a-row>

        <a-row style="margin-top: 5px">
          <a-col :span="24">
            <a-table
              id="algorithm-table"
              :pagination="false"
              :columns="tableConf.columns"
              :data-source="tableConf.data"
              :scroll="{ y: 680 }"
              :expandedRowKeys="tableConf.expandKeys"
              @expand="onExpand"
              bordered
              :customRow="tableConf.handleClickRow"
              rowKey="id"
              :rowClassName="
                (record, index) => {
                  return record.id === rowIndex ? 'rowBgcolor' : '';
                }
              "
              childrenColumnName="algorithmList"
            >
              <template #emptyText>
                <a-empty description="暂无数据" />
              </template>
              <template #bodyCell="{ column, record }">
                <template v-if="column.dataIndex === 'name' && !record.algorithmList">
                  <div class="algorithm-icon-box">
                    <div v-html="record.icon" class="algorithm-icon1" v-if="record.icon"></div>
                    <div class="algorithm-icon1" v-else>
                      <!-- <img src="../../../../assets/Property 1=通用目标.svg" /> -->
                      <IconFont type="icon-moxingku" class="emIcon"></IconFont>
                    </div>
                    <a-tooltip placement="right">
                      <template #title>
                        {{ record.name }}
                      </template>
                      <span>{{
                        record.name.length > 10 ? record.name.slice(0, 10) + "..." : record.name
                      }}</span>
                    </a-tooltip>
                  </div>
                </template>
                <template v-if="column.dataIndex === 'isBlock'">
                  <span v-if="record.algorithmList"><a-tag color="processing">分类</a-tag></span>
                  <span v-else><a-tag color="warning">算法</a-tag></span>
                </template>
                <template v-if="column.dataIndex === 'operation'">
                  <a-button
                    :disabled="record.isPreset"
                    v-if="!record.algorithmList"
                    @click="tableConf.delAlgorithm(record)"
                    >删除
                  </a-button>
                </template>
              </template>
            </a-table>
          </a-col>
        </a-row>
      </a-col>

      <a-col :span="16">
        <a-row style="margin-top: 10px; border: 1px solid #f0f0f0">
          <a-col :span="24">
            <AlgorithmEdit
              ref="algorithmEditRef"
              :algorithmInfo="editAlgorithmInfo"
              @okClick="updateList"
            ></AlgorithmEdit>
          </a-col>
        </a-row>
      </a-col>
    </a-row>

    <teleport to="body">
      <AlgorithmModal ref="algorithmModal" @okClick="updateList" />
    </teleport>
  </div>
</template>

<script lang="ts">
import { defineComponent, onMounted, reactive, ref, createVNode, computed, nextTick } from "vue";
import { Modal, message } from "ant-design-vue";
import { ExclamationCircleOutlined, UploadOutlined, SearchOutlined } from "@ant-design/icons-vue";
import type { RuleObject, FormInstance } from "ant-design-vue/es/form";

import { JsonStrFun, JsonParseFun, eachTreesNode, OPR_TYPE } from "@/common/utils";
import { algorithmModel, algorithmModelDel } from "@/api/project";
import { downloadByParseBlob } from "@/common/u-http";
import ImgButton from "@/components/ImgButton.vue";
import AlgorithmEdit from "./algorithmEdit.vue";
import AlgorithmModal from "./algorithmModal.vue";
import IconFont from "@/components/c-iconfont";

interface IEditInfo {
  id: string;
  name: string;
  packageFile: string;
  des: string;
  isPreset: boolean;
}

export default defineComponent({
  name: "AlgorithmLibrary",
  components: {
    AlgorithmEdit,
    AlgorithmModal,
    ImgButton,
    SearchOutlined,
    IconFont,
  },
  setup() {
    const algorithmModal = ref(null);
    const algorithmEditRef = ref(null);

    const showModal = ref<boolean>(false);
    const modalTitle = ref<string>("添加节点");
    const confirmLoading = ref<boolean>(false);

    const algorithmFormRef = ref<FormInstance>();

    const tableConf = reactive({
      data: [],
      expandKeys: [],
      columns: [
        {
          title: "名称",
          dataIndex: "name",

          ellipsis: true,
        },
        // {
        //   title: '标识',
        //   dataIndex: 'typeId',
        // },
        {
          title: "类型",
          dataIndex: "isBlock",
          // width: 120,
          width: 110,
        },
        {
          title: "操作",
          dataIndex: "operation",
          width: 110,
        },
      ],
      handleClickRow: (record, index) => {
        if (record.algorithmList) {
          return;
        }
        return {
          onClick: (event) => {
            const edit = {
              id: record.id,
              name: record.name,
              des: record.description,
              packageFile: record.packageFile,
              isPreset: record.isPreset,
              // packageFile: null,
            };
            editAlgorithmInfo.value = edit;
            algorithmEditRef.value.clearError();
            setRowIndex(record.id);
          },
        };
      },
      delAlgorithm: (record) => {
        // eslint-disable-next-line no-restricted-globals
        event.stopPropagation();

        Modal.confirm({
          title: "删除算法",
          content: "是否删除该算法?",
          onOk() {
            algorithmModelDel(record.id).then((res) => {
              if (res.code === 0) {
                message.success("删除成功");
                getFbTypesTreeData(false, record.id);
              }
            });
          },
        });
      },
      searchInfo: "",
    });
    const rowIndex = ref<string>("");
    const setRowIndex = (id) => {
      rowIndex.value = id;
    };

    const editAlgorithmInfo = ref<IEditInfo>(null);

    const openModal = (type, operation) => {
      algorithmModal.value.openModal(type, operation);
    };

    const getFbTypesTreeData = async (isSearch = false, id?: string) => {
      // 如果是查询清空选中
      if (isSearch) {
        editAlgorithmInfo.value = null;
        rowIndex.value = "";
        tableConf.expandKeys = [];
      }
      if (editAlgorithmInfo.value) {
        if (editAlgorithmInfo.value.id === id) {
          editAlgorithmInfo.value = null;
        }
      }

      const res = await algorithmModel(encodeURIComponent(tableConf.searchInfo));
      if (res.code === 0) {
        // 由于后端返回的数据中 缺少id字段 导致展开有误 因此手动添加一个id
        let id = 0;
        tableConf.data = res.data.map((item) => {
          item.name = item.classification;
          item.id = id++;
          if (item.algorithmList) {
            tableConf.expandKeys.push(item.id);
          }
          return item;
        });
        const firstAl = findFirstAlByList(tableConf.data);
        if (firstAl) {
          const edit = {
            id: firstAl.id,
            name: firstAl.name,
            des: firstAl.description,
            packageFile: firstAl.packageFile,
            isPreset: firstAl.isPreset,
            // packageFile: null,
          };
          editAlgorithmInfo.value = edit;
          algorithmEditRef.value.clearError();
          setRowIndex(firstAl.id);
        }
      }
    };

    // 展开行事件
    const onExpand = (expanded, record) => {
      if (expanded) {
        if (!tableConf.expandKeys.includes(record.id)) {
          tableConf.expandKeys.push(record.id);
        }
      } else {
        tableConf.expandKeys = tableConf.expandKeys.filter((key) => key !== record.id);
      }
    };

    const updateList = (val) => {
      getFbTypesTreeData();
    };

    // 找到算法列表的第一个算法节点
    const findFirstAlByList = (List: any[]) => {
      if (List.length === 0) {
        return null;
      }
      const firstAl = List[0].algorithmList[0];
      return firstAl;
    };

    onMounted(() => {
      getFbTypesTreeData();
    });
    // 搜索按钮
    const searchClick = () => {
      getFbTypesTreeData(true);
    };
    // 清空输入框事件
    const clearSearch = (e) => {
      console.log(e.cancelable);
      if (e.cancelable) {
        getFbTypesTreeData(false);
      }
    };

    return {
      algorithmModal,
      algorithmEditRef,
      tableConf,
      showModal,
      modalTitle,
      confirmLoading,
      searchClick,
      clearSearch,
      openModal,
      rowIndex,
      editAlgorithmInfo,
      updateList,
      onExpand,
    };
  },
});
</script>

<style scoped lang="less">
.box {
  position: relative;
}
:deep(.rowBgcolor) td {
  background-color: #e6f7ff;
}
:deep(.rowBgcolor:hover) td {
  background-color: #e6f7ff !important;
}
.algorithm-icon-box {
  display: flex;
  justify-items: center;
  gap: 5px;
}
.algorithm-icon1 {
  width: 24px;
  height: 24px;
  display: inline;
  line-height: 24px;
  color: var(--textColor1);
  font-size: 24px;
  text-align: center;
  svg {
    width: 24px;
    height: 24px;
  }
  img {
    width: 24px;
    height: 24px;
  }
  .emIcon {
    display: inline-block;
  }
}
</style>
