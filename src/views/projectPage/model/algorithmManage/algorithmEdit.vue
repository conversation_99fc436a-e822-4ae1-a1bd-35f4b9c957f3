<template>
  <div class="editTitle" v-show="!algorithmInfo">
    <span class="titleText">算法编辑</span>
  </div>
  <div class="desBox" v-show="algorithmInfo">
    <div class="title">
      <!-- <a-button style="margin: 10px 20px 0 0" @click="nodeConf.handleOk">提交</a-button> -->
      <p>算法编辑</p>
    </div>

    <div style="padding-right: 25px; padding-top: 20px" class="alg_edit_div">
      <a-form
        ref="algorithmFormRef"
        :model="nodeConf.algorithmForm"
        :label-col="labelCol"
        labelAlign="right"
        :rules="rules"
      >
        <a-row>
          <a-col :span="24">
            <a-form-item label="算法名称" name="algorithmName">
              <a-input
                v-model:value="nodeConf.algorithmForm.algorithmName"
                :allowClear="!nodeConf.algorithmForm.isPreset"
                :readOnly="nodeConf.algorithmForm.isPreset"
                style="width: 15%"
                size="small"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <div style="display: flex">
              <a-form-item label="算法文件" name="algorithmfileName">
                <a-input v-model:value="nodeConf.algorithmForm.algorithmfileName" readOnly />
              </a-form-item>
              <a-upload
                :fileList="nodeConf.algorithmForm.algorithmFile"
                :beforeUpload="nodeConf.uploadAlgorithmModal.beforeUpload"
                @remove="nodeConf.uploadAlgorithmModal.remove"
                :showUploadList="false"
                accept=".zip"
                :maxCount="1"
              >
                <a-button
                  style="
                    width: 50px;
                    border: 1px solid #1d41ab;
                    color: #1d41ab;
                    margin-top: 5px;
                    margin-left: 10px;
                  "
                  :disabled="nodeConf.algorithmForm.isPreset"
                  title="上传算法"
                >
                  <UploadOutlined />
                </a-button>
              </a-upload>
              <a-button
                style="
                  width: 50px;
                  border: 1px solid #1d41ab;
                  color: #1d41ab;
                  margin-left: 10px;
                  margin-top: 4px;
                "
                :disabled="nodeConf.algorithmForm.isPreset"
                title="删除已上传算法"
                @click="nodeConf.uploadAlgorithmModal.remove"
                ><DeleteOutlined
              /></a-button>
            </div>
          </a-col>
        </a-row>

        <a-row>
          <a-col :span="24">
            <a-form-item label="算法描述" name="algorithmNameDes">
              <!-- <a-textarea
                v-model:value="nodeConf.algorithmForm.algorithmNameDes"
                :readOnly="nodeConf.algorithmForm.isPreset"
                :maxlength="200"
                showCount
                style="width: 200px;"
              ></a-textarea> -->
              <mavon-editor
                v-model="nodeConf.algorithmForm.algorithmNameDes"
                :defaultOpen="editMode"
                :subfield="false"
                :toolbarsFlag="false"
                :boxShadow="false"
                style="height: 545px; z-index: 500"
                previewBackground="#ffffff"
              />
            </a-form-item>
          </a-col>
        </a-row>
        <a-row>
          <a-col :span="24">
            <a-form-item style="position: relative; padding-left: 90px">
              <a-button
                type="primary"
                @click="nodeConf.handleOk"
                :disabled="nodeConf.algorithmForm.isPreset"
                :loading="nodeConf.loading"
                >提交</a-button
              >

              <a-button
                @click="editDes"
                style="margin-left: 10px"
                v-if="!nodeConf.algorithmForm.isPreset"
                >{{ editMode === "preview" ? "编辑描述" : "取消编辑" }}</a-button
              >
            </a-form-item>
          </a-col>
        </a-row>
      </a-form>
    </div>
  </div>
</template>
<script lang="ts">
import { defineComponent, reactive, ref, watch } from "vue";
import { Form, Modal, message } from "ant-design-vue";
import { UploadOutlined, DeleteOutlined } from "@ant-design/icons-vue";
import type { RuleObject, FormInstance } from "ant-design-vue/es/form";
import { algorithmModelEdit } from "@/api/project";
import { JsonParseFun, JsonStrFun } from "@/common/utils";
// import { validateAlgorithmName } from '@/common/rules';

interface IFileItem {
  uid: string;
  name: string;
  size: number;
  type: string;
}

export default defineComponent({
  components: {
    UploadOutlined,
    DeleteOutlined,
  },
  props: {
    algorithmInfo: {
      type: Object,
      default: null,
    },
  },
  setup(props, ctx) {
    const codeHeight = ref<string>("15vh");
    const showAlgorithmEdit = ref<boolean>(false);
    const algorithmFormRef = ref<FormInstance>();
    const nodeConf = reactive({
      showModal: false,
      loading: false,
      modalTitle: "",
      algorithmForm: {
        algorithmId: "",
        algorithmName: "",
        algorithmfileName: "",
        algorithmNameDes: "",
        isPreset: false,
        algorithmFile: [],
      },

      algorithmFormRef,
      uploadAlgorithmModal: {
        fileList: [],
        beforeUpload(file: IFileItem) {
          const fileSuffix = file.name.split(".").pop();

          if (!["zip", "ZIP"].includes(fileSuffix as any)) {
            message.warn("请上传zip格式文件");
            return;
          }
          if (file.size > 100 * 1024 * 1024) {
            message.warn("文件大小不能超过100M");
            return;
          }
          nodeConf.algorithmForm.algorithmfileName = file.name;
          nodeConf.algorithmForm.algorithmFile = [file];
          console.log(nodeConf.algorithmForm.algorithmFile);
          return false;
        },
        remove(file: IFileItem) {
          nodeConf.algorithmForm.algorithmfileName = "";
          const index = nodeConf.algorithmForm.algorithmFile.indexOf(file);
          const newFileList = nodeConf.algorithmForm.algorithmFile.slice();
          newFileList.splice(index, 1);
          nodeConf.algorithmForm.algorithmFile = newFileList;
        },
      },

      cancel() {
        nodeConf.showModal = false;
        // resetFields();
      },
      handleOk() {
        console.log(nodeConf);
        algorithmFormRef.value.validateFields().then(() => {
          const regex = /(?:^\s+|\s+$)/g;
          const algorithmName = nodeConf.algorithmForm.algorithmName.replace(regex, "");
          const formData = new FormData();
          formData.append("algorithmId", nodeConf.algorithmForm.algorithmId);
          formData.append("algorithmName", algorithmName);
          formData.append("algorithmDes", nodeConf.algorithmForm.algorithmNameDes);

          if (nodeConf.algorithmForm.algorithmFile.length) {
            const targetFile: IFileItem = nodeConf.algorithmForm.algorithmFile[0];
            const upFile = targetFile.size > 0 ? targetFile : null;
            formData.append("algorithmFile", upFile as any);
          }
          nodeConf.loading = true;
          algorithmModelEdit(formData).then((res) => {
            nodeConf.loading = false;
            if (res.code === 0) {
              message.success("更新成功");
              editMode.value = "preview";

              ctx.emit("okClick", {
                type: "edit",
                data: formData,
              });
            }
          });
        });
      },
    });

    watch(
      () => props.algorithmInfo,
      (nv) => {
        if (nv) {
          editMode.value = "preview";
          nodeConf.uploadAlgorithmModal.remove(nodeConf.algorithmForm.algorithmFile[0]);
          nodeConf.algorithmForm.algorithmId = nv.id;
          nodeConf.algorithmForm.algorithmName = nv.name;
          nodeConf.algorithmForm.algorithmfileName = nv.packageFile;
          nodeConf.algorithmForm.algorithmNameDes = nv.des;
          nodeConf.algorithmForm.isPreset = nv.isPreset;
          nodeConf.loading = false;
        }
      },
      { deep: true }
    );

    const validateAlgorithmName = async (_rule: RuleObject, value: string) => {
      if (value === "") {
        // eslint-disable-next-line prefer-promise-reject-errors
        return Promise.reject("名称不能为空");
      }
      const reg: RegExp = /^[\u4e00-\u9fa5a-zA-Z0-9_ ]{0,20}$/;
      if (!reg.test(value)) {
        return Promise.reject("不能包含特殊字符且不能超过20字符");
      }
    };
    const validateAlgorithmfileName = async (_rule: RuleObject, value: any[]) => {
      if (value.length === 0) {
        // eslint-disable-next-line prefer-promise-reject-errors
        return Promise.reject("上传算法包不能为空");
      }
    };

    const rules = {
      algorithmName: [{ required: true, validator: validateAlgorithmName, trigger: "change" }],
      algorithmfileName: [
        { required: true, validator: validateAlgorithmfileName, trigger: "change" },
      ],
    };

    const clearError = () => {
      algorithmFormRef.value.clearValidate();
    };

    /**
     * markdown 编辑器
     */

    const editMode = ref<"preview" | "edit">("preview");

    const editDes = () => {
      if (editMode.value === "preview") {
        editMode.value = "edit";
      } else {
        editMode.value = "preview";
      }
    };

    return {
      nodeConf,
      showAlgorithmEdit,
      algorithmFormRef,
      codeHeight,
      rules,
      labelCol: { style: { width: "90px" } },
      clearError,
      editDes,
      editMode,
    };
  },
});
</script>
<style scoped lang="less">
.title {
  width: 100%;
  text-align: left;
  vertical-align: middle;
  background: #fafafa;
  height: 50px;
}
.title > p {
  font-size: 16px;
  line-height: 50px;
  margin-left: 10px;
}
.alg_edit_div {
  overflow: auto;
  max-height: 755px;
  margin-left: 20px;
}

.editTitle {
  height: 757px;
  width: 100%;
  display: flex;
  justify-content: center;
  .titleText {
    align-items: center;
    display: inline-flex;
  }
}
.desBox {
  padding-bottom: 20px;
  height: 757px;
}
</style>
