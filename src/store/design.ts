const zIndex = 1;
const allTreeData: any[] = [];
const TreeKeyAggregate: any[] = [];
const activeTreeKey: string | null = null;
const loading = false;
const perspectiveimgData = {
  fromSquare: null,
  toSquareWidth: "",
  toSquareHeight: "",
  ratio: null,
  available: false,
  benchImg: "",
};
const projectBaseInfo = null;
const channelBaseInfo: any[] = [];
const channelConfigInfo: any[] = [];
const presetBaseInfo: any[] = [];
const directoryBaseInfo: any[] = [];
const scheduleConfigModel: any[] = [];
const authorizationModalVisible = false;
// 树递归
const recursionTree = (tree: any[], activeKey, label, des) => {
  if (tree.length !== 0) {
    tree.forEach((ele: any) => {
      if (ele.id === activeKey) {
        ele.label = label;
        ele.des = des;
      }
      recursionTree(ele.children, activeKey, label, des);
    });
  }
};
// 树url递归
const urlTree = (tree: any[], activeKey, url) => {
  if (tree.length !== 0) {
    tree.forEach((ele: any) => {
      if (ele.id === activeKey) {
        ele.url = url;
      }
      urlTree(ele.children, activeKey, url);
    });
  }
};
export default {
  state: {
    zIndex,
    allTreeData, // 所有的工程树节点
    TreeKeyAggregate, // 选中树节点的key的集合
    activeTreeKey, // 当前选中的树节点key
    loading, // 全局加载中,
    perspectiveimgData, // 校验后的图片数据
    projectBaseInfo, // 工程基础信息
    channelBaseInfo, // 通道临时保存基础信息
    directoryBaseInfo, // 区域临时保存基础信息
    channelConfigInfo, // 通道临时保存配置信息
    presetBaseInfo, // 预置点临时保存基础信息
    scheduleConfigModel, // 预置点临时保存调度信息
    authorizationModalVisible, // 授权弹窗打开关闭
  },
  mutations: {
    // 设置授权弹窗打开关闭
    setAuthorizationModalVisible(state, visible) {
      state.authorizationModalVisible = visible;
    },
    setzIndex(state) {
      state.zIndex += 1;
    },
    // set该工程所有树的节点
    setallTreeData(state, allTree) {
      state.allTreeData = allTree;
    },
    // 修改树内容
    editallTreeData(state, { label, des }) {
      recursionTree(state.allTreeData, state.activeTreeKey, label, des);
    },
    // 清空当前工程所有信息
    clearProjectAllInfo(state) {

      state.allTreeData = [];
      state.TreeKeyAggregate = [];
      state.activeTreeKey = null;
    },
    // set激活的树节点集合
    pushtreeKey(state, treeData) {
      let flag = false;
      for (let item = 0; item < state.TreeKeyAggregate.length; item++) {
        if (state.TreeKeyAggregate[item].id === treeData.id) {
          state.TreeKeyAggregate[item] = treeData;
          flag = true;
        }
      }
      if (!flag) {
        if(state.TreeKeyAggregate.length > 10){
          (state.TreeKeyAggregate as Array<any>).splice(0,1)
        }
        state.TreeKeyAggregate.push(treeData);
      }
    },
    // 修改当前激活树节点内容
    editTreeNode(state, { nodeData, isNode }) {
      if (
        isNode === "dir" ||
        isNode === "preset" ||
        isNode === "project" ||
        isNode === "channel"
      ) {
        for (const element of state.TreeKeyAggregate) {
          if (element.id === state.activeTreeKey) {
            element.label = nodeData.label;
            element.des = nodeData.des;
          }
        }
      }
      if (isNode === "editChannelConfig") {
        for (const element of state.TreeKeyAggregate) {
          if (element.id === state.activeTreeKey) {
            element.type = nodeData.type;
            element.ip = nodeData.ip;
            element.password = nodeData.password;
            element.url = nodeData.url;
          }
        }
      }
      if (isNode === "web") {
        for (const element of state.TreeKeyAggregate) {
          if (element.id === state.activeTreeKey) {
            element.url = nodeData.url;
          }
        }
      }
    },
    // 修改树中的url
    editTreeUrl(state, url) {
      urlTree(state.allTreeData, state.activeTreeKey, url);
    },
    // 通道保存触发
    editChannelConfig(state, configData) {
      state.channelConfig = configData;
    },
    // 修改预置点的调度信息
    editPresetSchedule(state, scheduleData) {
      for (const element of state.TreeKeyAggregate) {
        if (element.id === scheduleData.presetId) {
          element.scheduleConfigInfo.failureStrategy = scheduleData.failureStrategy;
          element.scheduleConfigInfo.staySecond = scheduleData.staySecond;
          element.scheduleConfigInfo.validTimes = scheduleData.validTimes;
        }
      }
    },
    // set激活树节点属性集合

    // set当前激活的树节点
    setActiveTree(state, activeKey) {
      state.activeTreeKey = activeKey;
    },

    // 删除激活的树节点和树节点的属性
    delTreeKey(state, delKey) {
      state.TreeKeyAggregate = state.TreeKeyAggregate.filter((item) => item.id !== delKey);
    },

    // 更改校准图片数据
    // changePerspectiveimgData(state, {
    //   newHeight1, newWidth1, square, ratio,
    // }) {
    //   state.perspectiveimgData.toSquareHeight = newHeight1;
    //   state.perspectiveimgData.toSquareWidth = newWidth1;
    //   state.perspectiveimgData.fromSquare = square;
    //   state.perspectiveimgData.ratio = ratio;
    // },
    // changePerspectiveActive(state, available: boolean) {
    //   state.perspectiveimgData.available = available;
    // },
    changePerspectiveBenchImg(state, src: string) {
      state.perspectiveimgData.benchImg = src;
    },
    // 清除数据
    clearDesignData(state) {
      state.allTreeData = [];
      state.TreeKeyAggregate = [];
      state.activeTreeKey = null;
    },

    // 添加工程临时保存基本信息
    addProjectBaseInfo(state, projectBase) {
      state.projectBaseInfo = projectBase;
    },
    // 清空工程临时保存基本信息
    clearProjectBaseInfo(state) {
      state.projectBaseInfo = projectBaseInfo;
    },
    // 添加通道临时保存基本信息
    addChannelBaseInfo(state, channelBase) {

      const index = state.channelBaseInfo.findIndex((element) => element.id === channelBase.id);
      state.channelBaseInfo.splice(index, 1, channelBase);
    },
    // 删除已保存的通道信息
    deleteChannelBaseInfo(state, channelBase) {
      state.channelBaseInfo = state.channelBaseInfo.filter(
        (element) => element.id !== channelBase.id
      );
    },

    // 添加通道临时保存基本信息
    addDirectoryBaseInfo(state, directoryBase) {
      const index = state.directoryBaseInfo.findIndex((element) => element.id === directoryBase.id);
      state.directoryBaseInfo.splice(index, 1, directoryBase);
    },
    // 删除已保存的通道信息
    deleteDirectoryBaseInfo(state, directoryBase) {
      state.directoryBaseInfo = state.directoryBaseInfo.filter(
        (element) => element.id !== directoryBase.id
      );
    },

    // 添加通道临时保存配置信息
    addChannelConfigInfo(state, channelConfig) {
      const index = state.channelConfigInfo.findIndex((element) => element.id === channelConfig.id);
      state.channelConfigInfo.splice(index, 1, channelConfig);
    },
    // 删除已保存的配置信息
    deleteChannelConfigInfo(state, channelConfig) {
      state.channelConfigInfo = state.channelConfigInfo.filter(
        (element) => element.id !== channelConfig.id
      );
    },

    // 添加实例临时保存基本信息
    addpresetBaseInfo(state, presetBase) {
      const index = state.presetBaseInfo.findIndex((element) => element.id === presetBase.id);
      state.presetBaseInfo.splice(index, 1, presetBase);
    },
    // 清除实例临时保存基本信息
    deletepresetBaseInfo(state, presetBase) {
      state.presetBaseInfo = state.presetBaseInfo.filter(
        (element) => element.id !== presetBase.id
      );
    },

    // 添加实例临时保存调度信息
    addscheduleConfigModel(state, scheduleConfig) {
      const index = state.scheduleConfigModel.findIndex((element) => scheduleConfig.presetId === element.presetId);
      state.scheduleConfigModel.splice(index, 1, scheduleConfig);
    },
    // 清除实例临时保存调度信息
    clearscheduleConfigModel(state, scheduleConfig) {
      state.scheduleConfigModel = state.scheduleConfigModel.filter(
        (element) => element.presetId !== scheduleConfig.presetId
      );
    },
    // 清空所有信息
    clearAll(state) {
      state.projectBaseInfo = projectBaseInfo;
      state.channelBaseInfo.length = 0;
      state.channelConfigInfo.length = 0;
      state.presetBaseInfo.length = 0;
      state.scheduleConfigModel.length = 0;
      state.directoryBaseInfo.length = 0;
    },
    // 根据 tab-key 清除其关联信息
    clearByTabKey(state, targetKey) {
      state.channelConfigInfo = state.channelConfigInfo.filter(
        (element) => element.id !== targetKey
      );
      state.channelBaseInfo = state.channelBaseInfo.filter((element) => element.id !== targetKey);

      //TODO 这里不知道节点类型，暂时只能根据key唯一性全部清一遍了
      state.presetBaseInfo = state.presetBaseInfo.filter(
        (element) => element.id !== targetKey
      );
      state.scheduleConfigModel = state.scheduleConfigModel.filter(
        (element) => element.presetId !== targetKey
      );

      state.directoryBaseInfo = state.directoryBaseInfo.filter(
        (element) => element.id !== targetKey
      );
    },
    // 全局loading
    onLoading(state, Msg: string) {
      state.loading = true;
      state.loadingMsg = Msg;
    },
    disLoading(state) {
      state.loading = false;
    },
    chengeSelectOneByObj(state, val) {
      state.selectOneByObj = val;
    },
  },
  getters: {
    getActiveKeyNodeInfo(state) {
      if (!state.activeTreeKey) {
        return null;
      }
      return state.TreeKeyAggregate.find((item) => item.id === state.activeTreeKey);
    },
    getActiveKeyChannelInfo(state) {
      if (!state.activeTreeKey) {
        return null;
      }
      
      return state.TreeKeyAggregate.find(
        (item) => item.id === state.activeTreeKey && item.typeNode === "channel"
      );
    },
    getActiveKeyModelModelInfo(state) {
      if (!state.activeTreeKey) {
        return null;
      } 
      return state.TreeKeyAggregate.find(
        (item) => item.id === state.activeTreeKey && item.typeNode === "preset"
      );
    },
    getActiveKeyprojectInfo(state) {
      if (!state.activeTreeKey) {
        return null;
      }
      return state.TreeKeyAggregate.find(
        (item) => item.id === state.activeTreeKey && item.typeNode === "project"
      );
    },
  },
};
