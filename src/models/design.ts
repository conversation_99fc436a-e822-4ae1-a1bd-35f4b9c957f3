// 类型
export interface IGraphic {
  title: string;
  type: string;
  graphic: string;
}

export const DatatypeComponent = {
  number: {
    name: 'a-input-number',
    props: {
      disabled: false, // 需要根据算法参数类型描述设置
      min: '-999999999999999999999999999999', // 需要根据算法参数类型描述设置
      max: '999999999999999999999999999999', // 需要根据算法参数类型描述设置
      precision: 7, // 需要根据算法参数类型描述设置
      step: 0.0000001, // 需要和精度保持一致
      stringMode: true,
      formatter: (v) => v,
      controls: false,
    },
  },
  string: {
    name: 'a-input',
    props: {
      disabled: false, // 需要根据算法参数类型描述设置
      minlength: 0, // 需要根据算法参数类型描述设置
      maxlength: 0, // 需要根据算法参数类型描述设置
    },
  },
  select: {
    name: 'a-select',
    props: {
      disabled: false, // 需要根据算法参数类型描述设置
      options: [],
    },
  },
  POINT: {
    name: 'a-button',
    props: {
      disabled: false, // 需要根据算法参数类型描述设置
      size: 'small',
      style: {
        color: '',
      },
    },
  },
  LINE: {
    name: 'a-button',
    props: {
      disabled: false, // 需要根据算法参数类型描述设置
      size: 'small',
      style: {
        color: '',
      },
    },
  },
  CIRCLE: {
    name: 'a-button',
    props: {
      disabled: false, // 需要根据算法参数类型描述设置
      size: 'small',
      style: {
        color: '',
      },
    },
  },
  SQUARE: {
    name: 'a-button',
    props: {
      disabled: false, // 需要根据算法参数类型描述设置
      size: 'small',
      style: {
        color: '',
      },
    },
  },
  ELLIPSE: {
    name: 'a-button',
    props: {
      disabled: false, // 需要根据算法参数类型描述设置
      size: 'small',
      style: {
        color: '',
      },
    },
  },
  POLYGON: {
    name: 'a-button',
    props: {
      disabled: false, // 需要根据算法参数类型描述设置
      size: 'small',
      style: {
        color: '',
      },
    },
  },
};

// 所有的参数图形集合
export const graphicTypeList = ['SQUARE', 'CIRCLE', 'POLYGON', 'ELLIPSE', 'LINE', 'POINT','LINE_ARROW'];

export const getType = (type,dir?) => {
  switch (type) {
    case 'SQUARE':
      return 'icon-xingzhuang-juxing';
    case 'CIRCLE':
      return 'icon-yuanxingweixuanzhong';
    case 'POLYGON':
      return 'icon-duobianxing';
    case 'ELLIPSE':
      return 'icon-tuoyuanxing';
    case 'LINE_ARROW':
      return 'icon-jiantou_youshang_o';
    case 'LINE':
      return 'icon-zhixian';
    case 'POINT':
      return 'icon-dian';
    default:
      break;
  }
};

// ROI集合
export const RoiList = [
  {
    title: '矩形',
    type: 'icon-xingzhuang-juxing',
    graphic: 'SQUARE',
  },
  {
    title: '圆形',
    type: 'icon-yuanxingweixuanzhong',
    graphic: 'CIRCLE',
  },
  {
    title: '多边形',
    type: 'icon-duobianxing',
    graphic: 'POLYGON',
  },
  {
    title: '椭圆形',
    type: 'icon-tuoyuanxing',
    graphic: 'ELLIPSE',
  },
  {
    title: '直线',
    type: 'icon-zhixian',
    graphic: 'LINE',
  },
  {
    title: '点',
    type: 'icon-dian',
    graphic: 'POINT',
  },
];

export const week = ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday', 'Sunday'];

export const thirtyMinScale = [
  0, 0.5, 1, 1.5, 2, 2.5, 3, 3.5, 4, 4.5, 5, 5.5, 6, 6.5, 7, 7.5, 8, 8.5, 9, 9.5, 10, 10.5, 11,
  11.5, 12, 12.5, 13, 13.5, 14, 14.5, 15, 15.5, 16, 16.5, 17, 17.5, 18, 18.5, 19, 19.5, 20, 20.5,
  21, 21.5, 22, 22.5, 23, 23.5,
];
export const oneHourScale = [
  0, 1, 2, 3, 4, 5, 6, 7, 8, 9, 10, 11, 12, 13, 14, 15, 16, 17, 18, 19, 20, 21, 22, 23, 24,
];
export const twoHourScale = [0, 2, 4, 6, 8, 10, 12, 14, 16, 18, 20, 22, 24];

export const fourHourScale = [0, 4, 8, 12, 16, 20, 24];
