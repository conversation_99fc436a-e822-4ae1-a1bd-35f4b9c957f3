<template>
  <a-layout>
    <a-layout-sider class="left-menu-sider" :width="'10%'" theme="light">
      <div>
        <ImgButton
          v-for="(item, index) in commonBtns"
          :key="index"
          :index="index"
          :icon="item.icon"
          :label="item.label"
          :activeIndex="activeIndex"
          @onClick="menuClick(item.pathName)"
          @onKeyDown="menuClick(item.pathName)"
      />
      </div>
    </a-layout-sider>

    <a-layout-content class="layout-content" theme="light">
      <div class="right-content">
        <router-view />
      </div>
    </a-layout-content>
  </a-layout>
</template>

<script lang="ts">
import { defineComponent, ref, watch, onMounted } from "vue";
import { useRouter, useRoute } from "vue-router";
import ImgButton from "@/components/ImgButton.vue";

export default defineComponent({
  name: "ProjectMonitorLayout",
  components: {
    ImgButton,
  },
  setup() {
    const router = useRouter();
    const route = useRoute();
    const activeName = ref("");
    const activeIndex = ref();
    watch(
      () => route.name,
      (val, oval) => {
        console.log(val, "aaaa");
        initActiveName();
      }
    );
    const commonBtns = [
      {
        icon: "icon-a-Property1suanfashuchujiankong",
        label: "算法输出监控",
        pathName: "monitor_point",
      },
      {
        icon: "icon-a-Property1xitongjiankong",
        label: "系统监控",
        pathName: "monitor_system",
      },
      // {
      //   icon: 'icon-bianyigongcheng',
      //   label: '服务监控',
      //   pathName: 'monitor_service',
      // },
      // {
      //   icon: 'icon-tiaodu',
      //   label: '调度监控',
      //   pathName: 'monitor_scheduling',
      // },

      // {
      //   icon: 'icon-bianyigongcheng',
      //   label: '报警监控',
      //   pathName: 'monitor_alarm',
      // },
      // {
      //   icon: "icon-a-Property1shipinliujiankong",
      //   label: "视频流监控",
      //   pathName: "monitor_SRS",
      // },
    ];
    const menuClick = (pathName) => {
      activeName.value = pathName;
      router.push({ name: pathName });
    };

    const initActiveName = () => {
      commonBtns.forEach((item, index) => {
        if (item.pathName === route.name) {
          activeName.value = route.name as string;
          activeIndex.value = index;
          console.log(activeIndex.value);
        }
      });
    };

    onMounted(() => {
      initActiveName();
    });

    return {
      menuClick,
      commonBtns,
      activeName,
      initActiveName,
      activeIndex,
    };
  },
});
</script>

<style scoped>
@import './style/common-layout.css';
</style>
