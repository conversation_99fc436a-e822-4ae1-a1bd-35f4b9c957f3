<template>
  <a-layout>
    <a-layout-sider class="left-menu-sider" :width="'10%'" theme="light">
      <div>
        <ImgButton
          v-for="(item, index) in commonBtns"
          :key="index"
          :index="index"
          :icon="item.icon"
          :label="item.label"
          :activeIndex="activeIndex"
          @onClick="menuClick(item.pathName)"
          @onKeyPress="menuClick(item.pathName)"
        />
      </div>
    </a-layout-sider>

    <a-layout-content class="layout-content" theme="light">
      <div class="right-content">
        <router-view />
      </div>
    </a-layout-content>
  </a-layout>
  </template>
  
  <script lang="ts">
  import {
    defineComponent, ref, watch, onMounted,
  } from 'vue';
  import { useRouter, useRoute } from 'vue-router';
  import ImgButton from '@/components/ImgButton.vue';
  
  export default defineComponent({
    name: 'ProjectModel',
    components: {
      ImgButton,
    },
    setup() {
      const router = useRouter();
      const route = useRoute();
      const activeName = ref('');
      const activeIndex = ref();
      watch(
        () => route.name,
        (val, oval) => {
          initActiveName(); 
        },
      );
      const commonBtns = [
        {
          icon: 'icon-a-Property1suanfaguanli',
          label: '算法管理',
          pathName: 'algorithm_manage',
        },
        {
          icon: 'icon-a-Property1moxingguanli',
          label: '模型管理',
          pathName: 'model_manage',
        },
      ];
      const menuClick = (pathName) => {
        activeName.value = pathName;
        router.push({ name: pathName });
      };
      const initActiveName = () => {
        commonBtns.forEach((item, index) => {
          if (item.pathName === route.name) {
            activeName.value = route.name as string;
            activeIndex.value = index;
          }
        });
    };
  
      onMounted(() => {
        initActiveName();
        menuClick('algorithm_manage');
      });
  
      return {
        menuClick,
        commonBtns,
        activeName,
        initActiveName,
        activeIndex,
      };
    },
  });
  </script>
  
<style scoped>
@import './style/common-layout.css';
</style>
  