<template>
  <div>
    <div class="appHeader">
      <div class="appTitle">
        <img class="logo" src="../assets/logotitle.png" />
      </div>
      <CardTabs
        :tabs="menus"
        @menuClick="menuClick"
        :activeName="activeName"
        :activeIndex="activeIndex"
      />
      <MenuLogout id="logout" />
    </div>
    <AuthorizationModal :isExpire="false" @closeModal="closeModal" />
    <router-view />
  </div>
</template>

<script lang="ts">
/* eslint-disable camelcase */
import { computed, defineComponent, reactive, ref, watch, onMounted } from "vue";
import { useRoute, useRouter, onBeforeRouteUpdate } from "vue-router";
import { useStore } from "vuex";
import { message, Modal } from "ant-design-vue";

import MenuLogout from "./components/MenuLogout.vue";
import CardTabs from "./components/CardTabs.vue";
import AppHeader from "./components/AppHeader.vue";
import AuthorizationModal from "@/components/AuthorizationModal.vue";

export default defineComponent({
  components: {
    AppHeader,
    CardTabs,
    MenuLogout,
    AuthorizationModal,
    // IconFont,
  },
  name: "ProjectLayout",
  setup() {
    const router = useRouter();
    const route = useRoute();
    const store = useStore();
    const Component = ref<any>("project_design");

    const activeName = ref<any[]>([""]);
    const activeIndex = ref();

    // 关闭授权弹框
    const closeModal = (val) => {
      store.commit("setAuthorizationModalVisible", false);
    };

    let pathNameVal = "";
    const menuClick = (pathName) => {
      let timer = null;
      if ((window as any).g.plat_type === 0) {
        if (!window.localStorage.getItem("hollysysIntelligent")) {
          message.error("未登录系统");
          router.push({ name: "login" });
          return;
        }
      }
      pathNameVal = menus.filter((item) => item.name === pathName.key)[0].value;
      console.log(pathNameVal);
      // 打开查看授权弹框
      if (pathName.key === "viewAuthorization") {
        store.commit("setAuthorizationModalVisible", true);
        return;
      }

      if (pathName) {
        router.push({ name: pathNameVal }); // 返回总览，不需要传 projectId
      }

      if (pathName === "project_design") {
        timer = setTimeout(() => {
          document.body.style["overflow-x"] = "auto";
          document.body.style["overflow-y"] = "hidden";
        }, 0);
      } else {
        document.body.style.overflow = "auto";
      }
    };

    watch(
      () => route.name,
      (val, oval) => {
        initActiveName();
      }
    );

    const menus = reactive([
      {
        label: "工程",
        value: "project_design",
        name: "design",
      },
      {
        label: "配置",
        value: "conf_pointItemConf",
        name: "conf",
      },
      {
        label: "模型",
        value: "algorithm_manage",
        name: "manage",
      },
      {
        label: "监控",
        value: "monitor_point",
        name: "monitor",
      },
      {
        label: "日志",
        value: "operation_log",
        name: "log",
      },
      {
        label: "授权",
        value: "viewAuthorization",
        name: "viewAuthorization",
        // children: [
        //   {
        //     label: "查看授权",
        //     value: "viewAuthorization",
        //     name: "viewAuthorization",
        //   },
        //   {
        //     label: "关于",
        //     value: "project_help",
        //     name: "help",
        //   },
        // ],
      },
    ]);

    const initActiveName = () => {
      let timer = null;
      Component.value = route.name;
      menus.forEach((item, index) => {
        if ((route.name as string).includes(item.name)) {
          activeName.value = [item.name];
          activeIndex.value = index;
        }
      });

      if (route.name === "project_design") {
        timer = setTimeout(() => {
          document.body.style["overflow-x"] = "auto";
          document.body.style["overflow-y"] = "hidden";
        }, 0);
      } else {
        document.body.style.overflow = "auto";
      }
    };

    onMounted(() => {
      initActiveName();
    });

    return {
      menuClick,
      menus,
      activeName,
      initActiveName,
      activeIndex,
      Component,
      closeModal,
    };
  },
});
</script>

<style scoped lang="less">
@import "./style/project.less";


</style>
