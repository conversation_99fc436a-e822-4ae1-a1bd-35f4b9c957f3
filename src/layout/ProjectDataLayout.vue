<template>
  <a-layout>
    <a-layout-sider class="left-menu-sider" :width="'10%'" theme="light">
      <div>
        <ImgButton
          v-for="(item, index) in commonBtns"
          :key="index"
          :index="index"
          :icon="item.icon"
          :label="item.label"
          :activeIndex="activeIndex"
          @onClick="menuClick(item.pathName)"
          @onKeyDown="menuClick(item.pathName)"
        />
      </div>
    </a-layout-sider>

    <a-layout-content class="layout-content" theme="light">
      <div class="right-content">
        <router-view />
      </div>
    </a-layout-content>
  </a-layout>
</template>

<script lang="ts">
import {
  defineComponent, computed, ref, watch, onMounted, reactive,
} from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useStore } from 'vuex';
import ImgButton from '@/components/ImgButton.vue';
export default defineComponent({
  name: 'ProjectDataLayout',
  components: {
    ImgButton,
  },
  setup() {
    const router = useRouter();
    const store = useStore();
    const route = useRoute();
    const activeName = ref('');
    const activeIndex = ref();

    watch(
      () => route.name,
      (val, oval) => {
        initActiveName();
      },
    );
    const commonBtns = [
      {
        icon: 'icon-a-Property1bianliangpeizhi',
        label: '变量配置',
        pathName: 'conf_pointItemConf',
      },
      {
        icon:'icon-a-Property1tiaodupeizhi',
        label:'调度配置',
        pathName:'conf_schedulingConf',
      },
      {
        icon: 'icon-a-Property1baojingpeizhi',
        label: '报警配置',
        pathName: 'conf_alarmConf',
      },
      // {
      //   icon: 'icon-JSON',
      //   label: '通道订阅',
      //   pathName: 'conf_JSON',
      // },
      {
        icon: 'icon-a-Property1kongzhiliupeizhi',
        label: '控制流配置',
        pathName: 'conf_controlFlowConf',
      },
      {
        icon: 'icon-a-Property1jiekoushouquan',
        label: '接口授权',
        pathName: 'conf_interfaceConf',
      },
      // {
      //   icon: 'icon-tupian',
      //   label: '故障图片库',
      //   pathName: 'conf_faultLibrary',
      // },
      {
        icon: 'icon-a-Property1xitongpeizhi',
        label: '系统配置',
        pathName: 'conf_system',
      },

    ];
    const menuClick = (pathName: string) => {
      activeName.value = pathName;
      router.push({ name: pathName });
    };
    const systemPath = ['conf_timeConf','conf_PTZConf','conf_noticeConf','conf_userConf'];
    const selectSystem = ref<boolean>(false);
    const initActiveName = () => {
      let count = 0;
      commonBtns.forEach((item, index) => {
        if (item.pathName === route.name) {
          activeName.value = route.name as string;
          activeIndex.value = index;
          count++
        }
      });
      if(count === 0){
        activeIndex.value = null;
      }
      if(systemPath.includes(route.name as string)){
        selectSystem.value = true;
      }else{
        selectSystem.value = false;
      }
    };

    onMounted(() => {
      initActiveName();
    });

    const trigger = computed(() => store.state.project.trigger);

    return {
      menuClick,
      commonBtns,
      trigger,
      activeName,
      initActiveName,
      activeIndex,
      selectSystem,
    };
  },
});
</script>

<style scoped>
@import './style/common-layout.css';
</style>