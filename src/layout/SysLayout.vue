<template>
  <div class="operatorStyle">
    <!-- <dv-scroll-board :config="config" style="width: 100%; height: 200px" ref="scrollRef" /> -->
    <div class="leftCharts">
      <a-row>
        <a-col :span="8">
          <a-row>
            <!-- <a-col :span="12">
              <div id="LeftChart1-1"></div>
            </a-col>
            <a-col :span="12">
              <div id="LeftChart1-2"></div>
            </a-col> -->
            <div class="topbnt_left fl">
              <ul>
                <li :class="{ active: routeName === 'homePage' }">
                  <a @click="routeSkip('homePage')">首页</a>
                </li>

                <li :class="{ active: routeName === 'splitScreen' }">
                  <a @click="routeSkip('splitScreen')">视频轮播</a>
                </li>
                <li :class="{ active: routeName === 'map' }">
                  
                  <a @click="routeSkip('map')">智能巡检</a>
                </li>
              </ul>
            </div>

          </a-row>
        </a-col>
        <a-col :span="8">
          <h1 class="h1Text">
            <span text="智能监控系统">智能监控系统</span>
          </h1>
        </a-col>
        <a-col :span="8">
            <!-- <dv-scroll-board :config="config" style="width: 100%; height: 200px" ref="scrollRef" /> -->
            <div class="topbnt_left fl" style="margin-left: 42px;">
              <ul>
                <li :class="{ active: routeName === 'configPage' }">
                  <a @click="routeSkip('configPage')">视频列表</a>
                  
                </li>
                <li :class="{ active: routeName === 'historyAlarm' }">
                  <a @click="routeSkip('historyAlarm')">历史报警</a>
                </li>
                <li :class="{ active: routeName === 'inspectionRecords' }">
                  <a @click="routeSkip('inspectionRecords')">巡检记录</a>
                </li>
              </ul>
            </div>
        </a-col>
      </a-row>
    </div>
    <div class="bottomElements">
      <!-- <router-view /> -->
      <a-row :gutter="16">
        <a-col :span="18">
          <div style="height: 768px; width: 100%;">
            <router-view />
          </div>
        </a-col>
        <a-col :span="6">
          <!-- <a @click="startSpeak('您您')">asdas</a> -->
          <div class="chart">
            <div class="elementBody">
              <div class="title">
                <img src="../assets/title1.png" alt="暂无图片"/>
                <span style="font-size: 16px;position: absolute;left: 30px;top: 22px;"><strong>实时报警</strong></span>
              </div>
              <!-- <dv-scroll-board :config="config" style="width: 100%; height: 200px;margin-top: 10px;" ref="scrollRef" /> -->
              <el-table
                :data="tableData"
                ref="tableContainer"
                height="210"
                :header-cell-style="{
                  background: '#00baff',
                  color:'#fff',
                  fontFamily: 'PingFangSC-Regular',
                  fontSize: '14px',
                }"
                row-key="algorithmId"
                :row-class-name = "({row,rowIndex})=>{
                    return rowIndex%2? 'firstRow': 'secondRow'
                }"
                style="margin-top: 10px;"
              >
              <!-- "(row,rowindex)=>{
                  return rowindex % 2 ? 'firstRow':'secondRow'
                }" -->
                <template #empty>
                  <div style="color: white;">暂无报警</div>
                </template>
                <el-table-column
                  prop="startTime"
                  width="160"
                  label="开始时间"
                >
                  <template #default="scope">
                    <!-- <el-button size="small" @click="handleEdit(scope.$index, scope.row)"
                      >确认</el-button
                    > -->
                    <el-tooltip
                      effect="dark"
                      :content="scope.row.startTime"
                      placement="top"
                    >
                      {{ scope.row.startTime}}
                    </el-tooltip>
                  </template>
                </el-table-column>
                <el-table-column
                  prop="name"
                  label="名称"
                  width="120"
                >
                  <template #default="scope">
                    <!-- <el-button size="small" @click="handleEdit(scope.$index, scope.row)"
                      >确认</el-button
                    > -->
                    <el-tooltip
                      effect="dark"
                      :content="scope.row.name"
                      placement="top"
                    >
                      {{ scope.row.name>6 ? scope.row.name.slice(0,6)+'...': scope.row.name}}
                    </el-tooltip>
                  </template>
                </el-table-column>
                <!-- <el-table-column
                  prop="alarmVal"
                  label="报警信息"
                  width="80"
                /> -->
                <el-table-column
                  prop="alarmLevel"
                  label="等级"
                  width="60"
                />
                <el-table-column
                  label="操作"
                  width="90"
                >
                  <template #default="scope">
                    <el-button size="small" type="primary" @click="handleEdit(scope.$index, scope.row)"
                      >确认</el-button
                    >
                  </template>
                </el-table-column>

              </el-table>
            </div>
            <div class="elementBody">
              <div class="title">
                <img src="../assets/title1.png" alt="暂无图片"/>
                <span style="font-size: 16px;position: absolute;left: 30px;top: 287px;"><strong>实时报警趋势</strong></span>
              </div>
              <div id="LeftChart1-1"></div>
            </div>
            <div class="elementBody">
              <div class="title">
                <img src="../assets/title1.png" alt="暂无图片"/>
                <span style="font-size: 16px;position: absolute;left: 30px;top: 532px;"><strong>当日报警前10</strong></span>
              </div>
              <div id="LeftChart1-2"></div>
            </div>
          </div>
        </a-col>
      </a-row>
    </div>
  </div>
</template>

<script lang="ts">
import {
  defineComponent, reactive, ref, watch, onMounted, onBeforeUnmount, onUnmounted
} from 'vue';
import { useRoute, useRouter } from 'vue-router';
import * as echarts from 'echarts';
import dayjs from 'dayjs';
import HWebsocket from '@/common/h-websocket';
import { JsonParseFun, JsonStrFun } from '@/common/utils';
import { get7DayAlarm, get7DayAlarmTop, getOneDayAlarm, comfirmAlarm } from '@/api/operatorStation';

import { ElTable, ElTableColumn } from 'element-plus';
import SetInterval from "@/common/SetInterval";

type EChartsOption = echarts.EChartsOption;
export default defineComponent({
    name: 'SysLayout',
    components:{
      ElTable,
      ElTableColumn
    },
    setup() {
        const echart = echarts;
        const router = useRouter();
        const route = useRoute();
        const routeName = ref<string>(route.name as string);
        /**
         * @description 图像
         */
        let initchart1 = null;
        let initchart2 = null;
        const chartOptions1 = (axisData, seriesData) => {
            const options: EChartsOption = {
                tooltip: {
                    trigger: 'axis',
                },
                grid: {
                    left: 50,
                    right: 50,
                    top: 40,
                    bottom: 30,
                },
                xAxis: {
                    type: 'category',
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(255,255,255,.3)'
                        }
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(255,255,255,.3)'
                        }
                    },
                    axisLabel: {
                        color: 'rgba(255,255,255,.3)'
                    },
                    data: axisData,
                    axisPointer: {
                        type: 'shadow'
                    }
                },
                yAxis: {
                    type: 'value',
                    axisLine: {
                        lineStyle: {
                            color: 'rgba(255,255,255,.3)',
                        },
                    },
                    splitLine: {
                        lineStyle: {
                            color: 'rgba(255,255,255,.3)',
                        },
                    },
                    axisLabel: {
                        formatter: '{value}',
                    },
                },
                // dataZoom:[
                //   {
                //     type:'inside',
                //     start: seriesData.length,
                //     end: seriesData.length - 6
                //   },
                //   {
                //     start: seriesData.length,
                //     end: seriesData.length - 6
                //   }
                // ],
                series: [
                    {
                        type: 'line',
                        name: '该时间段报警数量',
                        itemStyle: {
                            color: '#7cffb2',
                        },
                        data: seriesData,
                    },
                ]
            };
            initchart1.setOption(options);
        };
        const chartOptions2 = (axisData, data) => {
            const options: EChartsOption = {
                backgroundColor: 'rgba(1,202,217,.0)',
                tooltip: {
                    trigger: 'item',
                    axisPointer: {
                        type: 'shadow',
                    },
                    confine: true,
                },
                grid: {
                    left: 0,
                    right: 20,
                    bottom: 0,
                    top: 20,
                    containLabel: true,
                },
                xAxis: {
                    type: 'value',
                    boundaryGap: [0, 0.01],
                },
                yAxis: {
                    type: 'category',
                    data: axisData,
                    inverse: true,
                    nameTextStyle: {
                        color: '#fff'
                    },
                    axisLabel: {
                        show: true,
                        interval: 0,
                        formatter: function (value) {
                            if (value.length > 8) {
                                return value.substring(0, 8) + "...";
                            }
                            else {
                                return value;
                            }
                        }
                    }
                },
                series: [
                    {
                        type: 'bar',
                        data: data,
                    },
                ],
            };
            initchart2.setOption(options);
        };
        let wsChart = null;
        function openWsChart() {
            wsChart = new HWebsocket(`${(window as any).g.wsApi}/websocket/statistical`);
            wsChart.onMessage = (evt) => {
                const wsData = JSON.parse(evt.data);
                getData(wsData);
            };
        }
        function closeWsChart() {
            if (wsChart) {
                wsChart.close();
                wsChart = null;
            }
        }
        const axisData1 = ref<string[]>([]);
        const data1 = ref<number[]>([]);
        const getData = (wsData) => {
            // 实时报警趋势
            if (axisData1.value.length > 20) {
                axisData1.value.shift();
            }
            if (data1.value.length > 20) {
                data1.value.shift();
            }
            axisData1.value.push(Object.keys(wsData.realAlarmCount)[0].slice(11));
            data1.value.push(Object.values(wsData.realAlarmCount)[0] as number);
            chartOptions1(axisData1.value, data1.value);
            // 当日报警前10
            const axisData2 = wsData.toDayTop10.map((item) => {
                return `${item.algorithmName}`;
            });
            const data2 = wsData.toDayTop10.map((item, index) => {
                if (index < 3) {
                    return {
                        value: item.alarmCount,
                        name: `${item.channelName}_${item.presetName}_${item.algorithmName}`,
                        itemStyle: {
                            color: '#a90000'
                        }
                    };
                }
                return {
                    name: `${item.channelName}_${item.presetName}_${item.algorithmName}`,
                    value: item.alarmCount
                };
            });
            chartOptions2(axisData2, data2);
        };
        // 滚动表格
        // const scrollRef = ref(null);
        // const config = {
        //   header: ['开始时间', '名称', '报警信息', '报警条件'],
        //   data: [],
        // };
        // function updateTable(row, index?) {
        //   const newRow = JsonParseFun(JsonStrFun(row));
        //   newRow.forEach((item)=>{
        //     item.splice(2,1)
        //   })
        //   scrollRef.value.updateRows(newRow);
        // }
        const tableData = ref([]);
        const handleEdit = (index, row) => {
          const data = {
            id:row.alarmId,
            confirm: true
          }
          comfirmAlarm(data).then((res)=>{
            if(res.code === 0){
              tableData.value = tableData.value.filter((item)=> item.alarmId !== row.alarmId)
            }
          })
        };
        // 滚动
        const scroll = () => {
          let isScroll = true //滚动
          const tableDom = document.getElementsByClassName('el-scrollbar__wrap')[0];

          //鼠标放上去，停止滚动；移开，继续滚动
          tableDom.addEventListener('mouseover', () => {
            isScroll = false
          })
          tableDom.addEventListener('mouseout', () => {
            isScroll = true
          })

          setInterval(() => {
            if (isScroll) {
              tableDom.scrollTop += 42.5 //设置滚动速度
              if (tableDom.clientHeight + tableDom.scrollTop == tableDom.scrollHeight) {
                tableDom.scrollTop = 0
                // tableData.value.push();
                // if(tableData.value.length >= realTableData.length * 2){
                //   tableData.value.slice(realTableData.length);
                //   tableData.value.concat(tableData.value)
                // }else{
                //   tableData.value = tableData.value.concat(tableData.value)
                // }

                // console.log(tableData.value)
                // console.log(tableData.value);
                // if(tableData.value.length >= realTableData.length * 2){

                // }
              }
            }
          }, 2000)
        }
        // 语音播报
        const speakMsg = new SpeechSynthesisUtterance();
        function startSpeak(msg: string) {
            speakMsg.text = msg;
            speakMsg.volume = 100;
            speakMsg.pitch = 0.5;
            speakMsg.rate = 1;
            window.speechSynthesis.speak(speakMsg);
        }
        // 初始化ws
        const dataList = ref<string[][]>([]);
        let ws = null;
        function openWs() {
            // drawGraph(fakedata);
            ws = new HWebsocket(`${(window as any).g.wsApi}/websocket/alarm`);
            ws.onMessage = (evt) => {
                const wsData = JSON.parse(evt.data);
                todoWsMessage(wsData);
            };
        }
        function todoWsMessage(data) {
          if (tableData.value.length !== 0) {
              const findIndex = tableData.value.findIndex((item) => item.algorithmId === data.algorithmId)
              if(findIndex >= 0){
                 tableData.value[findIndex] = data;
              }else{
                 tableData.value.push(data);
              }
          } else {
              tableData.value.push(data);
          }
          startSpeak(data.alarmVoice);
        }
        function closeWs() {
          if (ws) {
              console.log('关闭ws');
              ws.close();
              ws = null;
          }
        }
        onMounted(() => {
            initchart1 = echart.init(document.getElementById('LeftChart1-1'));
            initchart2 = echart.init(document.getElementById('LeftChart1-2'));
            bodyStyle();
            openWs();
            openWsChart();
            scroll();
            SetInterval.close('loginTimer'); // 关闭全局定时器
        });
        onUnmounted(() => {
            closeWs();
            closeWsChart();
        });
        // const mycharts = echart.init(document.getElementById('chart1'));
        const bodyStyle = () => {
            document.body.style.overflowX = 'auto';
            document.body.style.overflowY = 'hidden';
        };
        // 路由跳转
        const routeSkip = (val) => {
            switch (val) {
                case 'homePage':
                    router.push({ name: val });
                    routeName.value = 'homePage';
                    break;
                case 'splitScreen':
                    router.push({ name: val });
                    routeName.value = 'splitScreen';
                    break;
                case 'configPage':
                    router.push({ name: val });
                    routeName.value = 'configPage';
                    break;
                case 'historyAlarm':
                    router.push({ name: val });
                    routeName.value = 'historyAlarm';
                    break;
                case 'map':
                    router.push({ path: '/map', query: { width: 960, height: 540, osd: 'true' } });
                    // const url = router.resolve({
                    //   name: 'DCSPopup',
                    // });
                    // window.open(url.href);
                    routeName.value = 'map';
                    break;
                case 'inspectionRecords':
                    router.push({ name: val });
                    routeName.value = 'inspectionRecords';
                    break;
                default:
                    break;
            }
        };
        return {
            initchart1,
            // scrollRef,
            // config,
            tableData,
            handleEdit,
            routeName,
            routeSkip,
            startSpeak,
        };
    },
});
</script>

<style scoped lang="scss">
.operatorStyle {
  color: #fff;
  background: #030a20 url(../assets/background.png);
  background-size: 100% 100%;
  background-position: 0 0;
  width: 100% !important;
  height: 100% !important;
  background-repeat: no-repeat;
  overflow: hidden;
}
.leftCharts {
  width: 100%;
  /* height: 200px; */
  /* padding: 10px; */
}
/* 报警趋势图 */
#LeftChart1-2 {
  height: 200px;
}
#LeftChart1-1 {
  height: 200px;
}

.h1Text {
  height: 100px;
  text-align: center;
  font-size: 30px;
  font-weight: bold;
  letter-spacing: 8px;
  padding-top: 16px;
}
.h1Text span {
  text-shadow: 2px 3px 1px black;

  color: #fff;

}
.h1Text span::before{

  content: attr(text);
  position: absolute;
  z-index: 10;
  /*覆盖文字的颜色*/
  color: #1AB5FE;
  -webkit-mask: linear-gradient(to top,#1AB5FE, transparent);
}
.middleBtn {
  height: 100px;
}
.fl {
  float: left;
}
.fr {
  float: right;
}
.topbnt_left {
  width: 100%;
  height: 40px;
  margin-top: 80px;
  /* margin-left: 8px; */
}
.topbnt_left ul {
  /* padding-top: 40px; */
  width: 100%;
  list-style-type: none;
}
.topbnt_left li {
  background: url(../assets/border2.png) center;
  font-size: 16px;
  /* margin-top: 20px; */
  line-height: 33px;
  background-repeat: no-repeat;
  width: 150px;
  height: 40px;
  float: left;
  margin-left: 17px;
  text-align: center;
}
.topbnt_left li.active,
.topbnt_right li.active {
  background: url(../assets/border3.png) no-repeat center;
}
.topbnt_left li a {
  text-decoration: none;
  color: #fff;
}

.bottomElements{
  height: 80%;
  margin-top: 10px;
  margin-left: 55px;
  margin-right: 55px;
}
.chart{
  display: flex;
  flex-direction: column;
  height: 768px;
  width: 100%;
  background-color: #040f2f;
}
.elementBody{
  margin-left: 10px;
  margin-top: 20px
}
.title{
  /* background-image: url('../assets/title1.png');
  background-repeat: no-repeat; */
  font-size: 16px;
}

:deep(.el-scrollbar__wrap){
  scroll-behavior: smooth;
}
:deep(.firstRow){
  background-color: #0a2732  !important;
  color: #fff;
}
:deep(.el-table--enable-row-hover .el-table__body tr:hover>td.el-table__cell) {
  background-color: transparent !important;
}
:deep(.secondRow){
  background-color: #003b51;
  color: #fff;
}
:deep(.el-table){
  background-color: transparent !important;
}
:deep(.el-table td.el-table__cell, .el-table th.el-table__cell.is-leaf){
  border-bottom:0px;

}

</style>
