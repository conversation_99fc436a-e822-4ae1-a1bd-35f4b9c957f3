<template>
  <div class="app-header">
    <img class="logo" src="/m7s.png" />
    <span class="text">智能视觉分析与监控系统</span>
  </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue';

export default defineComponent({
  components: {},
  setup() {},
});
</script>

<style scoped>
.app-header {
  display: flex;
  align-items: center;
  width: 100%;
  background: #0d141f;
}
.text {
  font-size: 14px;
  color: #fff;
  margin-left: 8px;
}
.logo {
  width: 18px;
}
</style>
