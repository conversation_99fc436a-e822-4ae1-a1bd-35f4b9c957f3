<template>
  <div>
    <a-dropdown class="userLogout">
      <span>
        <IconFont style="font-size: 18px" type="icon-yonghu" />
        <span style="margin-left: 10px; margin-right: 8px">{{ userName }}</span>
        <DownOutlined />
      </span>
      <template #overlay>
        <a-menu @click="menuLogout">
          <a-menu-item key="changePass" v-if="(windows as any).g.plat_type === 0">修改密码</a-menu-item>
          <a-menu-item key="logout">退出</a-menu-item>
        </a-menu>
      </template>
    </a-dropdown>
    <a-modal
      v-model:visible="visible"
      title="修改密码"
      @ok="handleOk"
      :maskClosable="false"
      @cancel="afterClose"
      destroyOnClose
    >
      <a-form
        ref="formRef"
        :model="formState"
        name="basic"
        :label-col="{ span: 4 }"
        :wrapper-col="{ span: 18 }"
        autocomplete="off"
        :rules="rules"
      >
        <a-form-item label="旧密码" name="oldPassWord">
          <a-input-password v-model:value="formState.oldPassWord" />
        </a-form-item>

        <a-form-item label="新密码" name="newPassWord">
          <a-input-password v-model:value="formState.newPassWord" />
        </a-form-item>

        <a-form-item name="confirmPassWord" label="确认密码">
          <a-input-password v-model:value="formState.confirmPassWord" />
        </a-form-item>
      </a-form>
    </a-modal>
  </div>
</template>
<script lang="ts">
import { message, Modal } from "ant-design-vue";
import { defineComponent, computed, ref, reactive } from "vue";
import { useStore } from "vuex";
import { DownOutlined } from "@ant-design/icons-vue";
import { useRouter } from "vue-router";
import IconFont from "@/components/c-iconfont";
import { logoutLogin, changePassWord } from "@/api/project";
import type { FormInstance } from "ant-design-vue";
import type { Rule } from "ant-design-vue/es/form";
import md5 from "md5";
import OAuth2 from "@/common/oauth/OAuth2";

export default defineComponent({
  components: {
    IconFont,
    DownOutlined,
  },
  setup() {
    const router = useRouter();
    const store = useStore();
    const userName =
      (window as any).g.plat_type === 0
        ? JSON.parse(window.localStorage.getItem("hollysysIntelligent"))?.username
        : JSON.parse(window.localStorage.getItem("ssoUser"))?.name;

    const menuLogout = ({ key }) => {
      // console.log(key)
      if (key === "logout") {
        Modal.confirm({
          title: "确认退出",
          content: "确定退出吗？",
          onOk() {
            if ((window as any).g.plat_type === 0) {
              logoutLogin().then((res) => {
                if (res.code === 0) {
                  window.localStorage.removeItem("hollysysIntelligent");
                  router.push({ name: "login" });
                }
              });
            } else {
              OAuth2.getInstance().logout();
            }
          },
        });
      }

      if (key === "changePass") {
        visible.value = true;
      }
    };

    // 修改密码
    const visible = ref<boolean>(false);
    const handleOk = () => {
      let timer: any = null;
      formRef.value.validateFields().then(() => {
        // formRef.value.resetFields();
        // visible.value = false;
        const data = {
          newPassword: md5(formState.newPassWord),
          oldPassword: md5(formState.oldPassWord),
        };
        changePassWord(data).then((res) => {
          if (res.code === 0) {
            message.success("更新成功，请重新登陆");
            formRef.value.resetFields();
            visible.value = false;
            timer = setTimeout(() => {
              logoutLogin().then((res) => {
                if (res.code === 0) {
                  window.localStorage.removeItem("hollysysIntelligent");
                  router.push({ name: "login" });
                }
              });
            }, 1000);
          }
        });
      });
    };

    const formRef = ref<FormInstance>(null);
    const formState = reactive({
      oldPassWord: "",
      newPassWord: "",
      confirmPassWord: "",
    });

    let checkOld = async (_rule: Rule, value: string) => {
      if (!value) {
        return Promise.reject("请输入旧密码");
      }
    };
    let checkNew = async (_rule: Rule, value: string) => {
      const regex = /^(?=.*[a-z])(?=.*[A-Z])(?=.*\d)(?=.*[!@#$%^&*()_+{}\[\]:;<>,.?~\\/-]).{6,15}$/;
      if (!value) {
        return Promise.reject("请输入修改密码");
      }
      if (!regex.test(value)) {
        return Promise.reject(
          "密码必须包含大小写字母、数字和特殊字符，长度6-15位"
        );
      }
      // if(!regex.test(value)){
      //   return Promise.reject('长度不超过20个字符');
      // }
      if (value !== formState.confirmPassWord && formState.confirmPassWord) {
        formRef.value.clearValidate("confirmPassWord");
        return Promise.reject("两次密码不一致");
      }
      if (value === formState.oldPassWord) {
        return Promise.reject("新密码不得和旧密码一致");
      }
    };
    let checkConfirm = async (_rule: Rule, value: string) => {
      if (!value) {
        return Promise.reject("请确认密码");
      }

      if (value !== formState.newPassWord) {
        formRef.value.clearValidate("newPassWord");
        return Promise.reject("两次密码不一致");
      }
    };
    const rules: Record<string, Rule[]> = {
      oldPassWord: [{ required: true, validator: checkOld, trigger: "change" }],
      newPassWord: [{ required: true, validator: checkNew, trigger: "change" }],
      confirmPassWord: [{ required: true, validator: checkConfirm, trigger: "change" }],
    };

    const afterClose = () => {
      formRef.value.resetFields();
    };
    const windows = (window as any);
    return {
      userName,
      menuLogout,
      visible,
      handleOk,
      formRef,
      formState,
      rules,
      afterClose,
      windows,
    };
  },
});
</script>

<style scoped>
.userLogout {
  margin-right: 15px;
  font-size: 14px;
  justify-content: flex-end;
}

.inputBox {
  width: 80%;
}
</style>
