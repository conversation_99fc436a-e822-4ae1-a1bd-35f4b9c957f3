<template>
  <div class="content">
    <a-menu mode="horizontal" v-model:selectedKeys="activeName" @click="tabClick">
      <a-menu-item v-for="(item, index) in tabs" :key="item.name">{{ item.label }} </a-menu-item>
    </a-menu>
  </div>
</template>

<script lang="ts">
import { defineComponent, PropType, ref, createVNode } from "vue";
import { ExclamationCircleOutlined } from "@ant-design/icons-vue";
import { useRoute } from "vue-router";
import { useStore } from "vuex";
import { Modal } from "ant-design-vue";
import IconFont from "@/components/c-iconfont";
import { DownOutlined } from "@ant-design/icons-vue";
import MenuLogout from "../components/MenuLogout.vue";

interface ITab {
  children?: any;
  label: string;
  value: string;
  name: string;
}

export default defineComponent({
  components: {
    MenuLogout,
    IconFont,
    DownOutlined,
  },
  props: {
    tabs: {
      type: Array as PropType<ITab[]>,
      default: () => [],
    },
    activeName: {
      type: Array,
      default: [],
    },
  },
  setup(props, ctx) {
    const store = useStore();
    const route = useRoute();
    const tabClick = (val) => {
      ctx.emit("menuClick", val);
    };

    const trigger = ref(false);
    const type = ref("icon-angle-double-up");
    const onTrigger = () => {
      type.value = trigger.value ? "icon-angle-double-up" : "icon-angle-double-down";
      trigger.value = !trigger.value;
      ctx.emit("trigger", trigger.value);
      store.commit("setProjectTigger", trigger.value);
    };

    return {
      tabClick,
      type,
      onTrigger,
    };
  },
});
</script>

<style scoped lang="less">
.content {
  margin-left: auto;
}
.ant-menu-light {
  background: #ffffff00;
  color: #fff;
}

.ant-menu-horizontal {
  line-height: 50px;
}

:deep(.ant-menu-item-selected) {
  color: #fff !important;
}
:deep(.ant-menu-item-selected::after) {
  border-bottom-color: #fff !important;
}

:deep(.ant-menu-item) {
  color: #a6acb2;
}

:deep(.ant-menu-item:hover:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected)) {
  color: #fff;
}

:deep(.ant-menu-item:hover:not(.ant-menu-item-selected):not(.ant-menu-submenu-selected)::after) {
  border-bottom-color: #fff;
}
</style>
