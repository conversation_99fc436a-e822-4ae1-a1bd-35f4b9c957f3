/**
* 配置全局属性
*/
@font-size-base: 16px;

//==========================================横向滚动条样式===================================

::-webkit-scrollbar {
  width: 5px;
  height: 5px;
}

::-webkit-scrollbar-thumb {
  border-radius: 10px;
}

// =========================================覆盖表格默认样式=================================
.ant-table-thead > tr > th {
  height: 50px !important;
  padding: 0px 16px !important;
}

.ant-table-tbody > tr > td {
  height: 50px !important;
  padding: 0px 16px !important;
}

.ant-table-measure-row {
  visibility: collapse;
}

// =========================================覆盖step默认样式=================================

.ant-steps-item-process
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title {
  color: white !important;
}
.ant-steps-item-process
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-description {
  color: white !important;
}

.ant-steps-item-active
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-title {
  color: #1890ff !important;
}

.ant-steps-item-active
  > .ant-steps-item-container
  > .ant-steps-item-content
  > .ant-steps-item-description {
  color: #1890ff !important;
}

.ant-divider {
  border-top-color: #ccc !important;
  margin: 10px 0;
}

// =========================================覆盖antd-modal默认样式=================================
.ant-modal-content {
  padding: 0px 0px 12px 0px !important;
}
.ant-modal-header {
  height: 44px !important;
  background: var(--primary-color-title) !important;
  padding: 10px 24px !important;
  border-radius: 2px 2px 0px 0px !important;
  margin-bottom: 10px !important;
}
.ant-modal-title {
  color: #fff !important;
}
.ant-modal-close {
  top: 12px !important;
  color: #fff !important;
}
.ant-modal-body {
  padding: 20px 24px 10px 24px !important;
}

.ant-modal-footer {
  padding: 0px 24px !important;
}

// ===========================================   覆盖antd-button默认样式   =====================

.ant-btn-sm {
  border-radius: 2px !important;
}

// =========================================覆盖antd-tabs默认样式==================================

// tabs选中文字颜色
// .ant-tabs .ant-tabs-tab.ant-tabs-tab-active .ant-tabs-tab-btn {
//   color: var(--tab-active-color) !important;
// }
// // tabs未选中文字颜色
// .ant-tabs-tab {
//   color: var(--textColor1);
//   border-radius: 0px !important;
// }
// .ant-tabs-tab-active {
//   background: var(--tab-active-bg-color) !important;
//   border-radius: 0px !important;
// }
// .ant-tabs-nav-wrap {
//   background: var(--areaColor1);
// }
// .ant-tabs-tab-active{
//   background: var(--areaColor2) !important;
//   border-bottom: 2px solid !important;
// }
// =========================================覆盖ant-message默认样式==================================
.ant-message-notice {
  border-radius: 2px !important;
  margin-top: 60px;
}


// =========================================覆盖ant-tree默认样式===================================
// .ant-tree .ant-tree-node-content-wrapper.ant-tree-node-selected {
//   background: var(--areaColor2);
//   // color: var(--activeTextColor);
//   border-radius: 50px 0px 0px 50px !important;
// }

// .ant-tree-title {
//   color: var(--textColor3);
//   font-size: 14px;
// }

// .ant-tree-switcher {
//   width: 15px !important;
// }

// .ant-tree .ant-tree-node-content-wrapper:hover .ant-tree-title {
//   background: var(--areaColor2);
// }

// // .ant-tree .ant-tree-node-content-wrapper {
// //   line-height: 30px;
// //   user-select: none;
// // }
// .ant-tree-treenode{
//   height: 42px !important;
// }
// .ant-tree-indent-unit {
//   width: 15px !important;
// }

// =========================================覆盖ant-menu默认样式===================================

// .ant-menu .ant-menu-item-selected {
//   border-radius: 4px;
//   background: var(--areaColor2);
// }

// .ant-menu-item:not(.ant-menu-item-selected):hover {
//   background: var(--areaColor2);
//   border-radius: 4px;
// }

.ant-form-item {
  margin-bottom: 12px;
}
