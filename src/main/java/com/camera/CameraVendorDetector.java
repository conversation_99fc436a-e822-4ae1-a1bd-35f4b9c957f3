package com.camera;

import java.io.*;
import java.net.*;
import java.nio.charset.StandardCharsets;
import java.util.*;
import java.util.concurrent.*;
import java.util.regex.Pattern;
import javax.net.ssl.*;
import java.security.cert.X509Certificate;

/**
 * 通用摄像头厂商识别器
 * 支持识别宇视(Uniview)、海康威视(Hikvision)、大华(Dahua)等主流厂商
 * 
 * 识别原理：
 * 1. HTTP响应头分析 - 检查Server、WWW-Authenticate等头信息
 * 2. 设备信息接口 - 访问标准设备信息API
 * 3. Web页面特征 - 分析登录页面HTML特征
 * 4. RTSP流信息 - 检查RTSP服务器信息
 */
public class CameraVendorDetector {
    
    public enum CameraVendor {
        HIKVISION("海康威视", "Hikvision"),
        DAHUA("大华", "Dahua"), 
        UNIVIEW("宇视", "Uniview"),
        UNKNOWN("未知", "Unknown");
        
        private final String chineseName;
        private final String englishName;
        
        CameraVendor(String chineseName, String englishName) {
            this.chineseName = chineseName;
            this.englishName = englishName;
        }
        
        public String getChineseName() { return chineseName; }
        public String getEnglishName() { return englishName; }
    }
    
    public static class DetectionResult {
        private final CameraVendor vendor;
        private final String method;
        private final String details;
        private final int confidence; // 置信度 0-100
        
        public DetectionResult(CameraVendor vendor, String method, String details, int confidence) {
            this.vendor = vendor;
            this.method = method;
            this.details = details;
            this.confidence = confidence;
        }
        
        // Getters
        public CameraVendor getVendor() { return vendor; }
        public String getMethod() { return method; }
        public String getDetails() { return details; }
        public int getConfidence() { return confidence; }
        
        @Override
        public String toString() {
            return String.format("厂商: %s (%s), 识别方法: %s, 置信度: %d%%, 详情: %s",
                vendor.getChineseName(), vendor.getEnglishName(), method, confidence, details);
        }
    }
    
    private static final int TIMEOUT = 5000; // 5秒超时
    private static final String USER_AGENT = "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36";
    
    // 厂商特征模式
    private static final Map<CameraVendor, List<Pattern>> VENDOR_PATTERNS = new HashMap<>();
    
    static {
        // 海康威视特征
        VENDOR_PATTERNS.put(CameraVendor.HIKVISION, Arrays.asList(
            Pattern.compile("hikvision", Pattern.CASE_INSENSITIVE),
            Pattern.compile("hik-connect", Pattern.CASE_INSENSITIVE),
            Pattern.compile("ivms", Pattern.CASE_INSENSITIVE),
            Pattern.compile("sadp", Pattern.CASE_INSENSITIVE),
            Pattern.compile("webcomponents", Pattern.CASE_INSENSITIVE)
        ));
        
        // 大华特征
        VENDOR_PATTERNS.put(CameraVendor.DAHUA, Arrays.asList(
            Pattern.compile("dahua", Pattern.CASE_INSENSITIVE),
            Pattern.compile("dh-", Pattern.CASE_INSENSITIVE),
            Pattern.compile("configmanager", Pattern.CASE_INSENSITIVE),
            Pattern.compile("smartpss", Pattern.CASE_INSENSITIVE)
        ));
        
        // 宇视特征
        VENDOR_PATTERNS.put(CameraVendor.UNIVIEW, Arrays.asList(
            Pattern.compile("uniview", Pattern.CASE_INSENSITIVE),
            Pattern.compile("unv", Pattern.CASE_INSENSITIVE),
            Pattern.compile("eztools", Pattern.CASE_INSENSITIVE),
            Pattern.compile("ezstation", Pattern.CASE_INSENSITIVE)
        ));
    }
    
    public CameraVendorDetector() {
        // 禁用SSL证书验证（用于测试环境）
        disableSSLVerification();
    }
    
    /**
     * 检测摄像头厂商
     */
    public DetectionResult detectVendor(String ip, String username, String password) {
        List<DetectionResult> results = new ArrayList<>();
        
        // 方法1: HTTP响应头分析
        DetectionResult headerResult = detectByHttpHeaders(ip);
        if (headerResult.getVendor() != CameraVendor.UNKNOWN) {
            results.add(headerResult);
        }
        
        // 方法2: 设备信息接口
        DetectionResult apiResult = detectByDeviceAPI(ip, username, password);
        if (apiResult.getVendor() != CameraVendor.UNKNOWN) {
            results.add(apiResult);
        }
        
        // 方法3: Web页面特征
        DetectionResult webResult = detectByWebFeatures(ip);
        if (webResult.getVendor() != CameraVendor.UNKNOWN) {
            results.add(webResult);
        }
        
        // 方法4: RTSP信息
        DetectionResult rtspResult = detectByRTSP(ip, username, password);
        if (rtspResult.getVendor() != CameraVendor.UNKNOWN) {
            results.add(rtspResult);
        }
        
        // 选择置信度最高的结果
        return results.stream()
            .max(Comparator.comparingInt(DetectionResult::getConfidence))
            .orElse(new DetectionResult(CameraVendor.UNKNOWN, "综合检测", "无法识别厂商", 0));
    }
    
    /**
     * 方法1: 通过HTTP响应头检测
     */
    private DetectionResult detectByHttpHeaders(String ip) {
        try {
            URL url = new URL("http://" + ip);
            HttpURLConnection conn = (HttpURLConnection) url.openConnection();
            conn.setRequestMethod("GET");
            conn.setConnectTimeout(TIMEOUT);
            conn.setReadTimeout(TIMEOUT);
            conn.setRequestProperty("User-Agent", USER_AGENT);
            
            // 获取响应头
            Map<String, List<String>> headers = conn.getHeaderFields();
            String serverHeader = conn.getHeaderField("Server");
            String wwwAuth = conn.getHeaderField("WWW-Authenticate");
            
            StringBuilder headerInfo = new StringBuilder();
            if (serverHeader != null) headerInfo.append("Server: ").append(serverHeader).append(" ");
            if (wwwAuth != null) headerInfo.append("Auth: ").append(wwwAuth).append(" ");
            
            String allHeaders = headerInfo.toString().toLowerCase();
            
            // 检查海康威视特征
            if (allHeaders.contains("hikvision") || allHeaders.contains("webserver")) {
                return new DetectionResult(CameraVendor.HIKVISION, "HTTP响应头", 
                    "检测到海康威视特征: " + headerInfo.toString(), 85);
            }
            
            // 检查大华特征
            if (allHeaders.contains("dahua") || allHeaders.contains("dh-")) {
                return new DetectionResult(CameraVendor.DAHUA, "HTTP响应头", 
                    "检测到大华特征: " + headerInfo.toString(), 85);
            }
            
            // 检查宇视特征
            if (allHeaders.contains("uniview") || allHeaders.contains("unv")) {
                return new DetectionResult(CameraVendor.UNIVIEW, "HTTP响应头", 
                    "检测到宇视特征: " + headerInfo.toString(), 85);
            }
            
        } catch (Exception e) {
            // 忽略异常，继续其他检测方法
        }
        
        return new DetectionResult(CameraVendor.UNKNOWN, "HTTP响应头", "未检测到厂商特征", 0);
    }
    
    /**
     * 方法2: 通过设备信息API检测
     */
    private DetectionResult detectByDeviceAPI(String ip, String username, String password) {
        // 常见的设备信息API端点
        String[] apiEndpoints = {
            "/ISAPI/System/deviceInfo",           // 海康威视
            "/cgi-bin/magicBox.cgi?action=getDeviceType", // 大华
            "/api/v1/system/deviceinfo",          // 宇视
            "/cgi-bin/hi3510/param.cgi?cmd=getserverinfo", // 通用
        };
        
        for (String endpoint : apiEndpoints) {
            try {
                String response = makeAuthenticatedRequest(ip, endpoint, username, password);
                if (response != null && !response.isEmpty()) {
                    CameraVendor vendor = analyzeDeviceInfoResponse(response);
                    if (vendor != CameraVendor.UNKNOWN) {
                        return new DetectionResult(vendor, "设备信息API", 
                            "通过API端点 " + endpoint + " 识别", 90);
                    }
                }
            } catch (Exception e) {
                // 继续尝试下一个端点
            }
        }
        
        return new DetectionResult(CameraVendor.UNKNOWN, "设备信息API", "API访问失败", 0);
    }
    
    /**
     * 方法3: 通过Web页面特征检测
     */
    private DetectionResult detectByWebFeatures(String ip) {
        try {
            String htmlContent = getWebPageContent(ip);
            if (htmlContent == null) return new DetectionResult(CameraVendor.UNKNOWN, "Web页面特征", "无法获取页面内容", 0);
            
            String lowerContent = htmlContent.toLowerCase();
            
            // 海康威视特征检测
            if (lowerContent.contains("hikvision") || 
                lowerContent.contains("ivms") ||
                lowerContent.contains("webcomponents") ||
                lowerContent.contains("hik-connect")) {
                return new DetectionResult(CameraVendor.HIKVISION, "Web页面特征", 
                    "检测到海康威视页面特征", 80);
            }
            
            // 大华特征检测
            if (lowerContent.contains("dahua") || 
                lowerContent.contains("configmanager") ||
                lowerContent.contains("smartpss") ||
                lowerContent.contains("dh-")) {
                return new DetectionResult(CameraVendor.DAHUA, "Web页面特征", 
                    "检测到大华页面特征", 80);
            }
            
            // 宇视特征检测
            if (lowerContent.contains("uniview") || 
                lowerContent.contains("eztools") ||
                lowerContent.contains("ezstation") ||
                lowerContent.contains("unv")) {
                return new DetectionResult(CameraVendor.UNIVIEW, "Web页面特征", 
                    "检测到宇视页面特征", 80);
            }
            
        } catch (Exception e) {
            // 忽略异常
        }
        
        return new DetectionResult(CameraVendor.UNKNOWN, "Web页面特征", "未检测到厂商特征", 0);
    }
    
    /**
     * 方法4: 通过RTSP信息检测
     */
    private DetectionResult detectByRTSP(String ip, String username, String password) {
        // RTSP端口通常是554
        String[] rtspUrls = {
            "rtsp://" + username + ":" + password + "@" + ip + ":554/Streaming/Channels/101", // 海康威视
            "rtsp://" + username + ":" + password + "@" + ip + ":554/cam/realmonitor?channel=1&subtype=0", // 大华
            "rtsp://" + username + ":" + password + "@" + ip + ":554/video1", // 宇视
        };
        
        for (String rtspUrl : rtspUrls) {
            try {
                // 这里简化处理，实际应该使用RTSP客户端库
                // 可以通过Socket连接检查RTSP服务器响应
                String serverInfo = checkRTSPServer(ip, 554);
                if (serverInfo != null && !serverInfo.isEmpty()) {
                    CameraVendor vendor = analyzeRTSPResponse(serverInfo);
                    if (vendor != CameraVendor.UNKNOWN) {
                        return new DetectionResult(vendor, "RTSP信息", 
                            "RTSP服务器信息: " + serverInfo, 75);
                    }
                }
            } catch (Exception e) {
                // 继续尝试
            }
        }
        
        return new DetectionResult(CameraVendor.UNKNOWN, "RTSP信息", "RTSP检测失败", 0);
    }
